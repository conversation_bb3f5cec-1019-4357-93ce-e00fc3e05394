# Commit Msg Rule

format examples:

🦄 feat: <short description of the change>
🐛 bugfix: <short description of> the bug
🎨 style: <short description of the style change>
🚀 perf: <short description of the performance improvement>
📝 docs: <short description of the documentation change>
🔧 chore: <short description of the chore>
♻️ refactor: <short description of the refactoring>
🔒 security: <short description of the security fix>
🔖 release: <version number>
[customized-emoji] keyword(module): <short description of the change>

---

- keep the commit short and brief in less than 70 words
- only with oneline to describe the change no extra line
- make sure use `to #xxxxxxx` to related issue or PR on demand, you can check back the last commit message as reference at the end of the line
