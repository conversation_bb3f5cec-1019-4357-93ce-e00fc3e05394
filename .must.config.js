// more config: https://gitlab.alibaba-inc.com/parrot/parrot-tool-must/blob/master/doc/config.md
module.exports = {
  extract: {
    name: 'yida-manus',
    sourcePath: 'src',
    fileType: 'ts',
    prettier: true,
    macro: {
      path: 'src/i18n',
      method: 'i18n("$key$")',
      import: 'import i18n from "src/i18n"',
      keySplitter: '.',
      placeholder: (variable) => {
        return `{${variable}}`;
      },
      dependencies: ['@ali/global-locale', '@ali/global-string-format'],
    },
    babel: {
      allowImportExportEverywhere: true,
      decoratorsBeforeExport: true,
      plugins: [
        'asyncGenerators',
        'classProperties',
        'decorators-legacy',
        'doExpressions',
        'exportExtensions',
        'exportDefaultFrom',
        'typescript',
        'functionSent',
        'functionBind',
        'jsx',
        'objectRestSpread',
        'dynamicImport',
        'numericSeparator',
        'optionalChaining',
        'optionalCatchBinding',
      ],
    },
    isNeedUploadCopyToMedusa: true,
    sourceLang: 'zh-CN',
  },
};
