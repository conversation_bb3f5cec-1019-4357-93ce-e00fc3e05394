# yida-manus 智能应用生成平台

## 项目概述

yida-manus 是一个基于 React 和 TypeScript 开发的智能应用生成平台，通过 Agent 系统和丰富的工具集，实现应用的智能化生成。平台能够根据用户需求，自动规划、设计和生成完整的应用，包括表单、页面、数据模型等核心组件。

### 核心特性

- **智能应用生成**：通过 AI 技术，根据用户描述自动生成完整应用
- **多种 Agent 支持**：提供 PlanningAgent、CoderAgent 等多种智能 Agent
- **丰富的工具系统**：包含应用创建、表单设计、代码生成等多种工具
- **完整的应用生命周期管理**：从需求分析到应用生成的全流程支持

## 技术栈

- **前端框架**：React 18
- **开发语言**：TypeScript
- **状态管理**：Zustand
- **UI 组件库**：Antd 5 和 @alifd/next
- **网络请求**：Axios
- **工程化工具**：ESLint, Prettier, <PERSON><PERSON>, <PERSON><PERSON>
- **测试框架**：Jest

## 项目结构

```
src/
├── agents/                        # Agent 配置与实现
│   ├── appAgent.ts                # 应用生成Agent
│   ├── appQuickAgent.ts           # 快速应用生成Agent
│   ├── chatbotAgent.ts            # 聊天机器人Agent
│   ├── headlessCmsAgent.ts        # 无头CMS Agent
│   ├── index.ts                   # Agent导出文件
│   ├── lotteryAgent.ts            # 抽奖应用Agent
│   ├── normalAgent.ts             # 标准Agent
│   ├── pageAgent.ts               # 页面生成Agent
│   └── surveyAgent.ts             # 调查问卷Agent
├── apis/                          # API 接口定义与实现
│   ├── cardCRUD.ts                # 卡片增删改查API
│   ├── createAIAssistant.ts       # 创建AI助手API
│   ├── createComponent.ts         # 创建组件API
│   ├── createFormInstance.ts      # 创建表单实例API
│   ├── createOrUpdateEntitySkill.ts # 创建/更新实体技能API
│   ├── createPage.ts              # 创建页面API
│   ├── getAppIncludingAecpInfo.ts # 获取应用信息API
│   ├── getAppList.ts              # 获取应用列表API
│   ├── getComponentSchema.ts      # 获取组件Schema API
│   ├── getCubeFieldList.ts        # 获取数据模型字段列表API
│   ├── getFormNavigationListByOrder.ts # 获取表单导航列表API
│   ├── getFormSchema.ts           # 获取表单Schema API
│   ├── index.ts                   # API导出文件
│   ├── listAgentEntitySkills.ts   # 列出Agent实体技能API
│   ├── registerApp.ts             # 注册应用API
│   ├── saveComponentSchema.ts     # 保存组件Schema API
│   ├── saveFormSchema.ts          # 保存表单Schema API
│   ├── saveFormSchemaInfo.ts      # 保存表单Schema信息API
│   ├── searchCubeList.ts          # 搜索数据模型列表API
│   ├── searchDepartmentList.ts    # 搜索部门列表API
│   ├── searchEmployeeList.ts      # 搜索员工列表API
│   ├── triggerTxtToImg.ts         # 触发文本转图片API
│   └── updateApp.ts               # 更新应用API
├── components/                    # 通用组件
│   ├── AppCard/                   # 应用卡片组件
│   │   ├── index.less             # 样式文件
│   │   └── index.tsx              # 组件实现
│   ├── AppMentions/               # 应用提及组件
│   │   ├── demo.tsx               # 示例文件
│   │   ├── index.less             # 样式文件
│   │   └── index.tsx              # 组件实现
│   ├── Artifacts/                 # 工件组件
│   │   ├── errorSection.tsx       # 错误部分组件
│   │   ├── index.less             # 样式文件
│   │   └── index.tsx              # 组件实现
│   ├── ChooseTemlate/             # 选择模板组件
│   │   ├── index.less             # 样式文件
│   │   ├── index.tsx              # 组件实现
│   │   └── template.ts            # 模板定义
│   ├── Code/                      # 代码组件
│   │   ├── code-editor.tsx        # 代码编辑器
│   │   └── shared.tsx             # 共享组件
│   ├── Logger/                    # 日志组件
│   │   └── index.tsx              # 组件实现
│   ├── SettingDialog/             # 设置对话框组件
│   │   └── index.tsx              # 组件实现
│   └── UploadFile/               # 上传图片组件
├── config/                        # 配置文件
│   ├── defaultCardSchema.ts       # 默认卡片Schema
│   ├── defaultDataModuleSchema.ts # 默认数据模块Schema
│   └── index.ts                   # 配置导出文件
├── core/                          # 核心实现（Agent 基类等）
│   ├── BaseAgent.ts               # Agent基类
│   ├── CoderAgent.ts              # 代码生成Agent
│   ├── FactoryAgent.ts            # Agent工厂
│   ├── ManusAgent.ts              # Manus Agent
│   ├── PlanningAgent.ts           # 规划Agent
│   └── PrintCoderAgent.ts         # 打印代码Agent
├── docs/                          # 文档目录
├── pages/                         # 页面组件
│   ├── home/                      # 首页
│   │   ├── index.less             # 样式文件
│   │   └── index.tsx              # 页面实现
│   ├── mobile/                    # 移动端页面
│   │   ├── apis.ts                # API定义
│   │   ├── feedback.ts            # 反馈功能
│   │   └── index.ts               # 入口文件
│   ├── preview/                   # 预览页面
│   │   ├── index.scss             # 样式文件
│   │   └── index.tsx              # 页面实现
│   ├── index.scss                 # 主样式文件
│   ├── index.tsx                  # 主入口文件
│   └── temp.less                  # 临时样式文件
├── plugins/                       # 插件
│   └── managerPlugins.ts          # 管理插件
├── services/                      # 服务层
│   ├── llmService.ts              # LLM服务
│   └── mockService.ts             # 模拟服务
├── store/                         # 状态管理
│   ├── aiAgenticStore.ts          # Agent状态管理
│   ├── aiArtifactStore.ts         # AI生成资源状态管理
│   ├── aiChatStore.ts             # AI聊天状态管理
│   ├── logMiddleWare.ts           # 日志中间件
│   └── zustand.ts                 # Zustand配置
├── tools/                         # 工具实现
│   ├── CreateAIAssistantTool/     # 创建AI助手工具
│   ├── CreateAppIconTool/         # 创建应用图标工具
│   ├── CreateAppTool/             # 创建应用工具
│   ├── CreateFormTool/            # 创建表单工具
│   ├── CreateHomePageTool/        # 创建首页工具
│   ├── CreatePageTool/            # 创建页面工具
│   ├── GenerateBizPRDTool/        # 生成业务PRD工具
│   ├── GenerateCodeTool/          # 生成代码工具
│   ├── GenerateDataManagePageTool/ # 生成数据管理页面工具
│   ├── GenerateImageTool/         # 生成图片工具
│   ├── GenerateInterfaceTool/     # 生成接口工具
│   ├── GenerateMockDataTool/      # 生成模拟数据工具
│   ├── GeneratePRDTool/           # 生成PRD工具
│   ├── GetCubesInfoTool/          # 获取数据模型信息工具
│   ├── MessageTools/              # 消息工具
│   ├── OtherTool/                 # 其他工具
│   ├── PlanningTool/              # 规划工具
│   ├── PreviewPageTool/           # 页面预览工具
│   ├── ReadImageTool/             # 读取图片工具
│   ├── SetFormSkillsTool/         # 设置表单技能工具
│   ├── StepCompleteTool/          # 步骤完成工具
│   ├── TargetPageTool/            # 目标页面工具
│   ├── TerminateTool/             # 终止工具
│   ├── UpdateAppSettingTool/      # 更新应用设置工具
│   ├── UpdateFormTool/            # 更新表单工具
│   └── WebSearchTool/             # 网络搜索工具
├── types/                         # 类型定义
│   ├── babel.d.ts                 # Babel类型定义
│   └── types.ts                   # 通用类型定义
└── utils/                         # 工具函数
    ├── ChatManager.ts             # 聊天管理器
    ├── Errors.ts                  # 错误处理
    ├── Form.ts                    # 表单工具
    ├── FormGenerator.ts           # 表单生成器
    ├── ImageSaver.ts              # 图片保存工具
    ├── LangUtils.ts               # 语言工具
    ├── Logger.ts                  # 日志工具
    ├── Memory.ts                  # 内存管理
    ├── Message.ts                 # 消息处理
    ├── Tool.ts                    # 工具基类
    ├── ToolCollection.ts          # 工具集合
    ├── UpdatePrinterEffect.ts     # 更新打印效果
    ├── UploadFile.ts             # 上传图片工具
    ├── UserFeedbackLogger.ts      # 用户反馈日志
    ├── Validator.ts               # 验证工具
    ├── WebC.ts                    # Web组件工具
    └── YidaMeta.ts                # Yida元数据工具

## 核心模块说明

### Agent 系统

Agent 系统是平台的核心，采用工厂模式设计，支持多种类型的 Agent：

- **BaseAgent**：提供基础能力和接口
- **PlanningAgent**：负责任务规划与分解
- **CoderAgent**：专注于代码生成
- **LotteryAgent**：应用生成专用 Agent

Agent 系统通过提示词工程和上下文管理，实现智能化的应用生成流程。

### 工具系统

工具系统提供丰富的功能模块，主要包括：

- **应用创建工具**：CreateAppTool
- **表单创建工具**：CreateFormTool
- **代码生成工具**：GenerateCodeTool
- **PRD 生成工具**：GeneratePRDTool
- **模拟数据生成工具**：GenerateMockDataTool

工具系统采用可扩展设计，支持自定义工具的开发和集成。

### 状态管理

采用 Zustand 进行状态管理，主要包括：

- **aiArtifactStore**：管理 AI 生成的资源
- **aiChatStore**：管理对话状态和历史
- **aiAgenticStore**：管理 Agent 状态和配置

## 快速开始

### 环境要求

- Node.js >= 16
- Yarn 或 npm

### 安装依赖

```bash
# 推荐使用 yarn 安装依赖
yarn
```

### 开发模式

```bash
yarn start
```

### 构建项目

```bash
yarn build
```

## 开发指南

### 添加新的 Agent

1. 在 `src/agents` 目录下创建新的 Agent 配置文件
2. 实现 AgentConfig 接口
3. 在 `src/agents/index.ts` 中导出新 Agent

### 添加新的工具

1. 在 `src/tools` 目录下创建新的工具目录
2. 实现工具类
3. 在 `src/tools/index.ts` 中导出新工具

## 项目规范

* 文件目录命名，使用小驼峰命名
* 遵循 TypeScript 类型定义规范
* 使用 ESLint 和 Prettier 进行代码格式化
* 提交前运行 lint-staged 进行代码检查


## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

MIT
