{".babelrc": "{\n  \"presets\": [\n    \"@babel/preset-env\",\n    [\"@babel/preset-react\", {\"runtime\": \"automatic\"}]\n  ]\n} ", "cli.js": "const fs = require(\"fs\");\nconst { parse } =  require(\"@babel/parser\");\nconst { default: traverse } = require(\"@babel/traverse\");\n\nconst run = () => {\n  const modules = [];\n  const code = fs.readFileSync(\"./src/YidaComp.jsx\", \"utf-8\");\n  const ast = parse(code, {\n    sourceType: \"module\",\n    plugins: [\"jsx\"],\n  });\n  traverse(ast, {\n    ImportDeclaration: (path) => {\n      modules.push(path.node.source.value);\n    },\n  });\n  fs.writeFileSync(\"./dist/modules.json\", JSON.stringify(modules, null, 2));\n};\n\nrun();\n\n", "package.json": "{\n  \"name\": \"react-webpack-demo\",\n  \"version\": \"1.0.0\",\n  \"description\": \"React Webpack Demo\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"start\": \"webpack serve --mode development --open\",\n    \"parse\": \"node cli.js\",\n    \"build\": \"webpack --mode production && npm run parse\",\n    \"test\": \"echo \\\"Error: no test specified\\\" && exit 1\"\n  },\n  \"dependencies\": {\n    \"react\": \"18\"\n  },\n  \"devDependencies\": {\n    \"@babel/core\": \"^7.23.9\",\n    \"@babel/preset-env\": \"^7.23.9\",\n    \"@babel/preset-react\": \"^7.23.9\",\n    \"@babel/parser\": \"^7.22.1\",\n    \"@babel/preset-typescript\": \"^7.26.0\",\n    \"@babel/traverse\": \"^7.22.1\",\n    \"autoprefixer\": \"^10.4.17\",\n    \"babel-loader\": \"^9.1.3\",\n    \"css-loader\": \"^6.10.0\",\n    \"postcss\": \"^8.4.35\",\n    \"postcss-loader\": \"^8.1.0\",\n    \"style-loader\": \"^3.3.4\",\n    \"tailwindcss\": \"^3.4.1\",\n    \"webpack\": \"^5.90.1\",\n    \"webpack-cli\": \"^5.1.4\",\n    \"webpack-dev-server\": \"^5.0.1\"\n  }\n} ", "postcss.config.js": "module.exports = {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n} ", "src": {"YidaComp.jsx": "", "index.css": "@tailwind base;\n@tailwind components;\n@tailwind utilities; "}, "tailwind.config.js": "/** @type {import('tailwindcss').Config} */\nmodule.exports = {\n  content: [\n    \"./src/**/*.{js,jsx}\",\n    \"./public/index.html\",\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n} ", "webpack.config.js": "const path = require('path');\n// const HtmlWebpackPlugin = require('html-webpack-plugin');\n\nmodule.exports = {\n  entry: {\n    YidaComp: ['./src/YidaComp.jsx', './src/index.css'],\n  },\n  output: {\n    path: path.resolve(__dirname, 'dist'),\n    filename: '[name].js',\n  },\n  optimization: {\n    usedExports: false,\n    sideEffects: false\n  },\n  externals: {\n    'react': 'React',\n    'react-dom': 'ReactDOM',\n    'react-router-dom': 'ReactRouterDOM',\n    'antd': 'antd',\n    'ahooks': 'ahooks',\n    'd3': 'd3',\n    'lodash': '_',\n    'dayjs': 'dayjs',\n    '@ant-design/icons': 'icons',\n    'recharts': 'Recharts',\n    'lucide-react': 'DynamicIcon',\n    'yida-plugin-markdown': 'YidaMarkdown',\n    '@radix-ui/themes': 'Radix',\n    'framer-motion': 'FramerMotion',\n    '@antv/g2': 'G2'\n  },\n  module: {\n    rules: [\n      {\n        test: /\\.(js|jsx)$/,\n        exclude: /node_modules/,\n        use: {\n          loader: 'babel-loader',\n        },\n      },\n      {\n        test: /\\.css$/,\n        use: ['style-loader', 'css-loader', 'postcss-loader'],\n      },\n      {\n        test: /\\.(png|svg|jpg|jpeg|gif)$/i,\n        type: 'asset/resource',\n      },\n    ],\n  },\n  resolve: {\n    extensions: ['.js', '.jsx', '.mjs'],\n  },\n  plugins: [\n    // new HtmlWebpackPlugin({\n    //   template: './public/index.html',\n    // }),\n  ],\n  devServer: {\n    // static: {\n    //   directory: path.join(__dirname, 'public'),\n    // },\n    port: 3300,\n    hot: true,\n    open: true,\n  },\n}; "}