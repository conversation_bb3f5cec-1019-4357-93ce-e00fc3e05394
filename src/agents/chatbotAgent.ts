import { AgentConfig } from '@/core/FactoryAgent';

export * from './normalAppAgent';

const name = 'chatbot_agent';
const nameCn = '智能对话';
const description = '一个能够使用多种工具的通用智能代理。';
const welcomeMessage =
  '👋 你好！我是CodeGen 通用智能助理，我可以帮助你完成各种多步骤复杂任务，请告诉我你需要什么帮助？';
// 系统提示词
const systemPrompt = `你是CodeGen 通用智能助理。
你可以完成普通的对话，也可以使用多种工具来完成我的目标。
始终仔细思考是直接进行普通对话，还是使用多种工具来完成我的目标。
在使用工具时，请确保：
1. 清晰理解任务需求
2. 选择最合适的工具
3. 按照逻辑顺序执行工具
4. 优雅地处理错误
5. 清晰地解释你的行动
6. 默认使用中文语言进行回答
7. 工具使用返回 json 格式
`;

// 下一步提示词
const nextStepPrompt = '';

export const ManusChatBotConfig: AgentConfig = {
  name,
  nameCn,
  description,
  welcomeMessage,
  agentType: 'BaseAgent',
  systemPrompt,
  nextStepPrompt,
  agents: ['NormalAppConfig'],
  tools: [
    'WebSearchTool',
    'UpdateCodeTool',
    'GenerateCodeTool',
    // 'UpdateFormTool',
    'ReadImageTool',
    'GenerateImageTool',
  ],
};

export const AppQuickChatBotConfig: AgentConfig = {
  name,
  nameCn,
  description,
  welcomeMessage,
  agentType: 'BaseAgent',
  systemPrompt,
  nextStepPrompt,
  agents: ['AppAgentConfig'],
  tools: ['WebSearchTool', 'GenerateImageTool', 'ReadImageTool', 'GenerateImageTool'],
};

export const ToolBotConfig: AgentConfig = {
  name,
  nameCn,
  description,
  welcomeMessage,
  agentType: 'BaseAgent',
  systemPrompt,
  nextStepPrompt,
  agents: [],
  tools: ['ExcelReaderTool'],
};
