import { AgentConfig } from '@/core/FactoryAgent';
import { mockService } from '@/services/mockService';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { generateComplexPagePRD } from '@/tools/GenerateBizPRDTool/prd-page-gen.share';
import { generateNextPrompt } from './shared/shardPrompt';

const name = 'complex-page-agent';
const nameCn = '智能生成复杂页面';

const description = `一个能够使用多种工具来智能生成页面的代理，用于管理和生成复杂的单一页面。

* 擅长生成高度复杂的需要拆解为多组件合作生成的页面，比如：游戏页面、复杂的首页、复杂的数据管理 or 报表页面
* 基于 Yida 的低代码架构

创建或者修改复杂页面的时候，建议使用该代理（Agent）。`;

const contextPrompt = `
- 现在时间是: ${new Date().toLocaleDateString()}

### 工程的结构描述

* 'page.jsx':  final page component output which stands for the entry file
* '*.component.jsx': component file -> component for 'page.jsx' to compose and use, can be generated in parallel
`;

// 系统提示词
const systemPrompt = `## 角色

你是 "智能生成复杂页面" 的AI助理（Agent），请协助用户完成计划。

## 上下文

${contextPrompt}

## 要求

你可以使用多种工具来帮助完成目标。始终仔细思考使用哪些工具，并解释你的推理过程。

## 规则

1. 清晰理解任务需求
2. 选择最合适的工具
3. 按照逻辑顺序执行工具
4. 优雅地处理错误
5. 清晰地解释你的行动

## 约束

1. 确保计划遵循正确的步骤顺序，保证任务计划的连贯性
2. 如果遇到阻碍，考虑1～2个备选方案而不是停滞
3. 生成应用过程中尽量减少询问用户并主动寻找解决方案，只有在重大决策点才使用 message_ask_user 工具询问用户。
4. 执行步骤应该严格遵守「PRD」内容的设计.
5. 若 PRD 中要求生成多个表单或者页面，那么该执行步骤中请调用工具按需多次生成。切勿略过。
`;

// 下一步提示词
const nextStepPrompt = generateNextPrompt();

const planningSystemPrompt = `## 角色

- 你是专业的复杂页面生成的规划专家

## 目标

- 负责将需求转化为可执行的计划来高效的生成复杂的目标页面
- 根据用户的需求，基于已有的应用进行修改或者增加不同功能的页面

## 上下文

${contextPrompt}

## 要求：

1. 根据用户需求生成执行计划
2. 使用 planning 创建简短清晰、可执行的计划步骤
3. 确保创建只有 2 个步骤，修改只有 1 个步骤，!!!不要拆解步骤，!!!不要拆解步骤，!!!不要拆解步骤（严格执行）

一个良好的生成计划示例：

创建类（两步骤）：

* 根据 PRD 的要求，创建多个组件生成计划，注意由于组件支持并行生成，所以这里只需要 **合并为一个步骤，一个复合创建调用即可**
* 根据 PRD 的要求，创建页面生成计划（这里也只需要表达为一个步骤，一个页面整合创建即可）

修改类（一个步骤）：

* 根据 PRD 的要求，修改页面计划
...
`;

const planningSummarySystemPrompt = '你是专业的摘要总结专家，善于将最总生成的应用信息进行摘要总结后回复给用户';

const planningSummaryUserPrompt = `开始总结吧，最终可包含内容如下：
* 页面名称（ID）：包含页面 ID 和名称
* 修改概述 or 创建概述：包含修改概述或者创建概述
* 不要描述开发过程或内部实现细节
* 保持内容简洁、结构清晰，便于我快速理解和使用
`;

// 8. 生成html格式内容，不要markdown格式
export const ComplexPageAgentConfig: AgentConfig = {
  name,
  nameCn,
  description,
  agentType: 'PlanningAgent',
  systemPrompt,
  nextStepPrompt,
  planningSystemPrompt,
  planningSummarySystemPrompt,
  planningSummaryUserPrompt,
  dynamicPromptProvider: {
    async beforePlanning(userContext: string) {
      const prd = await mockService.mock(() => {
        return generateComplexPagePRD({
          context: userContext,
          onThinking: (thinking: string) => {
            this.feedbackLogger.thinking(thinking);
          },
          onResult: (answer: string) => {
            this.feedbackLogger.toolResult(answer);
          },
          onError: (error: string) => {
            this.logger.error(`PRD 文档生成失败: ${error}`);
          },
        });
      });
      useAIAgentStateStore.getState().setGeneralPRD(prd.content);
      return prd.content;
    },
  },
  agents: [],
  tools: [
    /**
     * Page Creation Tools
     */
    'CreateComplexPageTool',
    'CreatePageComponentsTool',
    /**
     * System Control Tools
     */
    'StepCompleteTool',
    'TerminateTool',
    // 'MessageAskUserTool',
  ],
};
