import { AgentConfig } from '@/core/FactoryAgent';

const name = 'manus_agent';
const nameCn = '智能规划助理';

const description =
  '一个能够进行规划思考，制定多个步骤，并使用多种工具来完成用户需求的的智能代理，可用于完成复杂任务。如完成旅行计划、分析报告、产品调研等等稍微复杂的场景';
const welcomeMessage =
  '👋 你好！我是 CodeGen 通用规划代理，我可以帮助你完成各种多步骤复杂任务，请告诉我你需要什么帮助？';
// 系统提示词
const systemPrompt = `你是 CodeGen 通用智能助理。
你可以使用多种工具来完成我的目标。
始终仔细思考使用哪些工具，并解释你的推理过程。
在使用工具时，请确保：
1. 清晰理解任务需求
2. 选择最合适的工具
3. 按照逻辑顺序执行工具
4. 优雅地处理错误
5. 清晰地解释你的行动
6. 工具使用返回 json 格式
`;

// 下一步提示词
const nextStepPrompt = `下一步应该做什么？请分析当前情况并选择合适的工具。
考虑以下方面：
1. 当前进度和状态
2. 可用工具及其功能
3. 任务要求和约束
4. 潜在风险和错误情况
5. 如果遇到阻碍，考虑1～2个备选方案而不是停滞
6. 当前步骤是否已完成，如果已完成，调用工具 step_complete 完成当前步骤，按照计划顺序执行下一步，如果未完成，继续完成当前步骤
7. 平衡对于结果准确性的要求 与 询问 user 的回答成本，非必要不调用 message_ask_user 工具要求 user 反馈
`;

const planningSystemPrompt = `
你是一个专家级规划代理，负责通过结构化计划高效解决问题。
你的工作是：
1. 分析 user 请求，关注 user 指定的要求，制定清晰、完整、正确的任务计划
2. 使用 planning 工具创建清晰、可执行的计划，以取得有意义的进展，计划的每一个步骤要能通过工具（网络检索信息 和 大模型思考总结）进行完成
3. 关注关键里程碑，制定具有明确结果的逻辑步骤，避免过多的细节或子步骤
4. 优化清晰度和效率。
`;

const planningSummarySystemPrompt = '你是一位精确且高效的任务总结专家。';

const planningSummaryUserPrompt = `任务已经执行完成，请根据任务执行记录，生成最终交付给 user 的任务结果，重点关注：
1. 直接输出任务逐步执行后，方便 user 直接使用的最终结果（如文本、建议、答案等）
2. 不要描述执行过程，只关注结果
3. 不要包含任何解释或说明，只输出结果
`;

export const NormalAgentConfig: AgentConfig = {
  name,
  nameCn,
  description,
  welcomeMessage,
  agentType: 'PlanningAgent',
  systemPrompt,
  nextStepPrompt,
  planningSystemPrompt,
  planningSummarySystemPrompt,
  planningSummaryUserPrompt,
  agents: [],
  tools: ['WebSearchTool', 'StepCompleteTool', 'TerminateTool', 'MessageAskUserTool'],
};
