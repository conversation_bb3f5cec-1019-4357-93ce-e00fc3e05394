import { AgentConfig } from '@/core/FactoryAgent';
import { generatePRD, type GenerateAppComplexityLevel } from '@/tools/GenerateBizPRDTool/prd-gen.share';
import { getLocal } from '@ali/yc-utils';

import { generateNextPrompt } from './shared/shardPrompt';

const name = 'normal_app_agent';
const nameCn = '应用生成代理';

const welcomeMessage =
  '👋 你好！我是 CodeGen 通用智能助理，我可以帮助你完成各种多步骤复杂任务，请告诉我你需要什么帮助？';

const description = `一个能够使用多种工具来生成应用/系统/平台的代理（Agent），用于首次生成低代码 + 源代码开发混合的应用。
当作为工具的时候，参数上下文的内容应该保留用户完整的输入内容，一字不漏，字字准确。
`;

/**
 * 系统提示词
 * 这里是第一次和该模型交互的时候的提示词
 * @notice 可能不会触发 planning 阶段
 */
const systemPrompt = `{{render "agentic.normal-app-system"}}`;

// 下一步提示词
const nextStepPrompt = generateNextPrompt();

const complexity: GenerateAppComplexityLevel = getLocal('yida_code_gen_app_complexity') || 'medium';

const planningSystemPrompt =
  complexity === 'easy'
    ? `{{render "agentic.normal-app-planning-simple"}}`
    : `{{render "agentic.normal-app-planning"}}`;

const planningSummarySystemPrompt = '你是专业的摘要总结专家，善于将最总生成的应用信息进行摘要总结后回复给用户';

const planningSummaryUserPrompt = `开始总结，最终可包含内容如下：
1. 应用名称：包含应用名称 和 应用唯一标识
2. 功能概述：简述应用定位、功能、用途
3. 页面详情：页面 ID，页面类型（元数据表单、自定义页面）、页面名称、页面简述
其他要求：
1. 不要描述开发过程或内部实现细节
2. 保持内容简洁、结构清晰，便于我快速理解和使用
`;

// 8. 生成html格式内容，不要markdown格式
export const NormalAppConfig: AgentConfig = {
  name,
  nameCn,
  description,
  welcomeMessage,
  agentType: 'PlanningAgent',
  systemPrompt,
  nextStepPrompt,
  planningSystemPrompt,
  planningSummarySystemPrompt,
  planningSummaryUserPrompt,
  dynamicPromptProvider: {
    async beforePlanning(userContext: string) {
      const prd = await generatePRD({
        context: userContext,
        complexity: getLocal('yida_code_gen_app_complexity') || 'medium',
        onThinking: (thinking: string) => {
          this.feedbackLogger.thinking(thinking);
        },
        onResult: (answer: string) => {
          this.feedbackLogger.toolResult(answer);
        },
        onError: (error: string) => {
          this.logger.error(`PRD 文档生成失败: ${error}`);
        },
      });
      return prd.content;
    },
  },
  agents: [],
  tools: [
    // 应用信息相关
    'CreateAppTool',
    'CreateFormTool',
    // 'CreateHomePageTool',
    // 'MessageAskUserTool',
    // 代码生成类
    'GenerateCodeTool',
    // Mock 数据生成类
    'GenerateMockDataTool',
    // 数据管理类
    // 'GenerateDataManagePageTool',
    // 图表元信息获取和生成类
    // 'GenerateInterfaceTool',
    // 'GetCubesInfoTool',
    // 系统流程控制类
    'StepCompleteTool',
    'TerminateTool',
  ],
};
