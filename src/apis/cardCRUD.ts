import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 创建表单接口参数
 */
interface CreateCardParams {
  /** 应用唯一标识 */
  appType: string;
  /** 数据卡片名称 */
  title: string;
}

/**
 * 创建数据卡片接口
 * @param {CreateCardParams} params - 创建数据卡片的参数
 * @returns {Promise<Object>} 创建成功后返回的数据卡片信息
 */
export async function createCard(params: CreateCardParams): Promise<string> {
  try {
    return await cacheFetch(
      {
        url: `/dingtalk/web/${params.appType}/visual/dataCardRpc/createCard.json`,
        method: 'POST',
        data: {
          cardName: JSON.stringify({
            zh_CN: params.title,
            en_US: params.title,
            type: 'i18n',
          }),
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}

/**
 * 更新数据卡片接口参数
 */
interface UpdateCardParams {
  /** 应用唯一标识 */
  appType: string;
  /** 数据卡片ID */
  cardId: string;
  /** 数据卡片schema */
  cardSchema: any;
}

/**
 * 保存数据卡片接口
 * @param {UpdateCardParams} params - 创建数据卡片的参数
 * @returns {Promise<Object>} 创建成功后返回的数据卡片信息
 */
export async function updateCard(params: UpdateCardParams): Promise<string> {
  try {
    return await cacheFetch(
      {
        url: `/dingtalk/web/${params.appType}/visual/dataCardRpc/updateCard.json`,
        method: 'POST',
        data: {
          cardCode: params.cardId,
          schemaBody: JSON.stringify(params.cardSchema),
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}

interface PublishCardParams {
  /** 应用唯一标识 */
  appType: string;
  /** 数据卡片ID */
  cardId: string;
}

/**
 * 发布数据卡片接口
 * @param {PublishCardParams} params - 发布数据卡片的参数
 * @returns {Promise<Object>} 发布成功后返回的数据卡片信息
 */
export async function publishCard(params: PublishCardParams): Promise<string> {
  try {
    return await cacheFetch(
      {
        url: `/dingtalk/web/${params.appType}/visual/dataCardRpc/publishCard.json`,
        method: 'POST',
        data: {
          cardCode: params.cardId,
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}

interface DeleteCardParams {
  /** 应用唯一标识 */
  appType: string;
  /** 数据卡片ID */
  cardId: string;
}

/**
 * 删除数据卡片接口
 * @param {DeleteCardParams} params - 删除数据卡片的参数
 * @returns {Promise<Object>} 删除成功后返回的数据卡片信息
 */
export async function deleteCard(params: DeleteCardParams): Promise<string> {
  try {
    return await cacheFetch(
      {
        url: `/dingtalk/web/${params.appType}/visual/dataCardRpc/deleteCard.json`,
        method: 'POST',
        data: {
          cardCode: params.cardId,
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
