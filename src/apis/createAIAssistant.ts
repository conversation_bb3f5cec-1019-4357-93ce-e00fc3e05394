import { errorHandler, getConfig, request } from '@ali/yc-utils';

/**
 * 创建AI助理接口参数
 */
export interface CreateAIAssistantParams {
  /** 助理名称 */
  name: string;
  /** 助理介绍 */
  instructions: string;
  /** 推荐的提示语 */
  recommendPrompts?: string[];
  /** 应用类型，通常是APP_XXXX格式 */
  appType: string;
  /** 应用名称 */
  appName: string;
}

const iconMediaIds = [
  '@lALPDeREgBiHVejNAljNAlg',
  '@lALPDfYH-VBs5kHNAljNAlg',
  '@lALPDeREgBiZxl3NAljNAlg',
  '@lALPDfmVd_U8-hbNAljNAlg',
  '@lALPDeC3AXPQ5SzNAljNAlg',
  '@lALPDfYH-VBr9FDNAljNAlg',
  '@lALPDfYH-VB8PfrNAljNAlg',
  '@lALPDfYH-VB-so3NAljNAlg',
  '@lALPDfmVd_UrZ_nNAljNAlg',
  '@lALPDf0i9poJ4eTNAljNAlg',
  '@lALPDetffWIU7hfNAljNAlg',
  '@lALPDfYH-VBzlDjNAljNAlg',
  '@lALPDefR_tqYTFTNAljNAlg',
  '@lALPDetffX9gCIDNAljNAlg',
  '@lALPDf0i9rdChAvNAljNAlg',
  '@lALPDfYH-W2nLkbNAljNAlg',
  '@lALPDf0i9rdC2fvNAljNAlg',
  '@lALPDe7s_CQje77NAljNAlg',
  '@lALPDfJ6esjtI-LNAljNAlg',
  '@lALPDe7s_CQZnbTNAljNAlg',
];

/**
 * 创建AI助理接口
 * @param {CreateAIAssistantParams} params - 创建AI助理的参数
 * @returns {Promise<any>} 创建成功后返回的助理信息
 */
export async function createAIAssistant(params: CreateAIAssistantParams): Promise<any> {
  try {
    // 随机取一个icon
    const mediaId = iconMediaIds[Math.floor(Math.random() * iconMediaIds.length)];
    const recommendPrompts = params.recommendPrompts?.length
      ? params.recommendPrompts.slice(0, 3)
      : ['👀 帮我提交一个表单', '📈 帮我做数据分析', '💼 如何使用这个应用'];
    const data: any = {
      corpId: getConfig('corpId'),
      name: params.name || `${params.appName}的AI助理`,
      icon: mediaId,
      description: `基于宜搭低代码${params.appName}的AI助理`,
      instructions: params.instructions,
      toneStyle: 'WORKPLACE',
      welcomeTitle: '很高兴为你服务',
      welcomeContent: `我是一名熟悉宜搭${params.appName}的AI助理，我具备智能填报、数据分析、智能顾问能力。通过自然语言对话，我可以智能录入用户信息，轻松洞察数据分析。`,
      appScopes: JSON.stringify({ userVisibleScopes: window.loginUser.userId }),
      recommendPrompts: JSON.stringify(recommendPrompts),
      source: 'YIDA',
      schemaActionRequests: JSON.stringify([
        {
          pluginType: 'YIDA',
          actionName: '宜搭AI',
          description: params.instructions,
          actionTypeValue: 'ADAPTIVE',
        },
      ]),
      shareRecipient: 'PRIVATE',
      needBindOrgAuth: false,
    };
    const response = await request({
      url: `/dingtalk/web/${params.appType}/query/ai/createAIAssistant.json`,
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return {
      ...response,
      iconUrl: `//static.dingtalk.com/media/${mediaId.slice(1)}_600_600.png_620x10000q90.png`,
    };
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
