import { errorHandler, cacheFetch, getYidaConfig } from '@ali/yc-utils';

export interface CreateComponentParams {
  name: string;
  description: string;
}

export interface CreateComponentResponse {
  componentId: string;
}

export async function createComponent(params: CreateComponentParams): Promise<CreateComponentResponse> {
  try {
    const { name, description } = params;

    const res = await cacheFetch(
      {
        url: '/_epaas/uipaas/query/material/createLowCode.json',
        method: 'POST',
        data: {
          extend: 'display',
          name: `CC_${name}`,
          device: 'web,mobile',
          baseLibrary: 'fusion',
          description,
          createFrom: getYidaConfig('corpId'),
          sourceApp: 'yida_lowcode_app',
          template: 'LCC-64B1-1QCRK9KU92B2K63FBHKD3-IY3S0N8M-N74',
        },
      },
      true,
    );
    return res;
  } catch (error) {
    errorHandler(error);
    return null;
  }
}
