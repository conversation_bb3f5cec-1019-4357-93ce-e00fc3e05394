import { errorHandler, cacheFetch } from '@ali/yc-utils';

export interface CreateFormInstanceParams {
  appType: string;
  formUuid: string;
  formData: Record<string, any>;
}

export interface CreateFormInstanceResponse {
  formInstanceId: string;
}

export async function createFormInstance(params: CreateFormInstanceParams): Promise<CreateFormInstanceResponse> {
  try {
    const { appType, formUuid, formData } = params;

    const res = await cacheFetch(
      {
        url: `/${appType}/v1/form/saveFormData.json`,
        method: 'POST',
        data: {
          formUuid,
          appType,
          formDataJson: JSON.stringify(formData),
        },
      },
      true,
    );
    return {
      formInstanceId: res || '',
    };
  } catch (error) {
    errorHandler(error);
    return {
      formInstanceId: '',
    };
  }
}
