import { errorHandler, request } from '@ali/yc-utils';

/**
 * 创建或更新实体技能参数
 */
export interface CreateOrUpdateEntitySkillParams {
  /** 助理ID */
  agentCode: string;
  /** 技能类型 */
  skill: 'chatBI' | 'chatForm';
  /** 技能配置，JSON字符串 */
  skillConfig: any;
  /** 实体类型 */
  entityType: string;
  /** 实体ID */
  entityId: string;
  /** 技能状态 */
  status: 'on' | 'off';
  /** 应用类型 */
  appType: string;
}

/**
 * 创建或更新助理实体技能API
 * @param {CreateOrUpdateEntitySkillParams} params - 创建或更新实体技能的参数
 * @returns {Promise<boolean>} 创建或更新成功后返回的信息
 */
export async function createOrUpdateEntitySkill(params: CreateOrUpdateEntitySkillParams): Promise<boolean> {
  try {
    // 处理skillConfig参数，确保它是字符串
    const skillConfig =
      typeof params.skillConfig === 'string' ? params.skillConfig : JSON.stringify(params.skillConfig);

    // 构建请求参数
    const data: Record<string, any> = {
      agentCode: params.agentCode,
      skill: params.skill,
      skillConfig,
      entityType: params.entityType,
      entityId: params.entityId,
      status: params.status,
    };

    // 发送API请求
    const response = await request({
      url: `/dingtalk/web/${params.appType}/query/ai/createOrUpdateEntitySkill.json`,
      method: 'POST',
      data,
    });

    // 返回成功结果
    return response;
  } catch (err) {
    // 错误处理
    errorHandler(err);
    return false;
  }
}
