import type { GeneratedPageType } from '@/tools/GenerateCodeTool/code.share';
import { errorHandler, cacheFetch } from '@ali/yc-utils';

export interface CreatePageParams {
  appType: string;
  name: string;
  pageType?: GeneratedPageType;
}

export interface CreatePageResponse {
  pageId: string;
}

export async function createPage(params: CreatePageParams): Promise<CreatePageResponse> {
  try {
    const { name, appType } = params;

    const res = await cacheFetch(
      {
        url: `/dingtalk/web/${appType}/query/formdesign/saveFormSchemaInfo.json`,
        method: 'POST',
        data: {
          formType: 'display',
          title: JSON.stringify({
            zh_CN: name,
            en_US: name,
            type: 'i18n',
          }),
        },
      },
      true,
    );
    if (res?.formUuid) {
      return {
        pageId: res.formUuid,
      };
    }
    return {
      pageId: '',
    };
  } catch (error) {
    errorHandler(error);
    return {
      pageId: '',
    };
  }
}
