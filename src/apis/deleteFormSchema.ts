import { cacheFetch, errorHandler } from '@ali/yc-utils';

export interface DeleteParams {
  appType: string;
  formUuid: string;
}

/**
 * 删除页面
 * @returns {Promise<boolean>} 删除结果
 */
export async function deleteFormSchema(params: DeleteParams): Promise<boolean> {
  const { appType, formUuid } = params;
  try {
    await cacheFetch(
      {
        url: `/${appType}/query/formdesign/deleteFormSchema.json`,
        method: 'POST',
        data: {
          formUuid,
        },
      },
      true,
    );
    return true;
  } catch (err) {
    errorHandler(err);
    return false;
  }
}
