import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 获取应用列表参数
 */
export interface GetAppIncludingAecpInfoParams {
  /** 应用类型 */
  appType: string;
}

export interface GetAppIncludingAecpInfoResponse {
  /** 应用Key */
  appKey: string;
  /** 应用模式 */
  mode: string;
  /** 应用名称 */
  appName: {
    en_US: string;
    key: string;
    pureEn_US: string;
    type: string;
    value: string;
    zh_CN: string;
  };
  /** 应用描述 */
  description: {
    en_US: string;
    key: string;
    pureEn_US: string;
    type: string;
    value: string;
    zh_CN: string;
  };
  /** 应用图标 */
  icon: string;
  /** 导航栏配置 */
  navigation: string;
  /** 是否在应用中心显示 */
  showAppCenter: 'n' | 'y';
  /** 版本号 */
  reVersion: string;
  /** 应用类型 */
  type: 'single';
  /** 导航布局 */
  navLayout: 'auto';
  /** 颜色主题 */
  colour: 'blue' | 'orange' | 'green';
  /** 首页Logo */
  homepageLogo: string;
  /** Logo链接 */
  logoLink?: string;
  /** 系统链接 */
  systemLink: string;
  /** 是否添加水印 */
  addWaterMark: 'n' | 'y';
  /** 短链接 */
  shortUrl?: string;
  /** 是否允许外部通讯录 */
  allowExternalAddressBook?: 'n' | 'y';
  /** 详情布局 */
  detailLayout: 'vertical' | 'horizontal';
  /** 流程操作记录布局 */
  procOperateRecordLayout: 'standard' | 'merge';
  /** 分类ID */
  categoryId: 'DEFAULT';
  /** 是否默认顾问 */
  defaultConsultant: 'n' | 'y';
  /** 部门ID */
  appDeptId?: -1;
  /** 是否显示应用标题 */
  showAppTitle: boolean;
}

/**
 * 获取应用列表
 * @param {GetAppIncludingAecpInfoParams} params - 查询参数
 * @returns {Promise<any>} 应用列表响应
 */
export async function getAppIncludingAecpInfo(params: GetAppIncludingAecpInfoParams): Promise<any> {
  try {
    return await cacheFetch({
      url: `/${params.appType}/query/app/getAppIncludingAecpInfo.json`,
      method: 'GET',
      data: {
        appKey: params.appType,
      },
    });
    // odinTopicId
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
