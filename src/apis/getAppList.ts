import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 获取应用列表参数
 */
export interface GetAppListParams {
  /** 页码，从1开始 */
  pageIndex: number;
  /** 每页大小 */
  pageSize: number;
  /** 排序字段，如data_gmt_create */
  orderField?: string;
  /** 应用状态，如ONLINE */
  appStatus?: string;
  /** 创建者ID */
  creator?: string;
  /** 搜索关键字 */
  key?: string;
  /** 是否管理员 */
  isAdmin?: boolean;
}

/**
 * 应用列表响应
 */
export interface GetAppListResponse {
  /** 应用列表 */
  data: any[];
  /** 总数 */
  total: number;
  /** 页码 */
  pageIndex: number;
  /** 每页大小 */
  pageSize: number;
}

/**
 * 获取应用列表
 * @param {GetAppListParams} params - 查询参数
 * @returns {Promise<GetAppListResponse>} 应用列表响应
 */
export async function getAppList(params: GetAppListParams): Promise<GetAppListResponse> {
  try {
    const response = await cacheFetch(
      {
        url: '/query/app/getAppList.json',
        method: 'POST',
        data: {
          pageIndex: params.pageIndex || 1,
          pageSize: params.pageSize || 16,
          orderField: params.orderField || 'data_gmt_create',
          appStatus: params.appStatus || 'ONLINE',
          creator: params.creator,
          key: params.key,
          isAdmin: params.isAdmin,
        },
      },
      true,
    );
    return response;
  } catch (err) {
    console.error('获取应用列表失败:', err);
    errorHandler(err);
    throw err;
  }
}
