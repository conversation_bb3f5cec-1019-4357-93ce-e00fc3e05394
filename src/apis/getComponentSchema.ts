import { errorHandler, cacheFetch, ISchema } from '@ali/yc-utils';
import { IPropType } from './saveComponentSchema';

export interface GetComponentSchemaParams {
  componentId: string;
}

export interface GetComponentSchemaResponse {
  schema: ISchema;
  propTypes: IPropType[];
  code: string;
}

export async function getComponentSchema(params: GetComponentSchemaParams): Promise<GetComponentSchemaResponse> {
  try {
    const { componentId } = params;

    const res = await cacheFetch(
      {
        url: '/_epaas/uipaas/query/lowCode/getLowCodeSchema.json',
        method: 'GET',
        data: {
          _api: 'uipaasLowCodeComponentSchema.read',
          templateUuid: componentId,
          schemaVersion: 'V5',
          domainCode: 'tEXDRG',
        },
      },
      true,
    );

    if (res) {
      const { componentsTree } = res;

      return {
        schema: componentsTree,
        propTypes: componentsTree?.[0]?.propTypes || [],
        code: componentsTree?.[0]?.children?.[0]?.props?.code || '',
      };
    }

    return null;
  } catch (error) {
    errorHandler(error);
    return null;
  }
}
