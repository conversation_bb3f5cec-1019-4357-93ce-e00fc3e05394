import { cacheFetch, errorHandler, getConfig } from '@ali/yc-utils';

/**
 * 获取应用列表参数
 */
export interface GetCubeFieldListParams {
  /** 应用类型 */
  appType: string;
  /** cubeCode列表 */
  cubeCodes: string[];
}

export function judgeDateField(fieldObj: any) {
  if (!fieldObj) return false;
  if (fieldObj.dataType === 'DATE') return true;
  const { children } = fieldObj;
  if (!Array.isArray(children)) return false;
  return children.length === 6 && children[0].dataType === 'DATE' && children[0].timeGranularityType === 'YEAR';
}

/**
 * 获取应用列表
 */
export async function getCubeFieldList(params: GetCubeFieldListParams): Promise<any> {
  try {
    const response = await cacheFetch({
      url: `/${params.appType}/visual/datasetRpc/getSimpleCubeModelList.json`,
      method: 'GET',
      data: {
        isOldVersion: false,
        cubeTenantId: getConfig('corpId'),
        cubeCodes: params.cubeCodes.join(','),
      },
    });
    const fieldListMap = {} as any;
    response.forEach((one: any) => {
      const { id: cubeCode, children } = one || {};
      let fieldList = [] as any;
      (children || []).forEach((item: any) => {
        // 将日期区间打平一个级别，其他不打平；
        if (item.children && item.children.length === 2 && judgeDateField(item)) {
          fieldList = fieldList.concat(item.children);
        } else {
          if (item.children) {
            // For表单对象类元数据，如果是imageField
            const isImageField = item.id?.indexOf('imageField_') === 0;
            if (isImageField) {
              const tarImageField = item.children.find((sub: any) => sub.id === `${item.id}_name`);
              if (tarImageField) {
                fieldList.push({ ...tarImageField, text: item.text });
                return;
              }
            }
            const isCompanyField = item.id?.indexOf('ccCompanyField_') === 0;
            if (isCompanyField) {
              const tarCompanyField = item.children.find((sub: any) => sub.id === `${item.id}_companyName`);
              if (tarCompanyField) {
                fieldList.push({ ...tarCompanyField, text: '企业名称' });
                return;
              }
            }
            // For表单对象类元数据
            const tarValueAllField = item.children.find((sub: any) => sub.id === `${item.id}_value_all`);
            if (tarValueAllField) {
              fieldList.push({ ...tarValueAllField, text: item.text });
              return;
            }
            // For表单对象类元数据
            const tarValueField = item.children.find((sub: any) => sub.id === `${item.id}_value`);
            if (tarValueField) {
              fieldList.push({ ...tarValueField, text: item.text });
              return;
            }
            // For视图表 如果是外层无cubeCode 但children存在的情况
            if (!item.cubeCode) {
              const tarField = item.children.find((sub: any) => !!sub.cubeCode);
              if (tarField) {
                const newTarField = item.children.length === 1 ? tarField : { ...tarField, text: item.text };
                fieldList.push(newTarField);
                return;
              }
            }
          }
          fieldList.push(item);
        }
      });
      const sysIds = [
        'yida_originator_corp_id',
        'yida_department_paths',
        'yida_departments',
        'originator_work_no',
        'parent_pid',
        'namespace_code',
        'table_name',
      ];
      fieldList = fieldList
        .map((item: any) => {
          const tarName = item.text.replace(/_/g, '');
          return {
            ...item,
            name: tarName,
            text: tarName,
          };
        })
        .filter((item: any) => !sysIds.includes(item.id));
      fieldListMap[cubeCode] = fieldList;
    });
    return fieldListMap;
  } catch (err) {
    errorHandler(err);
    return {};
  }
}
