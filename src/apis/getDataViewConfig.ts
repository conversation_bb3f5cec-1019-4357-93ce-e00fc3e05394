import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 数据管理页视图信息
 */
export interface ViewConfig {
  sort: string[];
  showFields: string[] | 'all';
  lockFieldIds?: string[];
  lineHeight?: number;
  filter?: Record<string, any>;
  widths?: Record<string, number>;
}

/**
 * 字段相关信息
 */
export interface FieldConfig {
  id: string;
  componentName: string;
  dataType: string;
  supportSearch: boolean;
  supportSort: boolean;
  supportVisible: boolean;
}

/**
 * 数据管理页视图信息
 */
export interface GetDataViewConfigParams {
  /** 应用唯一标识 */
  appType: string;
  /** 表单UUID */
  formUuid: string;
  /** 视图UUID */
  viewUuid: string;
}

/**
 * 数据视图基本信息
 */
export interface GetDataViewConfigResponse {
  componentValueVoList: FieldConfig[];
  buttonList: any[];
  viewConfig: ViewConfig;
}

/**
 * 获取数据管理页视图信息
 * @param {GetDataViewConfigParams} params - 查询参数
 * @returns {Promise<GetDataViewConfigResponse>} 表单Schema响应
 */
export async function getDataViewConfig(params: GetDataViewConfigParams): Promise<GetDataViewConfigResponse> {
  const { appType, formUuid, viewUuid } = params;
  try {
    const response = await cacheFetch(
      {
        url: `/${appType}/query/dataview/getDataViewPanel.json`,
        method: 'GET',
        data: {
          formUuid,
          viewUuid,
        },
      },
      true,
    );

    return response;
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
