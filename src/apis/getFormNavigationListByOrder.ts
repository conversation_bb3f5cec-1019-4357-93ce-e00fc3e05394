import { cacheFetch, errorHandler, i18nFormat } from '@ali/yc-utils';

export interface FormNavigationListByOrderParams {
  appType: string;
}

interface NavigationInfo {
  id: string;
  navId: number;
  type: string;
  navType: 'SYSTEM' | 'PAGE' | 'NAV';
  title: string;
  prdId: string;
}

/**
 * 获取表单导航列表
 * @returns {Promise<any>} 表单导航列表响应
 */
export async function getFormNavigationListByOrder(params: any): Promise<NavigationInfo[]> {
  try {
    const response = await cacheFetch(
      {
        url: `/dingtalk/web/${params.appType}/query/formnav/getFormNavigationListByOrder.json`,
        method: 'GET',
      },
      true,
    );
    return response.map((one: any) => {
      return {
        id: one.formUuid || one.navUuid,
        navId: one.id,
        type: one.formType,
        navType: one.navType,
        title: i18nFormat(one.title),
        prdId: one.topicId,
      };
    });
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
