import type { GeneratedPageType } from '@/tools/GenerateCodeTool/code.share';
import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 获取表单Schema参数
 */
export interface GetFormSchemaParams {
  /** 应用唯一标识 */
  appType: string;
  /** 表单UUID */
  formUuid: string;
  /** Schema版本 */
  schemaVersion?: string;
  pageType?: GeneratedPageType;
}
/**
 * 表单Schema响应
 */
export interface GetFormSchemaResponse {
  /** 页面schame */
  pages?: any[];
  /** 其他属性 */
  [key: string]: any;
}

/**
 * 获取表单Schema
 * @param {GetFormSchemaParams} params - 查询参数
 * @returns {Promise<GetFormSchemaResponse>} 表单Schema响应
 */
export async function getFormSchema(params: GetFormSchemaParams): Promise<GetFormSchemaResponse> {
  try {
    const response = await cacheFetch(
      {
        url: `/alibaba/web/${params.appType}/_view/query/formdesign/getFormSchema.json`,
        method: 'GET',
        data: {
          formUuid: params.formUuid,
          schemaVersion: params.schemaVersion || 'V5',
        },
      },
      true,
    );

    return response;
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
