import { cacheFetch, errorHandler, II18n } from '@ali/yc-utils';

/**
 * 获取表单Schema参数
 */
export interface GetFormSchemaInfoParams {
  /** 应用唯一标识 */
  appType: string;
  /** 表单UUID */
  formUuid: string;
}
/**
 * 页面信息响应
 */
export interface GetFormSchemaInfoResponse {
  formType: string;
  formUuid: string;
  title: II18n;
  relateFormType?: string;
  relateFormUuid?: string;
  multiViewUuids?: string[];
}

/**
 * 获取页面基本信息
 * @param {GetFormSchemaInfoParams} params - 查询参数
 * @returns {Promise<GetFormSchemaInfoResponse>} 页面信息
 */
export async function getFormSchemaInfo(params: GetFormSchemaInfoParams): Promise<GetFormSchemaInfoResponse> {
  const { appType, formUuid } = params;
  try {
    const response = await cacheFetch(
      {
        url: `/${appType}/query/formdesign/getFormSchemaInfo.json`,
        method: 'GET',
        data: {
          formUuid,
        },
      },
      true,
    );

    return response;
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
