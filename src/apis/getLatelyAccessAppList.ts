import { cacheFetch } from '@ali/yc-utils';

export interface GetLatelyAccessAppListParams {
  pageIndex?: number;
  pageSize?: number;
}

export interface GetLatelyAccessAppListResponse {
  currentPage: number;
  data: any[];
  entities: any;
  hasMore: boolean;
  idCursor: number;
  totalCount: number;
}

/**
 * 获取最近访问的应用列表
 * @param params
 * @returns
 */
export const getLatelyAccessAppList = async (
  params: GetLatelyAccessAppListParams = {},
): Promise<GetLatelyAccessAppListResponse> => {
  try {
    const response = await cacheFetch(
      {
        url: '/query/app/getLatelyAccessAppList.json',
        method: 'POST',
        data: {
          pageIndex: params.pageIndex || 1,
          pageSize: params.pageSize || 10,
        },
      },
      true,
    );
    return response;
  } catch (error) {
    console.error('获取最近访问应用列表失败:', error);
    throw error;
  }
};

export default getLatelyAccessAppList;
