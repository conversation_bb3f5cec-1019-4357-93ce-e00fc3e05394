import { cacheFetch } from '@ali/yc-utils';

export interface GetQuickAccessAppListParams {
  pageIndex?: number;
  pageSize?: number;
}

export interface GetQuickAccessAppListResponse {
  currentPage: number;
  data: any[];
  entities: any;
  hasMore: boolean;
  idCursor: number;
  totalCount: number;
}

/**
 * 获取快速访问的应用列表
 * @param params
 * @returns
 */
export const getQuickAccessAppList = async (
  params: GetQuickAccessAppListParams = {},
): Promise<GetQuickAccessAppListResponse> => {
  try {
    const response = await cacheFetch(
      {
        url: '/query/app/getQuickAccessAppList.json',
        method: 'POST',
        data: {
          pageIndex: params.pageIndex || 1,
          pageSize: params.pageSize || 10,
          isAdmin: false,
        },
      },
      true,
    );
    return response;
  } catch (error) {
    console.error('获取快速访问应用列表失败:', error);
    throw error;
  }
};

export default getQuickAccessAppList;
