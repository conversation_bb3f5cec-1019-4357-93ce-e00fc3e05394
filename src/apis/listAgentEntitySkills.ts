import { errorHandler, request } from '@ali/yc-utils';

/**
 * 获取助理实体技能列表的参数
 */
export interface ListAgentEntitySkillsParams {
  /** 助理ID */
  agentCode: string;
  /** 应用类型 */
  appType: string;
}

/**
 * 技能实体信息接口
 */
export interface SkillEntity {
  /** 实体ID */
  entityId: string;
  /** 实体类型 */
  entityType: string;
  /** 技能名称 */
  skill: string;
  /** 技能配置 */
  skillConfig: any;
  /** 技能状态，开启或关闭 */
  status: 'on' | 'off';
  /** 更新时间 */
  gmtModified: string;
  /** 创建时间 */
  gmtCreate: string;
}

/**
 * 获取助理实体技能列表API
 * @param {ListAgentEntitySkillsParams} params - 获取助理实体技能列表的参数
 * @returns {Promise<SkillEntity[] | null>} 返回技能实体列表，失败返回null
 */
export async function listAgentEntitySkills(params: ListAgentEntitySkillsParams): Promise<SkillEntity[] | null> {
  try {
    // 构建请求参数
    const data = {
      agentCode: params.agentCode,
    };

    // 发送API请求
    const response = await request({
      url: `/dingtalk/web/${params.appType}/query/ai/listAgentEntitySkills.json`,
      method: 'POST',
      data,
    });
    // 检查响应格式
    if (Array.isArray(response)) {
      return response;
    }
    // 处理空结果
    return [];
  } catch (err) {
    // 错误处理
    errorHandler(err);
    return null;
  }
}
