import { cacheFetch } from '@ali/yc-utils';

export interface MsanageQuickAccessParams {
  appType: string | number;
  isDeleted?: 'y' | 'n'; // 'y'表示取消收藏，'n'表示收藏
}

export interface MsanageQuickAccessResponse {
  success: boolean;
  message?: string;
}

/**
 * 管理应用收藏状态
 * @param params
 * @returns
 */
export const ManageQuickAccess = async (params: MsanageQuickAccessParams): Promise<MsanageQuickAccessResponse> => {
  try {
    const response = await cacheFetch(
      {
        url: '/query/app/manageQuickAccess.json',
        method: 'POST',
        data: {
          appType: params.appType,
          isDeleted: params.isDeleted || 'y', // 默认为取消收藏
        },
      },
      false,
    );
    return response;
  } catch (error) {
    console.error('管理应用收藏状态失败:', error);
    throw error;
  }
};
