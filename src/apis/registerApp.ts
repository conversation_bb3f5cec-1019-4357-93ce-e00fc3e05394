import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 注册应用接口参数
 */
interface RegisterAppParams {
  /** 应用名称 */
  appName: string;
  /** 应用描述，简要说明应用的用途和功能 */
  description: string;
  /** 应用Icon */
  homepageLogo?: string;
  /** 应用图标 */
  icon?: string;
  /** 应用图标URL */
  iconUrl?: string;
  /** 应用模板ID，可选择预设模板快速创建 */
  template?: string;
}

/**
 * 注册应用接口
 * @param {RegisterAppParams} params - 注册应用的参数
 * @returns {Promise<Object>} 注册成功后返回的应用信息
 */
export async function registerApp(params: RegisterAppParams): Promise<any> {
  try {
    return await cacheFetch(
      {
        url: '/query/app/registerApp.json',
        method: 'POST',
        data: {
          appName: JSON.stringify({ zh_CN: params.appName, en_US: params.appName, type: 'i18n' }),
          description: JSON.stringify({ zh_CN: params.description, en_US: params.description, type: 'i18n' }),
          icon: params.icon,
          iconUrl: params.iconUrl || params.icon,
          homepageLogo: params.homepageLogo,
          colour: 'blue',
          defaultLanguage: 'zh_CN',
          openExclusive: 'n',
          openPhysicColumn: 'n',
          openIsolationDatabase: 'n',
          openExclusiveUnit: 'n',
          group: '全部应用',
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
