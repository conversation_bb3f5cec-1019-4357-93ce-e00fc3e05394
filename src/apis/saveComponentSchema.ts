import { errorHandler, cacheFetch, ISchema } from '@ali/yc-utils';

/**
 * 国际化文案接口定义
 * 用于定义多语言文案的数据结构
 */
export interface II18n {
  /** 标识这是一个国际化文案对象 */
  type: 'i18n';
  /** 国际化文案的唯一标识键 */
  key?: string;
  /** 简体中文文案 */
  zh_CN: string;
  /** 英文文案 */
  en_US: string;
}

/**
 * 选项配置，在单选场景使用
 */
export interface IOption {
  /** 选项的显示文案，支持国际化 */
  title: II18n;
  /** 选项的实际值 */
  value: string;
}

/**
 * 单个属性的配置，用于描述组件的配置信息
 */
export interface IPropType {
  /** 配置项的标题，支持国际化 */
  title: II18n;
  /** 配置项的提示信息，支持国际化 */
  tipContent: II18n;
  /** 属性值 */
  name: string;
  /** 数据类型 */
  type: 'string' | 'boolean' | 'number' | 'object' | 'array' | 'function';
  /**
   * 用于配置属性的设置器类型，需要根据type进行设置其类型匹配
   */
  setter:
    | 'BoolSetter' // 布尔值设置器
    | 'TextSetter' // 文本设置器
    | 'I18nSetter' // 国际化文案设置器
    | 'ChoiceSetter' // 单选设置器
    | 'ImageSetter' // 图片链接设置器
    | 'NumberSetter' // 数字设置器
    | 'DateSetter' // 日期设置器
    | 'ActionSetter' // 动作设置器
    | 'ListSetter' // 列表设置器
    | 'JsonSetter'; // JSON设置器
  /** 配置项的默认值 */
  defaultValue: any;
  /** 当setter为ChoiceSetter时，配置项的可选项列表 */
  __options__: IOption[];
  /** 当setter为ListSetter时，配置项的子配置项列表 */
  configure: IPropType[];
  /** 是否支持变量配置 */
  supportVariable: boolean;
  /** 属性显示方式 */
  display: 'block' | 'inline';
  /** 属性唯一标识 */
  __sid?: string;
}

/**
 * 保存组件Schema的参数接口
 */
export interface SaveComponentSchemaParams {
  /** 组件ID */
  componentId: string;

  /** Schema内容 */
  schema?: ISchema;

  /** 组件属性 */
  propTypes?: IPropType[];
}

/**
 * 保存组件Schema的响应接口
 */
export interface SaveComponentSchemaResponse {
  componentId?: string;
}

/**
 * 保存组件Schema
 * @param {SaveComponentSchemaParams} params - 保存参数
 * @returns {Promise<SaveComponentSchemaResponse>} 保存响应
 */
export async function saveComponentSchema(params: SaveComponentSchemaParams): Promise<SaveComponentSchemaResponse> {
  try {
    const { componentId, schema, propTypes } = params;
    if (schema) {
      await cacheFetch(
        {
          url: '/_epaas/uipaas/query/materialLowcode/saveLowCodeProps.json?_api=LowcodeComponentSavePane.saveLowCode',
          method: 'POST',
          data: {
            templateUuid: componentId,
            schemaVersion: 'V5',
            domainCode: 'tEXDRG',
            prefix: '_epaas/uipaas',
            content: JSON.stringify(schema),
          },
        },
        true,
      );
    }

    if (propTypes) {
      await cacheFetch(
        {
          url: '/_epaas/uipaas/query/materialLowcode/saveLowCodeProps.json?_api=LowcodeComponentSavePane.saveLowCode',
          method: 'POST',
          data: {
            lowCodeComponentId: componentId,
            schemaVersion: 'V5',
            domainCode: 'tEXDRG',
            propTypes: JSON.stringify(propTypes),
          },
        },
        true,
      );
    }

    await cacheFetch(
      {
        url: '/query/transfer/transferComponent.json?_api=LowcodeComponentSavePane.transferComponent',
        method: 'POST',
        data: {
          templateUuid: componentId,
          schemaVersion: 'V5',
          domainCode: 'tEXDRG',
          lowCodeComponentId: componentId,
          version: '0.1.0',
        },
      },
      true,
    );

    return { componentId };
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
