import { cacheFetch, errorHandler, II18n } from '@ali/yc-utils';

/**
 * 保存导航信息
 */
export interface SaveFormNavigationParams {
  /** 应用ID */
  appType: string;
  /** 菜单标题 */
  title: II18n;
}

/**
 * 创建导航Item
 * @param {SaveFormNavigationParams} params - 保存参数
 * @returns {Promise<string>} 保存结果响应
 */
export async function saveFormNavigation(params: SaveFormNavigationParams): Promise<number> {
  try {
    const { appType, title } = params;
    return await cacheFetch(
      {
        url: `/${appType}/query/formnav/saveFormNavigation.json`,
        method: 'POST',
        data: {
          title: JSON.stringify(title),
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
