import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 保存表单Schema参数
 */
export interface SaveFormSchemaParams {
  /** 表单schema内容 */
  content: Record<string, any>;
  /** 表单UUID */
  formUuid: string;
  /** Schema版本 */
  schemaVersion?: string;
  /** 应用唯一标识 */
  appType?: string;
}

/**
 * 保存表单Schema响应
 */
export interface SaveFormSchemaResponse {
  /** 表单UUID */
  formUuid: string;
  /** 版本号 */
  version: number;
}

/**
 * 保存表单Schema
 * @param {SaveFormSchemaParams} params - 保存参数
 * @returns {Promise<SaveFormSchemaResponse>} 保存结果响应
 */
export async function saveFormSchema(params: SaveFormSchemaParams): Promise<SaveFormSchemaResponse> {
  try {
    return await cacheFetch(
      {
        url: `/alibaba/web/${params.appType}/_view/query/formdesign/saveFormSchema.json`,
        method: 'POST',
        data: {
          content: JSON.stringify(params.content),
          formUuid: params.formUuid,
          schemaVersion: params.schemaVersion || 'V5',
          domainCode: 'tEXDRG', // 临时方案
          prefix: '_view',
        },
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
