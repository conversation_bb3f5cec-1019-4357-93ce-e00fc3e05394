import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 创建表单接口参数
 */
interface SaveFormSchemaParams {
  /** 应用唯一标识 */
  appType: string;
  /** 表单类型 */
  formType: string;
  /** 表单标题 */
  title: string;
  /** 关联的表单ID */
  relateFormUuid?: string;
  /** 关联的表单类型 */
  relateFormType?: 'receipt';
}

/**
 * 创建表单接口
 * @param {SaveFormSchemaParams} params - 创建表单的参数
 * @returns {Promise<Object>} 创建成功后返回的表单信息
 */
export async function saveFormSchemaInfo(params: SaveFormSchemaParams): Promise<any> {
  try {
    const data: any = {
      formType: params.formType || 'receipt',
      relateFormType: params.formType || 'receipt',
      title: JSON.stringify({
        zh_CN: params.title,
        en_US: params.title,
        type: 'i18n',
      }),
    };
    if (params.formType === 'view') {
      data.relateFormUuid = params.relateFormUuid;
    }
    return await cacheFetch(
      {
        url: `/dingtalk/web/${params.appType}/query/formdesign/saveFormSchemaInfo.json`,
        method: 'POST',
        data,
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
