import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 获取应用列表参数
 */
export interface SearchCubeListParams {
  /** 应用类型 */
  appType: string;
  /** 页码，从1开始 */
  pageIndex?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段，如data_gmt_create */
  cubeSource?: string;
}

/**
 * 应用列表响应
 */
export interface SearchCubeListResponse {
  /** 应用列表 */
  name: string;
  /** 总数 */
  code: string;
}

/**
 * 获取应用列表
 */
export async function searchCubeList(params: SearchCubeListParams): Promise<SearchCubeListResponse[]> {
  try {
    const response = await cacheFetch({
      url: `/${params.appType}/visual/datasetRpc/searchCubeList.json`,
      method: 'GET',
      data: {
        pageIndex: params.pageIndex || 1,
        pageSize: params.pageSize || 10,
        cubeSource: params.cubeSource || '',
      },
    });
    return response?.data || [];
  } catch (err) {
    errorHandler(err);
    return [];
  }
}
