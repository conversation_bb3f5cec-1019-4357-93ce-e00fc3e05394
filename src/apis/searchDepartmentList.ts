import { errorHandler, cacheFetch, i18nFormat } from '@ali/yc-utils';

export interface SearchDepartmentListParams {
  keyword?: string;
  count?: number;
}

export interface DepartmentInfo {
  deptFullPath: string;
  emplId: string;
  name: string;
}

export interface SearchDepartmentListResponse {
  list: DepartmentInfo[];
}

export async function searchDepartmentList(
  params?: SearchDepartmentListParams,
  direct = false,
): Promise<SearchDepartmentListResponse> {
  try {
    const { keyword = '', count = 20 } = params || {};

    const res = await cacheFetch(
      {
        url: '/query/deptService/searchDepts.json',
        data: {
          key: keyword,
          limit: count,
        },
      },
      direct,
    );
    const list = (res?.values || []).map((item: any) => ({
      deptFullPath: i18nFormat(item.deptFullPath),
      emplId: item.emplId,
      name: i18nFormat(item.name),
    }));

    return {
      list,
    };
  } catch (error) {
    errorHandler(error);
    return {
      list: [],
    };
  }
}
