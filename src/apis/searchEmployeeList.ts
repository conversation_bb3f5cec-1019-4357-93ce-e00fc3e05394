import { errorHandler, cacheFetch } from '@ali/yc-utils';

export interface SearchEmployeeListParams {
  keyword?: string;
  count?: number;
}

export interface EmployeeInfo {
  avatar: string;
  name: string;
  emplId: string;
  deptId: string;
}

export interface SearchEmployeeListResponse {
  list: EmployeeInfo[];
}

export async function searchEmployeeList(
  params?: SearchEmployeeListParams,
  direct = false,
): Promise<SearchEmployeeListResponse> {
  try {
    const { keyword = '', count = 20 } = params || {};
    const res = await cacheFetch(
      {
        url: '/query/userservice/searchUserList.json',
        data: {
          key: keyword,
          limit: count,
        },
      },
      direct,
    );
    return {
      list: res?.userList || [],
    };
  } catch (error) {
    errorHandler(error);
    return {
      list: [],
    };
  }
}
