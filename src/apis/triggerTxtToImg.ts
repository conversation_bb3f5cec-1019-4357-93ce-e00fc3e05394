import { cacheFetch, getConfig, errorHandler, wait } from '@ali/yc-utils';
import { message } from 'antd';

/**
 * 获取AI生成图片结果接口参数
 */
interface GetImgFromAIParams {
  /** 任务ID，由triggerTxtToImg接口返回 */
  taskId: string;
}

/**
 * 获取AI生成图片结果接口
 * @param {GetImgFromAIParams} params - 获取图片结果的参数
 * @returns {Promise<Object>} 图片生成结果，包含图片URL等信息
 */
async function getImgFromAI(params: GetImgFromAIParams): Promise<any> {
  try {
    return await cacheFetch(
      {
        url: `/dingtalk/web/${getConfig('appType')}/query/intelligent/getImgFromAI.json`,
        method: 'POST',
        data: params,
      },
      true,
    );
  } catch (err) {
    errorHandler(err);
  }
}

/**
 * 文本生成图片接口参数
 */
export interface TxtToImgParams {
  /** 图片尺寸，如 "1024*1024" */
  size: string;
  /** 生成图片数量 */
  batchSize: number;
  /** 图片生成提示词 */
  prompt: string;
}

/**
 * 文本生成图片接口
 * @param {TxtToImgParams} params - 生成图片的参数
 * @returns {Promise<Object>} 生成结果，包含任务ID等信息
 */
export async function triggerTxtToImg(params: TxtToImgParams): Promise<string[]> {
  try {
    const taskRes = await cacheFetch(
      {
        url: `/dingtalk/web/${getConfig('appType')}/query/intelligent/triggerTxtToImg.json`,
        method: 'POST',
        data: params,
      },
      true,
    );
    if (!taskRes?.taskId) {
      throw new Error('生成图片失败');
    }
    const { taskId } = taskRes;
    const checkResult = async () => {
      const res = await getImgFromAI({ taskId });
      if (res.taskStatus === 'SUCCEEDED') {
        return res.results.map((item: any) => `/${getConfig('appType')}/${item.url}`);
      } else if (res.taskStatus === 'FAILED') {
        // throw new Error('生成图片失败');
        message.error('生成图片失败');
      } else {
        await wait(2000);
        return await checkResult();
      }
    };
    return await checkResult();
  } catch (err) {
    errorHandler(err);
    return [];
  }
}
