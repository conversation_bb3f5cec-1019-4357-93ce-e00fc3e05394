import { cacheFetch, errorHandler } from '@ali/yc-utils';

/**
 * 更新应用参数
 */
export interface UpdateAppParams {
  /** 应用Key */
  appKey: string;
  /** 应用模式 */
  mode: 'normal';
  /** 应用名称 */
  appName: {
    en_US: string;
    key: string;
    pureEn_US: string;
    type: string;
    value: string;
    zh_CN: string;
  };
  /** 应用描述 */
  description: {
    en_US: string;
    key: string;
    pureEn_US: string;
    type: string;
    value: string;
    zh_CN: string;
  };
  /** 应用图标 */
  icon: string;
  /** 导航栏配置 */
  navigation: 'TODO,DONE,SUBMIT';
  /** 是否在应用中心显示 */
  showAppCenter: 'n';
  /** 版本号 */
  reVersion: '5.9.16';
  /** 应用类型 */
  type: 'single';
  /** 导航布局 */
  navLayout: 'auto';
  /** 颜色主题 */
  colour: 'blue' | 'orange' | 'green';
  /** 首页Logo */
  homepageLogo: string;
  /** Logo链接 */
  logoLink?: string;
  /** 系统链接 */
  systemLink: string;
  /** 是否添加水印 */
  addWaterMark: 'n' | 'y';
  /** 短链接 */
  shortUrl?: string;
  /** 是否允许外部通讯录 */
  allowExternalAddressBook?: 'n' | 'y';
  /** 详情布局 */
  detailLayout: 'vertical' | 'horizontal';
  /** 流程操作记录布局 */
  procOperateRecordLayout: 'standard' | 'merge';
  /** 分类ID */
  categoryId: 'DEFAULT';
  /** 是否默认顾问 */
  defaultConsultant: 'n' | 'y';
  /** 部门ID */
  appDeptId?: -1;
  /** 是否显示应用标题 */
  showAppTitle: boolean;
}

/**
 * 更新应用信息
 * @param {UpdateAppParams} params - 更新应用的参数
 * @returns {Promise<boolean>} 更新结果，成功返回true，失败返回false
 */
export async function updateApp(params: UpdateAppParams): Promise<boolean> {
  try {
    const response = await cacheFetch({
      url: `/${params.appKey}/query/app/updateApp.json`,
      method: 'POST',
      data: {
        ...params,
        appName: JSON.stringify(params.appName),
        description: JSON.stringify(params.description),
      },
    });
    return response;
  } catch (err) {
    errorHandler(err);
    return false;
  }
}
