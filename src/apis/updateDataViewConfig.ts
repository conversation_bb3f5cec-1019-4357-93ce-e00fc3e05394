import { cacheFetch, errorHandler } from '@ali/yc-utils';
import { ViewConfig } from './getDataViewConfig';
/**
 * 更新数据视图配置
 */
export interface UpdateDataViewConfigParams {
  /** 应用唯一标识 */
  appType: string;
  /** 表单UUID */
  formUuid: string;
  /** 视图UUID */
  viewUuid: string;
  /** 视图配置 */
  config: ViewConfig;
}

/**
 * 更新数据视图配置
 * @param {UpdateDataViewConfigParams} params - 更新数据视图配置的参数
 * @returns {Promise<boolean>} 响应
 */
export async function updateDataViewConfig(params: UpdateDataViewConfigParams): Promise<boolean> {
  try {
    const { appType, formUuid, viewUuid, config } = params;
    const response = await cacheFetch(
      {
        url: `/${appType}/query/dataview/updateConfig.json`,
        method: 'POST',
        data: {
          formUuid,
          viewUuid,
          defaultConfig: JSON.stringify(config),
        },
      },
      true,
    );

    return response;
  } catch (err) {
    errorHandler(err);
    return null;
  }
}
