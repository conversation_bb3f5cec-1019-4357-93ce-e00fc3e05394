import { cacheFetch, errorHandler, II18n } from '@ali/yc-utils';

export interface UpdateNavigationParams {
  appType: string;
  id: number;
  hidden?: 'n' | 'y';
  mobileHidden?: 'n' | 'y';
  navUuid: string;
  formUuid?: string;
  title?: II18n;
  url?: string;
  icon?: string;
  parentNavUuid?: string;
  parentId?: number;
  slug?: string;
  isNew?: 'n' | 'y';
}

/**
 * 更新应用导航
 * @returns {Promise<boolean>} 更新结果
 */
export async function updateFormNavigation(params: UpdateNavigationParams): Promise<boolean> {
  const {
    appType,
    id,
    hidden = 'n',
    mobileHidden = 'n',
    navUuid,
    formUuid,
    title,
    url = '',
    icon = '',
    parentNavUuid = 'NAV-SYSTEM-PARENT-UUID',
    parentId = 0,
    slug = '',
    isNew = 'n',
  } = params;
  try {
    await cacheFetch(
      {
        url: `/${appType}/query/formnav/updateFormNavigation.json`,
        method: 'POST',
        data: {
          id,
          hidden,
          mobileHidden,
          navUuid,
          formUuid,
          title: JSON.stringify(title),
          url,
          icon,
          parentNavUuid,
          parentId,
          slug,
          isNew,
        },
      },
      true,
    );
    return true;
  } catch (err) {
    errorHandler(err);
    return false;
  }
}
