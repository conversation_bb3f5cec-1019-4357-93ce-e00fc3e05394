import { cacheFetch, errorHandler } from '@ali/yc-utils';

export interface UpdateNavigationOrderParams {
  appType: string;
  currentId: number;
  parentNavUuid?: string;
  ids: number[];
}

/**
 * 更新应用导航列表
 * @returns {Promise<boolean>} 更新结果
 */
export async function updateFormNavigationOrder(params: UpdateNavigationOrderParams): Promise<boolean> {
  const { appType, currentId, ids, parentNavUuid } = params;
  try {
    await cacheFetch(
      {
        url: `/${appType}/query/formnav/updateFormNavigationOrderNew.json`,
        method: 'POST',
        data: {
          currentId,
          navType: 'PAGE',
          parentNavUuid: parentNavUuid || 'NAV-SYSTEM-PARENT-UUID',
          ids: ids.join(','),
        },
      },
      true,
    );
    return true;
  } catch (err) {
    errorHandler(err);
    return false;
  }
}
