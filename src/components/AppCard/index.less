.yida-home-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  align-items: flex-start;
  position: relative;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
    border-color: #e6e6e6;
  }

  .icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f5f7ff;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    object-fit: contain;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content {
    flex: 1;
    min-width: 0;
  }

  .title {
    font-size: 15px;
    font-weight: 500;
    color: #262626;
    margin: 0 0 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .description {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 40px;
  }

  .action-icon {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    color: #8c8c8c;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    z-index: 2;
    cursor: pointer;
  }

  .unfavorite-icon {
    color: #f5222d;
    &:hover {
      color: #ff4d4f;
      transform: scale(1.1);
    }
  }

  .favorite-icon {
    color: #0089ff;
    &:hover {
      color: #0673d3;
      transform: scale(1.1);
    }
  }
}
