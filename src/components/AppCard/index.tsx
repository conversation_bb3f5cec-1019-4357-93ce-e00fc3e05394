import React, { useState } from 'react';
import { Icon } from '@alifd/next';
import './index.less';

interface AppCardProps {
  title: string;
  description: string;
  icon: string;
  iconType: 'image' | 'icon';
  onClick?: () => void;
  isFavorite?: boolean;
  onUnfavorite?: (e: React.MouseEvent) => void;
  onFavorite?: (e: React.MouseEvent) => void;
}

const AppCard: React.FC<AppCardProps> = ({
  title,
  description,
  icon,
  iconType,
  onClick,
  isFavorite = false,
  onUnfavorite,
  onFavorite,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  return (
    <div
      className={`yida-home-card ${isFavorite ? 'is-favorite' : ''}`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="icon-wrapper">
        {iconType === 'image' ? (
          <img src={icon} alt={title} className="icon" />
        ) : (
          <i
            className={`appList-icon iconfont icon-${icon.split('%%')[0]} icon`}
            style={{ backgroundColor: icon.split('%%')[1] }}
          />
        )}
      </div>
      <div className="content">
        <h3 className="title">{title}</h3>
        <p className="description">{description}</p>
      </div>
      {isHovered &&
        (isFavorite && onUnfavorite ? (
          <div
            className="action-icon unfavorite-icon"
            onClick={(e) => {
              e.stopPropagation();
              onUnfavorite(e);
            }}
            title="取消收藏"
          >
            <Icon type="quxiaodingzhu" size="small" />
          </div>
        ) : !isFavorite && onFavorite ? (
          <div
            className="action-icon favorite-icon"
            onClick={(e) => {
              e.stopPropagation();
              onFavorite(e);
            }}
            title="收藏"
          >
            <Icon type="dingzhu" size="small" />
          </div>
        ) : null)}
    </div>
  );
};

export default AppCard;
