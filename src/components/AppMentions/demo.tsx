import React, { useState } from 'react';
import { Card, Typography } from 'antd';
import { AppMentions } from './index';

const { Title, Paragraph } = Typography;

const Demo: React.FC = () => {
  const [value, setValue] = useState<string>('');

  const handleChange = (newValue: string) => {
    setValue(newValue);
  };

  return (
    <Card style={{ maxWidth: 800, margin: '20px auto' }}>
      <Typography>
        <Title level={3}>应用提及组件演示</Title>
        <Paragraph>
          这个组件允许用户以下方式进行选择:
          <ul>
            <li>
              使用 <code>@</code> 选择应用
            </li>
            <li>
              使用 <code>#</code> 选择页面
            </li>
            <li>
              使用 <code>$</code> 选择数据源
            </li>
          </ul>
        </Paragraph>
      </Typography>

      <AppMentions value={value} onChange={handleChange} placeholder="试着输入 @ 选择应用、# 选择页面或 $ 选择数据源" />

      <div style={{ marginTop: 16 }}>
        <Typography>
          <Title level={5}>当前值:</Title>
          <Paragraph>
            <pre>{value || '(空)'}</pre>
          </Paragraph>
        </Typography>
      </div>
    </Card>
  );
};

export default Demo;
