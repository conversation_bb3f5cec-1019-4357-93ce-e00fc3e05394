import './index.less';

import { Mentions } from 'antd';
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';

import { getFormNavigationListByOrder } from '@/apis';

import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { getAppList } from '@/apis/getAppList';
import { useAIChatStore } from '@/store/aiChatStore';

export interface AppData {
  id: string;
  name: string;
  icon?: string;
}

export interface PageData {
  id: string;
  name: string;
  appId: string;
}

export interface DataSourceData {
  id: string;
  name: string;
  appId: string;
}

// 模拟获取数据源列表
const mockGetDataSourceList = async (appId: string): Promise<DataSourceData[]> => {
  // 模拟网络请求延迟
  await new Promise((resolve) => setTimeout(resolve, 500));
  return [
    { id: `${appId}_ds_1`, name: '用户表', appId },
    { id: `${appId}_ds_2`, name: '订单表', appId },
    { id: `${appId}_ds_3`, name: '产品表', appId },
  ];
};

export const AppMentions: React.FC<any> = (props: any) => {
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState(props.value || '');
  const [activePrefix, setActivePrefix] = useState<string | null>(null);
  const [activeAppId, setActiveAppId] = useState<string | null>(null);
  const [activePageId, setActivePageId] = useState<string | null>(null);
  const [activeDataSourceId, setActiveDataSourceId] = useState<string | null>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const setArtifactPreviewUrl = useAIArtifactStateStore((s) => s.setArtifactPreviewUrl);
  const setArtifactMode = useAIArtifactStateStore((s) => s.setArtifactMode);
  const isProcessing = useAIChatStore((s) => s.isProcessing);
  const { refName, onChange, ...restProps } = props;

  // Update value when props change
  useEffect(() => {
    setValue(props.value || '');
  }, [props.value]);

  // 监听预览地址更新事件
  useEffect(() => {
    const handlePreviewReady = (event: MessageEvent) => {
      if (event.data?.type === 'appReady' && event.data?.appId) {
        setActiveAppId(event.data.appId);
      }
    };
    window.addEventListener('message', handlePreviewReady);
    return () => {
      window.removeEventListener('message', handlePreviewReady);
    };
  }, []);

  // 应用列表
  const [appOptions, setAppOptions] = useState<AppData[]>([]);
  // 页面列表
  const [pageOptions, setPageOptions] = useState<PageData[]>([]);
  // 数据源列表
  const [dataSourceOptions, setDataSourceOptions] = useState<DataSourceData[]>([]);

  // 获取应用列表
  const fetchAppList = async () => {
    setLoading(true);
    try {
      const textBeforeCursor = getTextBeforeCursor();
      const key = textBeforeCursor.split('@').slice(-1)?.[0]?.split(' ')?.[0];
      const response = await getAppList({
        pageIndex: 1,
        pageSize: 10,
        orderField: 'data_gmt_create',
        appStatus: '',
        creator: window.loginUser?.userId,
        key,
      });
      if (response && response.data) {
        const appList: AppData[] = response.data.map((app: any) => ({
          id: app.appType,
          name: app.appName.zh_CN,
          icon: app.iconUrl || app.icon || '',
        }));
        return appList;
      }
      return [];
    } catch (error) {
      console.error('获取应用列表失败:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // 获取页面列表
  const fetchPageList = async (appId: string) => {
    setLoading(true);
    try {
      const pages = await getFormNavigationListByOrder({ appType: appId });
      const pagopts: any = pages.filter(({ type }) => ['receipt', 'display', 'view'].includes(type));
      setPageOptions(pagopts);
    } catch (error) {
      console.error('获取页面列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取数据源列表
  const fetchDataSourceList = async (appId: string) => {
    setLoading(true);
    try {
      const dataSources = await mockGetDataSourceList(appId);
      setDataSourceOptions(dataSources);
    } catch (error) {
      console.error('获取数据源列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTextBeforeCursor = () => {
    const input = refName?.current?.textarea;
    if (!input) return '';
    const cursorPos = input.selectionStart;
    const textBefore = input.value.substring(0, cursorPos);
    return textBefore;
  };

  const getPageType = (type: string): string => {
    switch (type) {
      case 'receipt':
        return '表单';
      case 'display':
        return '自定义页面';
      case 'view':
        return '数据管理页';
      default:
        return type;
    }
  };

  useEffect(() => {
    const textBeforeCursor = getTextBeforeCursor();
    const valueHaveAppQuoted = /@[^()]+\([^)]+\)/.test(value);
    if (valueHaveAppQuoted) {
      // allow # after one app mention
      if (/(#)$/.test(textBeforeCursor)) {
        setIsOpen(true);
        return;
      }
      setIsOpen(false);
      return;
    }
    if (/(@|#)$/.test(textBeforeCursor)) {
      setIsOpen(true);
    } else if (/\s$/.test(textBeforeCursor)) {
      setIsOpen(false);
    }
  }, [props.value]);

  useEffect(() => {
    (async () => {
      if (!activePrefix) return;
      if (!isOpen) return;
      if (activePrefix === '@') {
        const appList = await fetchAppList();
        setAppOptions(appList);
      } else if (activePrefix === '#' && activeAppId) {
        fetchPageList(activeAppId);
      } else if (activePrefix === '$' && activeAppId) {
        fetchDataSourceList(activeAppId);
      }
    })();
  }, [activePrefix, activeAppId, props.value, isOpen]);

  // 处理选择应用
  const handleSelect = (option: any) => {
    const { key } = option;
    if (activePrefix === '@') {
      const url = new URL(location.href);
      setActiveAppId(key);
      url.pathname = `/${key}/workbench/`;
      url.search = '';
      setArtifactPreviewUrl(url.toString());
      setArtifactMode('preview');
    } else if (activePrefix === '#') {
      setActivePageId(key);
      const preViewUrlExisted = useAIArtifactStateStore.getState().artifactPreviewUrl;
      const url = new URL(preViewUrlExisted);
      const pathSegments = url.pathname.split('/');
      const appKey = pathSegments[1];
      url.pathname = `/${appKey}/workbench/${key}`;
      url.search = '?navConfig.type=none';
      setArtifactPreviewUrl(url.toString());
      setArtifactMode('preview');
    } else {
      setActiveDataSourceId(key);
    }
  };

  // 渲染应用选项
  const renderAppLabel = (option: any) => {
    const { icon, name } = option;
    const isHttpIcon = /^http/.test(icon);
    const IconDom = isHttpIcon ? (
      <img src={`${icon}?x-oss-process=image/resize,w_100`} className="option-img" />
    ) : (
      <i
        className={`appList-icon iconfont icon-${icon.split('%%')[0]} option-icon`}
        style={{ backgroundColor: icon.split('%%')[1] }}
      />
    );
    return (
      <div className="mentions-option">
        {IconDom}
        <span className="option-name">{name}</span>
      </div>
    );
  };

  const renderPageLabel = (option: any) => {
    return (
      <div className="mentions-option">
        <span className="option-name">{option.title}</span>
      </div>
    );
  };

  const renderDataSourceLabel = (option: any) => {
    return (
      <div className="mentions-option">
        <span className="option-name">{option.name}</span>
      </div>
    );
  };

  const renderValue = (option: any) => {
    return `${option.name}(${option.id})`;
  };
  const renderPageValue = (option: any) => {
    return `${option.title}(${option.id},type:${getPageType(option.type)})`;
  };

  // 获取当前使用的选项列表
  const getOptions = () => {
    if (activePrefix === '@') {
      return appOptions.map((app) => ({ value: renderValue(app), label: renderAppLabel(app), key: app.id }));
    }
    if (activePrefix === '#') {
      return pageOptions.map((page) => ({ value: renderPageValue(page), label: renderPageLabel(page), key: page.id }));
    }
    if (activePrefix === '$') {
      return dataSourceOptions.map((ds) => ({ value: renderValue(ds), label: renderDataSourceLabel(ds), key: ds.id }));
    }
    return [];
  };

  const composeMentionValues = (oldValue: string, newValue: string) => {
    const isTriggeredByPage = oldValue.slice(-1) === '#';
    // Extract mentions and surrounding text
    const oldAppMatch = oldValue.match(/@[^()]+\([^)]+\)/);
    const oldApp = oldAppMatch ? oldAppMatch[0] : '';
    const oldPreText = oldValue.split(/@[^()]+\([^)]+\)/)[0]?.trim() || '';
    const oldPostText = oldValue.split(/@[^()]+\([^)]+\)/)[1]?.trim() || '';

    // Extract new # mention but antd is returning the @ by default we need to hotfix in this way
    const newPageMatch = newValue.match(/@[^()]+\([^)]+\)/);
    const newPage = newPageMatch ? newPageMatch[0] : '';

    if (isTriggeredByPage && oldApp && newPage) {
      const parts = [oldPreText, oldApp, oldPostText, newPage.slice(1)]
        .filter(Boolean)
        .join('')
        .replace(/\s+/g, ' ')
        .trim();
      return parts;
    }

    return newValue;
  };

  return (
    <Mentions
      {...restProps}
      className={isProcessing ? 'input-is-processing message-input' : 'message-input'}
      value={value}
      onChange={(newValue) => {
        const composedValue = composeMentionValues(value, newValue);
        // console.info('composedValue', composedValue);
        setValue(composedValue);
        onChange?.(composedValue);
      }}
      onCompositionStart={() => setIsComposing(true)}
      onCompositionEnd={() => setIsComposing(false)}
      onKeyDown={(e) => {
        if (isOpen) return;
        if (isComposing && e.key === ' ') {
          e.preventDefault();
          return;
        }
        props.onKeyDown?.(e);
      }}
      open={isOpen}
      loading={loading}
      prefix={['@', '#']}
      autoSize={{ minRows: 4, maxRows: 4 }}
      onSearch={(text, prefix) => {
        setActivePrefix(prefix);
      }}
      onSelect={(option: any) => {
        handleSelect(option);
      }}
      options={getOptions()}
      dropdownClassName="app-mentions-dropdown"
      ref={refName}
    />
  );
};
