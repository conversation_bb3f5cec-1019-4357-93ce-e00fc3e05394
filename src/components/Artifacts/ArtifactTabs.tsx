import './ArtifactTabs.less';

import { IconFont } from '@legao/icon-comp';
import { Segmented } from 'antd';
import { SegmentedValue } from 'antd/lib/segmented';
import React from 'react';

import { ArtifactMode, useAIArtifactStateStore } from '@/store/aiArtifactStore';

export const ArtifactTabs: React.FC<{}> = () => {
  const artifactMode = useAIArtifactStateStore((s) => s.artifactMode);
  const setArtifactMode = useAIArtifactStateStore((s) => s.setArtifactMode);

  const options = [
    {
      label: (
        <div>
          <IconFont name="data-analysis" />
          Logs
        </div>
      ),
      value: 'logger',
    },
    {
      label: (
        <div>
          <IconFont name="code" />
          Code
        </div>
      ),
      value: 'code',
    },
    {
      label: (
        <div>
          <IconFont name="preview" />
          Preview
        </div>
      ),
      value: 'preview',
    },
    {
      label: (
        <div>
          <IconFont name="file-text" />
          PRD
        </div>
      ),
      value: 'prd',
    },
  ];

  return (
    <div className="artifact-tabs">
      <Segmented
        options={options}
        value={artifactMode}
        onChange={(v) => {
          setArtifactMode(v as ArtifactMode);
        }}
      />
      {artifactMode === 'preview' && (
        <span
          className="print-icon"
          onClick={() => {
            setArtifactMode('print');
          }}
        >
          <IconFont name="print" />
        </span>
      )}
    </div>
  );
};
