/**
 * ErrorSection 组件
 */
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { useAIChatStore } from '@/store/aiChatStore';
import { Alert, Space, Button } from 'antd';
import React from 'react';

export const ErrorSection: React.FC = () => {
  const errorMsg = useAIArtifactStateStore((s) => s.errorMsg);
  if (!errorMsg) {
    return null;
  }
  return (
    <>
      <Alert message={errorMsg} type="error" />
      <Space>
        <Button
          type="primary"
          onClick={() => {
            useAIArtifactStateStore.getState().setErrorMsg('');
            useAIChatStore.getState().sendMessage(`重试生成`);
          }}
        >
          重试
        </Button>
        <Button
          type="primary"
          onClick={() => {
            useAIArtifactStateStore.getState().setErrorMsg('');
            useAIChatStore.getState().sendMessage(`请修复以下错误：${errorMsg}`);
          }}
        >
          尝试修复
        </Button>
      </Space>
    </>
  );
};
