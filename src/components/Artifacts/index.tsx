import './index.less';

import React, { useEffect, useRef } from 'react';

import { useAIArtifactStateStore } from '@/store/aiArtifactStore';

import { CanvasLogger } from '../Logger';
import { ErrorSection } from './errorSection';
import { CanvasCodeDisplay } from '../Code/code-display';
import { PrdEditor } from '../PrdEditor/prdEditor';

export const Artifact: React.FC<{}> = () => {
  const artifactMode = useAIArtifactStateStore((s) => s.artifactMode);
  const artifactPreviewUrl = useAIArtifactStateStore((s) => s.artifactPreviewUrl);
  const artifactInlineHtml = useAIArtifactStateStore((s) => s.artifactInlineHtml);
  const setIframeRef = useAIArtifactStateStore((s) => s.setPreviewIframeRef);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (artifactMode === 'print' && iframeRef.current) {
      iframeRef.current.srcdoc = `
<!DOCTYPE html>
<html>
<head>
  <title>Inline Print Preview</title>
</head>
<body>
  ${artifactInlineHtml}
</body>
</html>
`;
    }
  }, [artifactMode, artifactInlineHtml]);

  useEffect(() => {
    if (iframeRef.current) {
      setIframeRef(iframeRef.current);
    }
  }, [setIframeRef, iframeRef.current]);

  return (
    <>
      <ErrorSection />
      <iframe
        id="iframe-preview"
        src={artifactPreviewUrl}
        ref={iframeRef}
        style={{
          display: artifactMode === 'preview' || artifactMode === 'print' ? 'block' : 'none',
        }}
      />
      <div
        id="yida-code-field"
        style={{
          display: artifactMode === 'code' ? 'block' : 'none',
          height: 'calc(100% - 32x)',
        }}
      >
        <CanvasCodeDisplay />
      </div>
      <div
        id="logger-container"
        style={{
          display: artifactMode === 'logger' ? 'block' : 'none',
          height: 'calc(100% - 32px)',
        }}
      >
        <CanvasLogger />
      </div>
      <div
        id="prd-container"
        style={{
          height: 'calc(100% - 32px)',
          display: artifactMode === 'prd' ? 'block' : 'none',
        }}
      >
        <PrdEditor />
      </div>
    </>
  );
};
