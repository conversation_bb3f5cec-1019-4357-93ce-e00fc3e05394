.assistant-response-container {
  font-family: 苹方-简;
  font-size: 14px;
  font-weight: normal;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;

  .log-item-wrapper {
    width: 100%;
  }

  .assistant-response-item {
    line-height: 22px;
    color: #171a1d;
    font-size: 14px;

    &.response {
      &:first-child {
        margin-top: 0;
      }
    }

    &.file {
      border-radius: 6px;
      background: rgba(132, 139, 153, 0.08);
      display: flex;
      align-items: center;
      padding: 3px 4px;
      gap: 4px;
      a {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #666;
        &:hover {
          color: #0089ff;
        }
        text-decoration: none;
      }
      .file-icon {
        width: 22px;
        height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &.plan {
      overflow: hidden;
      border-radius: 6px;
      background: rgba(132, 139, 153, 0.08);
      border: 1px solid rgba(126, 134, 142, 0.16);
      backdrop-filter: blur(24px);
      .progress-bar-container {
        height: 4px;
        overflow: hidden;
        .progress-bar {
          height: 100%;
          background-color: #52c41a;
          transition: width 0.3s ease;
        }
      }
      .plan-header {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px;
        height: 72px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(132, 139, 153, 0.15);
        }

        .plan-icon {
          font-size: 18px;
          height: 48px;
          width: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          background: #e6e8ff;
          color: #405eff;
          overflow: hidden;
          border-radius: 12px;
          .next-icon::before,
          .iconfont::before {
            transition: all 0.3s ease;
          }
          &:hover {
            .next-icon::before,
            .iconfont::before {
              transform: scale(1.1);
            }
          }
          img {
            width: 48px;
            height: 48px;
            margin: 0;
          }
        }
        .plan-title {
          flex: 1;
          font-size: 16px;
          display: flex;
          flex-direction: column;
          line-height: 24px;
          overflow: hidden;
          .plan-title-text {
            height: 24px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:hover {
              color: #0089ff;
            }
          }
          .plan-progress {
            font-size: 14px;
            color: rgba(23, 26, 29, 0.4);
            .completed {
              color: #52c41a;
            }
          }
        }
        .right-icon {
          width: 48px;
          height: 48px;
          vertical-align: middle;
          display: flex;
          align-items: center;
          justify-content: center;

          .arrow-icon {
            color: #878f95;
            font-size: 16px;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.25);
            }
          }
        }
        .thinking-icon {
          width: 48px;
          height: 48px;
          background: url('//img.alicdn.com/imgextra/i2/O1CN01UtvKf51i0LvNp44v4_!!6000000004350-54-tps-72-72.apng')
            no-repeat center center;
          background-size: 20px 20px;
        }
      }

      .step-status-list {
        border-radius: 6px 6px 0px 0px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 20px 12px;
        transition: all 0.3s ease;
        overflow: hidden;
        &.expanded {
          height: auto;
        }
        &.collapsed {
          height: 0;
          padding: 0 12px;
        }

        .step-item {
          display: flex;
          flex-direction: column;
          gap: 8px;
          line-height: 20px;
          border-radius: 4px;
          .step-title {
            display: flex;
            align-items: center;
            gap: 8px;
            .step-icon {
              width: 20px;
              text-align: center;
            }
            .step-description {
              flex: 1;
              font-size: 14px;
              color: #171a1d;
            }
          }
          .step-files {
            margin: 2px 0 2px 32px;
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
          &.completed {
            .step-icon {
              color: #52c41a;
            }
          }
          &.in-progress {
            .step-icon {
              color: #1890ff;
            }
          }
          &.blocked {
            .step-icon {
              color: #f5222d;
            }
          }
          &.not-started {
            .step-icon {
              color: #8c8c8c;
            }
          }
        }
      }
    }

    &.plan-end {
      margin: 8px 0 12px;
      border-radius: 6px;
      background: rgba(132, 139, 153, 0.08);
      display: flex;
      height: 26px;
      align-items: center;
      padding: 4px 12px;
      gap: 12px;

      .execution-data {
        display: flex;
        flex-direction: row;
        font-size: 12px;
        color: rgba(23, 26, 29, 0.4);
        gap: 16px;
        line-height: 18px;
        .data-item {
          padding-right: 16px;
          position: relative;
          &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto 0;
            width: 1px;
            height: 12px;
            background-color: rgba(23, 26, 29, 0.4);
          }
          &:last-child:after {
            display: none;
          }
        }
      }
    }
  }
}
