import React, { useState } from 'react';
import { FeedbackLogItem, FeedbackType } from '@/utils/UpdatePrinterEffect';
import { Icon } from '@alifd/next';
import './index.scss';
import { FileData } from '@/utils/UserFeedbackCard';
import { PlanStatusData } from '@/types/types';
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { useAIChatStore } from '@/store/aiChatStore';

interface AssistantResponseProps {
  logs: FeedbackLogItem[] | string;
}

const mockLogs = [
  {
    type: 'response',
    message: '根据需求分析，为你制定如下计划：',
    timestamp: '2025-04-30T02:40:59.345Z',
  },
  {
    type: 'plan',
    message: '',
    data: {
      title: '语文老师作业内容智能图片识别与评分',
      completed: 3,
      total: 3,
      steps: [
        {
          description: '创建宜搭应用框架',
          status: 'completed',
        },
        {
          description: '设计并实现图片上传及文字识别表单页面',
          status: 'completed',
        },
        {
          description: '生成友好结果展示首页',
          status: 'completed',
        },
      ],
      // icon: 'xian-xinwen%%#FFA200',
      // iconUrl: 'https://tianshu-vpc.oss-cn-shanghai.aliyuncs.com/5e924b68-36a2-4728-8280-9288409a60c5.png',
      url: 'https://pre-yida-vpc.alibaba-inc.com/APP_LNMFWVOF2L2UYYDEYEQB/workbench',
      desc: '此应用允许教师上传学生的作业图片，系统将自动识别图片中的文字，并提供给教师进行评分和反馈。',
    },
    timestamp: '2025-04-30T02:43:35.687Z',
  },
  {
    type: 'plan_end',
    message: '计划步骤已全部执行完成，感谢你的耐心等待。',
    data: {
      totalTime: '2分56秒',
      stepsCompleted: 3,
      toolCallsCount: 4,
      planId: 'plan_ma3bwudz_1745980840174',
    },
    timestamp: '2025-04-30T02:43:35.697Z',
  },
];

const AssistantResponse: React.FC<AssistantResponseProps> = ({ logs }) => {
  // logs = mockLogs;
  const [expandedPlans, setExpandedPlans] = useState<boolean>(true);

  const togglePlanExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedPlans(!expandedPlans);
  };

  const formatAssResContent = (content: string) => {
    return content
      .replace?.(/!\[(.*?)\]\((.*?)\)/g, (a, b, c) => {
        return `<img src="${c}" alt="${b}" />`;
      })
      .replace(/\[(.*?)\]\(((http(s)?:)?\/\/.*?)\)/g, (a, b, c) => {
        return `<a href="${c}" target="_blank">${b}</a>`;
      });
  };

  const renderResponseLog = (log: FeedbackLogItem) => {
    return <div className="assistant-response-item response">{formatAssResContent(log.message)}</div>;
  };

  const renderFileLog = (data: FileData) => {
    const { type, icon, title, link } = data;
    const iconTypeMap = {
      file: 'Filemanage',
      app: 'application',
      page: 'page',
      form: 'form',
      other: 'Filemanage',
    };
    const iconType = icon || iconTypeMap[type] || 'Filemanage';
    const contentDom = (
      <>
        <div className="file-icon">
          <Icon type={iconType} size="small" />
        </div>
        <div className="file-message">{title}</div>
      </>
    );
    const handleClick = (e: any) => {
      e.preventDefault();
      const { isRightPanelVisible } = useAIChatStore.getState();
      if (['app', 'form', 'page'].includes(type) && isRightPanelVisible) {
        const { setArtifactMode } = useAIArtifactStateStore.getState();
        setArtifactMode('preview');
        window.postMessage(
          {
            type: 'previewReady',
            url: link,
          },
          '*',
        );
      } else {
        window.open(link, '_blank');
      }
    };
    const linkDom = link ? (
      <a href="" onClick={handleClick}>
        {contentDom}
      </a>
    ) : (
      contentDom
    );
    return <div className="assistant-response-item file">{linkDom}</div>;
  };

  const renderPlanLog = (log: FeedbackLogItem) => {
    const { data } = log as { data: PlanStatusData };
    const isExpanded = expandedPlans === true;
    const iconType = data.icon?.split('%%')[0] || 'qiyeguangchang';
    const iconbg = data.icon?.split('%%')[1] || '#e6e8ff';
    const iconColor = data.icon ? '#ffffff' : '#405eff';

    let iconDom = null;
    if (data.iconUrl) {
      iconDom = <img src={data.iconUrl} alt={data.title} />;
    } else if (data.icon) {
      iconDom = <i className={`iconfont icon-${iconType}`} style={{ fontSize: 24 }} />;
    } else {
      iconDom = <Icon type={iconType} size="large" />;
    }
    const openApp = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (!data.url) return;
      window.open(data.url, '_blank');
    };

    return (
      <div className="assistant-response-item plan">
        {data.completed !== undefined && data.total !== undefined && (
          <div className="progress-bar-container">
            <div
              className="progress-bar"
              style={{ width: `${Math.min(Math.round((data.completed / data.total) * 100), 100)}%` }}
            />
          </div>
        )}
        <div className="plan-header" title={data?.desc || data?.title} onClick={(e) => togglePlanExpand(e)}>
          <div
            className="plan-icon"
            onClick={openApp}
            style={{
              backgroundColor: iconbg,
              color: iconColor,
            }}
          >
            {iconDom}
          </div>
          <div className="plan-title">
            <div className="plan-title-text" title={data.title}>
              <span onClick={openApp}>{data.title}</span>
            </div>
            <div>
              {data.steps && (
                <span className="plan-progress">
                  {data.completed === data.steps.length ? (
                    <>
                      <Icon className="completed" type="success" size="small" /> 已完成
                    </>
                  ) : (
                    `计划进行中...(${data.completed || 0}/${data.total || data.steps.length})`
                  )}
                </span>
              )}
            </div>
          </div>
          <div className="right-icon">
            {data.completed === data.steps.length ? (
              <Icon type={isExpanded ? 'arrow-up' : 'arrow-down'} size="small" className="arrow-icon" />
            ) : (
              <div className="right-icon thinking-icon" />
            )}
          </div>
        </div>
        <div className={`step-status-list ${isExpanded ? 'expanded' : 'collapsed'}`}>
          {data.steps.map((step: any, index: number) => {
            let statusIcon = 'loading';
            let statusClass = 'not-started';
            switch (step.status) {
              case 'completed':
                statusIcon = 'success';
                statusClass = 'completed';
                break;
              case 'in_progress':
                statusIcon = 'loading';
                statusClass = 'in-progress';
                break;
              case 'blocked':
                statusIcon = 'error';
                statusClass = 'blocked';
                break;
              case 'not_started':
              default:
                statusIcon = 'clock';
                statusClass = 'not-started';
            }
            let stepDesc = typeof step === 'string' ? step : step.description;
            if (!/^\d/.test(stepDesc.trim())) {
              stepDesc = `${index + 1}. ${stepDesc}`;
            }
            const stepId = `step-${step.id || step.description || index}`;
            return (
              <div key={stepId} className={`step-item ${statusClass}`}>
                <div className="step-title">
                  <div className="step-icon">
                    <Icon type={statusIcon} size="small" />
                  </div>
                  <div className="step-description">{stepDesc}</div>
                </div>
                {step.files?.length && (
                  <div className="step-files">{step.files?.map((file: FileData) => renderFileLog(file))}</div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderPlanEndLog = (log: FeedbackLogItem) => {
    const { message, data: executionData } = log;
    return (
      <>
        {executionData && (
          <div className="assistant-response-item plan-end">
            <div className="execution-data">
              <div className="data-item">总时长: {executionData.totalTime}</div>
              <div className="data-item">完成步骤: {executionData.stepsCompleted}</div>
              <div className="data-item">工具调用: {executionData.toolCallsCount}次</div>
            </div>
          </div>
        )}
        <div className="assistant-response-item response">{message}</div>
      </>
    );
  };

  const renderLogItem = (log: FeedbackLogItem) => {
    switch (log.type) {
      case FeedbackType.RESPONSE:
        return renderResponseLog(log);
      case FeedbackType.FILE:
        return renderFileLog({
          ...log.data,
          title: log.message || log.data.title,
        });
      case FeedbackType.PLAN:
        return renderPlanLog(log);
      case FeedbackType.PLAN_END:
        return renderPlanEndLog(log);
      default:
        return <div className="assistant-response-item default">{log.message}</div>;
    }
  };

  let tarNode = null;
  if (typeof logs === 'string') {
    tarNode = <div className="assistant-response-item default">{logs}</div>;
  } else {
    tarNode = logs.map((log: FeedbackLogItem, index: number) => {
      const logId = `log-${log.type}-${index}`;
      return (
        <div key={logId} className="log-item-wrapper">
          {renderLogItem(log)}
        </div>
      );
    });
  }
  return <div className="assistant-response-container">{tarNode}</div>;
};

export default AssistantResponse;
