.chatbox-container {
  border: 1px solid transparent;
  &:has(.ant-mentions-focused) {
    border-radius: 12px;
    -webkit-animation: light-gradient-border-rotate 2.5s linear infinite;
    animation: light-gradient-border-rotate 2.5s linear infinite;
    background-origin: padding-box, border-box;
    background-clip: padding-box, border-box;
    background-image: -webkit-gradient(linear, left top, right top, from(#fff), to(#fff)),
      conic-gradient(
        from 0deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, #fff, #fff),
      conic-gradient(
        from 0deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, #fff, #fff),
      conic-gradient(
        from 0deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }
  .input-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    border-radius: 12px;
    background: #ffffff;
    padding: 16px 12px 12px;
    border: 1px solid rgba(126, 134, 142, 0.16);
    box-shadow: 2px 3px 5px rgba(9, 24, 70, 0.02);
    transition: all var(--transition-speed) ease;
    overflow: hidden;
    &:has(.ant-mentions-focused) {
      border: 1px solid rgba(126, 134, 142, 0.3);
      box-shadow: 2px 3px 5px rgba(78, 110, 242, 0.2) !important;
    }
    &:has(.ant-mentions-disabled) {
      background-color: #f1f1f1;
      .ant-mentions-disabled {
        background: none !important;
      }
    }
  }
  /* 输入框样式 */
  .message-input {
    flex: 1;
    padding: 0 !important;
    min-height: 50px !important;
    border: none !important;
    box-shadow: none !important;
    textarea {
      padding: 0 6px !important;
    }
  }
  .bottom-actions {
    display: flex;
    align-items: center;
    justify-items: center;
    gap: 12px;
    flex-direction: row;
    height: 20px;
    .vline {
      width: 1px;
      height: 12px;
      background: rgba(131, 137, 143, 0.24);
    }
    .image-upload-button {
      margin-left: 6px;
      color: gray;
      height: 20px;
    }
    .attachment-container {
      max-height: 20px;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      gap: 10px;
      .attachment {
        position: relative;
        img {
          width: 20px;
          height: 20px;
          border-radius: 4px;
        }
        .attachment-delete {
          position: absolute;
          top: -2px;
          right: -2px;
          width: 8px;
          height: 8px;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
          opacity: 0.8;
          transition: opacity 0.2s;
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
  /* 发送按钮样式 */
  .send-button {
    position: absolute;
    right: 16px;
    bottom: 16px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      transform: scale(1.1);
    }
    &.actived img {
      opacity: 1;
      filter: none;
    }
    img {
      opacity: 0.4;
      transition: all 0.5s ease;
      filter: grayscale(100%);
      width: 20px;
      height: 20px;
    }
    &.running .next-icon {
      color: #ff4d4f !important;
    }
    &:disabled {
      filter: grayscale(100%);
    }
  }
}

@-webkit-keyframes light-gradient-border-rotate {
  0% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 180deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 180deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 180deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 180deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 180deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 180deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  5% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 198deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 198deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 198deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 198deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 198deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 198deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  10% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 216deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 216deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 216deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 216deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 216deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 216deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  15% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 234deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 234deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 234deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 234deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 234deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 234deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  20% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 252deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 252deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 252deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 252deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 252deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 252deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  25% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 270deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 270deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 270deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 270deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 270deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 270deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  30% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 288deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 288deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 288deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 288deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 288deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 288deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  35% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 306deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 306deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 306deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 306deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 306deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 306deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  40% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 324deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 324deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 324deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 324deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 324deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 324deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  45% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 342deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 342deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 342deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 342deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 342deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 342deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  50% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 1turn,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 1turn,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 1turn,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 1turn,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 1turn,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 1turn,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  55% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 378deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 378deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 378deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 378deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 378deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 378deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  60% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 396deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 396deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 396deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 396deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 396deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 396deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  65% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 414deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 414deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 414deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 414deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 414deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 414deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  70% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 432deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 432deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 432deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 432deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 432deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 432deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  75% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 450deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 450deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 450deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 450deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 450deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 450deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  80% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 468deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 468deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 468deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 468deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 468deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 468deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  85% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 486deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 486deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 486deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 486deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 486deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 486deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  90% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 504deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 504deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 504deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 504deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 504deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 504deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  95% {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 522deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 522deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 522deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 522deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 522deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 522deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }

  to {
    background-image: -webkit-gradient(linear, left top, right top, from(transparent), to(transparent)),
      conic-gradient(
        from 540deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(left, transparent, transparent),
      conic-gradient(
        from 540deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(90deg, transparent, transparent),
      conic-gradient(
        from 540deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-gradient(
        linear,
        left top,
        right top,
        from(var(--im_chat_message_other_bg_color, transparent)),
        to(var(--im_chat_message_other_bg_color, transparent))
      ),
      conic-gradient(
        from 540deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: -webkit-linear-gradient(
        left,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 540deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
    background-image: linear-gradient(
        90deg,
        var(--im_chat_message_other_bg_color, transparent),
        var(--im_chat_message_other_bg_color, transparent)
      ),
      conic-gradient(
        from 540deg,
        #ffbaf6 0,
        #b9adff 60deg,
        #80bfff 120deg,
        #adf7ff 180deg,
        #e9ccff 240deg,
        #ffc29c 300deg,
        #ffbaf6 1turn
      );
  }
}

.quick-action:hover .title,
.animatedText {
  color: transparent !important;
  background: linear-gradient(
      270deg,
      rgba(38, 36, 76, 0.88) 12%,
      rgba(38, 36, 76, 0.5) 36%,
      rgba(255, 255, 255, 0.9) 48%,
      rgba(122, 121, 138, 0.5) 60%,
      rgba(38, 36, 76, 0.88) 92%
    )
    0 0/200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: 3s linear infinite softlight;
}

@keyframes softlight {
  0% {
    background-position: 100% 0;
  }

  to {
    background-position: -100% 0;
  }
}
