import React, { useCallback } from 'react';
import { Button, Icon, Select } from '@alifd/next';
import './index.scss';
import { useAIChatStore } from '@/store/aiChatStore';
import { AppMentions } from '../AppMentions';
import { agentConfigList } from '@/store/aiArtifactStore';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { UploadFile } from '@/utils/UploadFile';
import { uniqueId } from '@ali/ve-utils';
import { PolyMindUploader } from '@/utils/PolymindUploader';
import { readFile } from '@/utils/Xlsx';

interface ChatBoxInputProps {
  chatManagerRef?: any;
  inputRef?: any;
  onSend?: () => void;
}

const ChatBoxInput: React.FC<ChatBoxInputProps> = ({ chatManagerRef, inputRef, onSend }) => {
  const inputValue = useAIChatStore((s) => s.chatInputValue);
  const isProcessing = useAIChatStore((s) => s.isProcessing);
  const attachments = useAIChatStore((s) => s.attachments);
  const setAttachments = useAIChatStore((s) => s.setAttachments);
  const setInputValue = useAIChatStore((s) => s.setChatInputValue);
  const countdownInterval = useAIChatStore((s) => s.countdownInterval);
  const setCountdownInterval = useAIChatStore((s) => s.setCountdownInterval);
  const setIsUserFocusedOnInput = useAIChatStore((s) => s.setIsUserFocusedOnInput);
  const setCountdown = useAIChatStore((s) => s.setCountdown);
  const currentAgent = useAIChatStore((s) => s.agentType);
  const setCurrentAgent = useAIChatStore((s) => s.setAgentType);
  const handleSendMessage = useAIChatStore((s) => s.sendMessage);

  // 处理输入框获得焦点
  const handleInputFocus = useCallback(() => {
    setIsUserFocusedOnInput(true);
  }, []);

  // 处理输入框失去焦点
  const handleInputBlur = useCallback(() => {
    setIsUserFocusedOnInput(false);
  }, []);

  // 处理按键事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend?.();
      handleSendMessage();
    }
  };

  // 处理输入框内容变化
  const handleInputChange = useCallback(
    (value: string) => {
      setInputValue(value);
      // 当用户开始输入时，如果有倒计时，则取消倒计时
      if (value.trim() && countdownInterval) {
        clearInterval(countdownInterval);
        setCountdownInterval(null);
        setCountdown(30); // 重置倒计时
        // 调用后端的 onInputStart 回调
        if (globalThis.waitingForUserResponse?.onInputStart) {
          globalThis.waitingForUserResponse.onInputStart();
        }
      }
    },
    [countdownInterval, setInputValue],
  );

  // 处理图片上传
  const handleImageUpload = () => {
    // 创建一个隐藏的文件输入框
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = true;
    fileInput.accept = '.csv, .xls, .xlsx, image/*';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    // 监听文件选择事件
    fileInput.onchange = async (e) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        // 发布到公网
        let pmArr = [] as any;
        for (let i = 0; i < target.files.length; i++) {
          const oneFile = target.files[i];
          if (/\.(csv|xls|xlsx)$/.test(oneFile.name)) {
            pmArr.push(readFile(oneFile));
            // const polyMindUploader = new PolyMindUploader();
            // pmArr.push(polyMindUploader.uploadFile(oneFile));
          } else {
            pmArr.push(UploadFile(oneFile));
          }
        }
        const resArr = await Promise.all(pmArr);
        const addAttach = [] as any;
        resArr.forEach((res) => {
          if (!res?.success) return;
          const tarFile: any = res.file;
          if (/\.(csv|xls|xlsx)$/.test(tarFile.name)) {
            addAttach.push({
              id: `file${uniqueId(null, '', '')}`,
              // url: `${tarFile.url}`,
              imgURL: 'https://gw.alicdn.com/imgextra/i1/O1CN01DfYN4k1EYIoARTgh6_!!6000000000363-2-tps-160-160.png',
              type: 'file',
              name: tarFile.name,
              size: tarFile.size,
              sheetName: res.sheetName,
              allData: res.allData,
              metaData: res.metaData,
            });
          } else {
            addAttach.push({
              id: `image${uniqueId(null, '', '')}`,
              url: `${tarFile.url}?x-oss-process=image/resize,w_100`,
              type: 'image',
              name: tarFile.name,
              size: tarFile.size,
              objectName: tarFile.objectName,
            });
          }
        });
        setAttachments([...attachments, ...addAttach]);
      }
      // 使用完后移除文件输入框
      document.body.removeChild(fileInput);
    };
    // 触发文件选择对话框
    fileInput.click();
  };
  // 删除附件
  const handleDeleteAttachment = (id: string) => {
    setAttachments(attachments.filter((attachment) => attachment.id !== id));
  };

  return (
    <div className="chatbox-container">
      <div className="input-container">
        <AppMentions
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={
            isProcessing && !globalThis.waitingForUserResponse
              ? '正在运行中，请稍后...'
              : '请描述你的需求，我来帮你解决...'
          }
          // disabled={isProcessing}
          refName={inputRef}
        />
        <div className="bottom-actions">
          <Select
            autoWidth={false}
            style={{ width: 170 }}
            size="small"
            value={currentAgent}
            onChange={setCurrentAgent}
            disabled={isProcessing && !globalThis.waitingForUserResponse}
            popupClassName="select-mode"
            dataSource={agentConfigList}
            itemRender={(v) => (
              <>
                <Icon size="small" type={v.icon as string} /> {v.label}
              </>
            )}
            valueRender={(v) => (
              <>
                <Icon size="small" type={v.icon as string} /> {v.label}
              </>
            )}
          />
          <div className="vline" />
          <Button
            className="image-upload-button"
            type="normal"
            text
            onClick={handleImageUpload}
            disabled={isProcessing && !globalThis.waitingForUserResponse}
            style={{ marginRight: '4px' }}
          >
            <Icon type="picture" size="medium" />
          </Button>
          <div className="attachment-container">
            {attachments.map((attachment: any) => (
              <div key={attachment.id} className="attachment">
                <img src={attachment.imgURL || attachment.url} title={attachment.name} />
                <div className="attachment-delete" onClick={() => handleDeleteAttachment(attachment.id)}>
                  <Icon type="close" size="xs" />
                </div>
              </div>
            ))}
          </div>
          <Button
            type="primary"
            text
            onClick={() => {
              if (isProcessing && !globalThis.waitingForUserResponse) {
                // 结束运行
                if (chatManagerRef?.current) {
                  chatManagerRef?.current?.stopExecution();
                  // 停止当前正在运行的代理（真正运行代理）
                  useAIAgentStateStore.getState().currentExecutingAgent?.stopExecution();
                }
              } else {
                onSend?.();
                handleSendMessage();
              }
            }}
            iconSize={'medium'}
            className={`
              send-button
              ${inputValue ? 'actived' : ''}
              ${isProcessing && !globalThis.waitingForUserResponse ? 'running' : ''}
            `}
            aria-label={isProcessing && !globalThis.waitingForUserResponse ? '结束运行' : '发送'}
          >
            {isProcessing && !globalThis.waitingForUserResponse ? (
              <Icon type="player-stop" style={{ marginRight: 0 }} />
            ) : (
              <img
                src="//img.alicdn.com/imgextra/i4/O1CN01adFwFy1VU2bOy0Rlu_!!6000000002655-55-tps-48-48.svg"
                alt="发送"
              />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatBoxInput;
