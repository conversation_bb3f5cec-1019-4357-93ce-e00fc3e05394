.template-dialog-content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .template-search {
    padding: 0 16px 0 0;
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  .next-tabs {
    flex: 1;
    display: flex;
    min-height: 0;

    .next-tabs-bar {
      width: 120px;
      border-right: 1px solid #e8e8e8;
      overflow-y: auto;
      flex-shrink: 0;
      height: 500px;
    }

    .next-tabs-content {
      flex: 1;
      min-height: 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 500px;

      .next-tabs-tabpane {
        overflow-y: auto;
        min-height: 0;
        height: 500px;
      }
    }
  }

  .industry-section {
    padding: 0 16px;
    height: 100%;

    .scenarios-list {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 16px;

      .scenario-card {
        background: #f7f8fa;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 16px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
          border-color: #dcdee3;
        }

        .scenario-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .title {
            font-weight: 500;
            font-size: 14px;
            color: #333;
          }

          .action-buttons {
            .use-btn {
              color: #0064c8;

              &:hover {
                color: #0052a3;
                background: rgba(0, 100, 200, 0.04);
              }
            }
          }
        }

        .scenario-content {
          font-size: 13px;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }
}
