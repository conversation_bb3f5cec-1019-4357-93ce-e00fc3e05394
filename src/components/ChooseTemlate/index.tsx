import React, { useState } from 'react';
import { <PERSON>alog, Tab, Button, Input, Icon } from '@alifd/next';
import { templateMap } from './template';
import './index.less';
import { useAIChatStore } from '@/store/aiChatStore';

interface ChooseTemplateProps {
  onSelectTemplate: (promptText: string) => boolean;
}

const ChooseTemplate: React.FC<ChooseTemplateProps> = ({ onSelectTemplate }) => {
  const [tabKey, setTabKey] = useState(templateMap[0].enname);
  const [searchText, setSearchText] = useState('');
  const [templateDialogVisible, setTemplateDialogVisible] = useState(false);
  const currentAgent = useAIChatStore((s) => s.agentType);

  // 过滤场景
  const filterScenarios = (scenarios: (typeof templateMap)[0]['scenarios']) => {
    if (!searchText.trim()) return scenarios;

    const searchValue = searchText.trim();
    return scenarios.filter(
      (scenario) => scenario.name.includes(searchValue) || scenario.description.includes(searchValue),
    );
  };

  // 处理搜索输入
  const handleSearch = (value: string) => {
    setSearchText(value);
    // 如果搜索结果为空，自动切换到第一个有结果的标签页
    const firstTabWithResults = templateMap.find((industry) => {
      const filteredScenarios = industry.scenarios.filter(
        (scenario) => scenario.name.includes(value.trim()) || scenario.description.includes(value.trim()),
      );
      return filteredScenarios.length > 0;
    });

    if (firstTabWithResults) {
      setTabKey(firstTabWithResults.enname);
    }
  };

  return (
    <>
      <Button text onClick={() => setTemplateDialogVisible(true)}>
        <Icon type="zidong" size="small" /> 应用广场
      </Button>
      <Dialog
        v2
        title="应用广场：200+场景应用"
        visible={templateDialogVisible}
        width={800}
        height={730}
        centered
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setTemplateDialogVisible(false);
            }}
          >
            关闭
          </Button>,
        ]}
        onClose={() => setTemplateDialogVisible(false)}
      >
        <div className="template-dialog-content">
          <div className="template-search">
            <Input placeholder="搜索场景..." value={searchText} onChange={handleSearch} hasClear />
          </div>
          <Tab activeKey={tabKey} shape="wrapped" tabPosition="left" onChange={(key: string) => setTabKey(key)}>
            {templateMap.map((industry) => {
              const filteredScenarios = filterScenarios(industry.scenarios);
              if (searchText && filteredScenarios.length === 0) return null;
              return (
                <Tab.Item title={industry.name} key={industry.enname}>
                  <div className="industry-section">
                    <div className="scenarios-list">
                      {filteredScenarios.map((scenario, index) => (
                        <div key={index} className="scenario-card">
                          <div className="scenario-header">
                            <span className="title">{scenario.name}</span>
                            <div className="action-buttons">
                              <Button
                                text
                                type="primary"
                                className="use-btn"
                                size="small"
                                onClick={() => {
                                  const res = onSelectTemplate(scenario.description);
                                  if (res) {
                                    setTemplateDialogVisible(false);
                                  }
                                }}
                              >
                                使用提示词
                              </Button>
                            </div>
                          </div>
                          <div className="scenario-content">{scenario.description}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Tab.Item>
              );
            })}
          </Tab>
        </div>
      </Dialog>
    </>
  );
};

export default ChooseTemplate;
