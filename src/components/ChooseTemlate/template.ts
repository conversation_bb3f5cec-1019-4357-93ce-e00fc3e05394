export const templateMap = [
  {
    name: '制造业',
    enname: 'manufacturing',
    scenarios: [
      {
        name: '设备巡检',
        description:
          '我是一名工厂设备管理员，需要创建一个设备信息智能图片识别应用。希望通过拍照上传设备标签图片，自动识别出设备名称、设备序列号、生产日期等信息，方便我们进行日常巡检记录。',
      },
      {
        name: '质量控制',
        description:
          '我是质检部主管，想要开发一个产品缺陷智能图片识别应用。需要上传产品图片后，能自动识别产品表面的划痕、裂纹、颜色偏差等缺陷，提高质检效率。',
      },
      {
        name: '库存管理',
        description:
          '作为仓库管理员，我需要一个物料信息智能图片识别应用。通过拍摄物料标签，自动识别并记录物料名称、数量、存放位置等信息，实现快速入库和盘点。',
      },
      {
        name: '设备维护',
        description:
          '我是设备维修工程师，希望开发一个设备状态智能图片识别应用。拍摄设备运行状态图片后，能自动识别并判断是否需要维护或更换零件，帮助及时发现设备隐患。',
      },
      {
        name: '生产监控',
        description:
          '作为生产线主管，我需要一个安全操作智能图片识别应用。通过上传生产现场图片，自动识别工人的操作是否符合安全规范，及时发现并纠正不规范行为。',
      },
      {
        name: '产品包装检测',
        description:
          '我是包装车间主管，需要一个包装质量智能图片识别应用。上传产品包装图片后，自动检测包装的完整性，识别标签、封口等是否符合标准。',
      },
      {
        name: '工艺参数监控',
        description:
          '作为工艺工程师，我想要一个参数数据智能图片识别应用。通过拍摄设备显示屏，自动识别并记录各项工艺参数数据，实现生产过程的实时监控和分析。',
      },
      {
        name: '原材料检验',
        description:
          '我是原料检验员，需要一个材料特征智能图片识别应用。上传原材料图片后，自动识别其外观、尺寸、颜色等特征，判断是否符合进料标准。',
      },
    ],
  },
  {
    name: '教育',
    enname: 'education',
    scenarios: [
      {
        name: '作业批改',
        description:
          '我是一名语文老师，想要一个作业内容智能图片识别应用。上传学生作业的图片后，能自动识别作业内容并给出评分，帮助我提高批改效率。',
      },
      {
        name: '题目识别',
        description:
          '作为一名数学老师，我需要一个题目解析智能图片识别应用。通过拍照上传题目图片，自动识别题目内容并生成解题思路和答案，辅助教学使用。',
      },
      {
        name: '试卷分析',
        description:
          '我是班主任，希望开发一个试卷内容智能图片识别应用。上传学生试卷图片后，自动分析答题情况，识别出学生的知识点掌握程度和薄弱环节。',
      },
      {
        name: '教材扫描',
        description:
          '作为教研组长，我需要一个教材内容智能图片识别应用。通过拍摄教材页面，自动识别文字和图表内容，生成可编辑的电子版教材。',
      },
      {
        name: '课堂监控',
        description:
          '我是教务主任，想要一个课堂行为智能图片识别应用。上传课堂照片后，自动分析学生的专注度和参与度，帮助教师改进教学方法。',
      },
      {
        name: '实验记录',
        description:
          '作为物理实验老师，我需要一个实验数据智能图片识别应用。通过拍摄实验过程，自动识别实验现象和数据，帮助学生快速生成实验报告。',
      },
      {
        name: '图表解析',
        description:
          '我是地理老师，想要一个图表内容智能图片识别应用。上传教学图表后，自动识别图表内容，生成详细的图表讲解和分析说明。',
      },
      {
        name: '手写公式识别',
        description:
          '作为数学教研组长，我需要一个公式转换智能图片识别应用。拍摄手写数学公式后，自动识别并转换为规范的数学表达式，方便教学使用。',
      },
    ],
  },
  {
    name: '零售',
    enname: 'retail',
    scenarios: [
      {
        name: '商品识别',
        description:
          '我是超市经理，需要一个商品信息智能图片识别应用。通过拍摄货架照片，自动识别商品种类和数量，实时更新库存信息。',
      },
      {
        name: '价格标签识别',
        description:
          '作为卖场主管，我想要一个价格信息智能图片识别应用。拍摄商品价格标签后，自动识别价格信息，确保价签与系统价格一致。',
      },
      {
        name: '顾客行为分析',
        description:
          '我是零售分析师，需要一个行为分析智能图片识别应用。上传店内监控图片，自动分析顾客的购物行为和动线，优化商品陈列。',
      },
      {
        name: '商品推荐',
        description:
          '作为导购主管，我想要一个商品关联智能图片识别应用。拍摄顾客购买的商品，自动分析并推荐相关联的商品，提升销售转化。',
      },
      {
        name: '自助结账',
        description:
          '我是便利店老板，需要一个商品结算智能图片识别应用。通过拍照识别商品，自动生成购物清单和账单，方便顾客自助结账。',
      },
      {
        name: '货架陈列分析',
        description:
          '作为陈列管理员，我需要一个陈列规范智能图片识别应用。上传货架照片后，自动分析商品摆放的规范性和合理性。',
      },
      {
        name: '促销展示检查',
        description:
          '我是促销主管，想要一个促销陈列智能图片识别应用。拍摄店内促销区域照片，自动检查促销商品的陈列是否符合标准。',
      },
      {
        name: '商品损坏检测',
        description:
          '作为质检专员，我需要一个商品质量智能图片识别应用。通过拍照识别商品外观，自动检测是否有破损、变质等问题。',
      },
    ],
  },
  {
    name: '医疗',
    enname: 'medical',
    scenarios: [
      {
        name: '病历识别',
        description:
          '我是医生，需要一个病历信息智能图片识别应用。通过拍摄病历照片，自动识别并提取关键信息，提高诊疗效率。',
      },
      {
        name: '影像诊断',
        description:
          '作为放射科医生，我想要一个医学影像智能图片识别应用。上传X光片、CT等影像，自动分析并辅助诊断疾病。',
      },
      {
        name: '药品识别',
        description: '我是药剂师，需要一个药品信息智能图片识别应用。拍摄药品包装，自动识别药品名称、规格和用法用量。',
      },
      {
        name: '手术导航',
        description: '作为外科医生，我需要一个手术导航智能图片识别应用。通过实时图像识别，辅助手术定位和操作。',
      },
      {
        name: '皮肤诊断',
        description: '我是皮肤科医生，想要一个皮肤病变智能图片识别应用。拍摄皮肤照片，自动分析可能的病变类型。',
      },
      {
        name: '伤口评估',
        description: '作为急诊医生，我需要一个伤口评估智能图片识别应用。拍摄伤口照片，自动分析伤口类型和严重程度。',
      },
      {
        name: '康复训练',
        description: '我是康复师，需要一个动作识别智能图片识别应用。通过视频识别患者动作，评估康复训练效果。',
      },
      {
        name: '患者监护',
        description: '作为护士，我想要一个患者状态智能图片识别应用。通过监控图像识别，实时监测患者生命体征。',
      },
    ],
  },
  {
    name: '物流',
    enname: 'logistics',
    scenarios: [
      {
        name: '包裹识别',
        description:
          '我是快递员，需要一个包裹信息智能图片识别应用。通过拍摄包裹照片，自动识别包裹类型和重量，提高分拣效率。',
      },
      {
        name: '地址识别',
        description:
          '作为分拣员，我想要一个地址信息智能图片识别应用。拍摄快递单照片，自动识别收件人地址，辅助包裹分拣。',
      },
      {
        name: '车辆调度',
        description: '我是调度主管，需要一个车辆状态智能图片识别应用。通过监控图像识别，实时掌握车辆位置和状态。',
      },
      {
        name: '仓库管理',
        description: '作为仓库管理员，我需要一个库存管理智能图片识别应用。拍摄货架照片，自动识别商品数量和位置。',
      },
      {
        name: '配送路线规划',
        description: '我是配送主管，想要一个路线规划智能图片识别应用。通过图像识别分析路况，优化配送路线。',
      },
      {
        name: '货物损坏检测',
        description: '作为质检员，我需要一个货物质量智能图片识别应用。拍摄货物照片，自动检测是否有破损、变形等问题。',
      },
      {
        name: '装卸监控',
        description: '我是装卸主管，需要一个装卸过程智能图片识别应用。通过视频识别，监控装卸过程的安全性。',
      },
      {
        name: '包装检查',
        description: '作为包装专员，我想要一个包装规范智能图片识别应用。拍摄包装照片，自动检查是否符合规范要求。',
      },
    ],
  },
  {
    name: '金融',
    enname: 'finance',
    scenarios: [
      {
        name: '票据识别',
        description:
          '我是银行柜员，需要一个票据信息智能图片识别应用。通过拍摄票据照片，自动识别票据类型和金额，提高处理效率。',
      },
      {
        name: '身份验证',
        description:
          '作为风控专员，我想要一个身份验证智能图片识别应用。拍摄身份证照片，自动识别身份信息，进行身份核验。',
      },
      {
        name: '签名识别',
        description: '我是业务经理，需要一个签名验证智能图片识别应用。拍摄签名照片，自动识别签名真伪，防范风险。',
      },
      {
        name: '合同识别',
        description: '作为法务专员，我需要一个合同信息智能图片识别应用。拍摄合同照片，自动识别关键条款，辅助审核。',
      },
      {
        name: '支票识别',
        description: '我是出纳，想要一个支票信息智能图片识别应用。拍摄支票照片，自动识别金额和日期，提高处理效率。',
      },
      {
        name: '银行卡识别',
        description:
          '作为客服专员，我需要一个银行卡信息智能图片识别应用。拍摄银行卡照片，自动识别卡号信息，辅助业务办理。',
      },
      {
        name: '印章识别',
        description: '我是印章管理员，需要一个印章验证智能图片识别应用。拍摄印章照片，自动识别印章真伪，防范风险。',
      },
      {
        name: '单据管理',
        description: '作为会计，我想要一个单据管理智能图片识别应用。拍摄单据照片，自动识别并归档，提高工作效率。',
      },
    ],
  },
  {
    name: '农业',
    enname: 'agriculture',
    scenarios: [
      {
        name: '作物识别',
        description: '我是农场主，需要一个作物生长智能图片识别应用。通过拍摄作物照片，自动识别生长状态和病虫害情况。',
      },
      {
        name: '土壤分析',
        description: '作为农技专家，我想要一个土壤状况智能图片识别应用。拍摄土壤照片，自动分析土壤质地和养分含量。',
      },
      {
        name: '病虫害防治',
        description: '我是植保员，需要一个病虫害识别智能图片识别应用。拍摄作物照片，自动识别病虫害类型，指导防治。',
      },
      {
        name: '产量预测',
        description: '作为农业专家，我需要一个产量预测智能图片识别应用。拍摄农田照片，自动分析作物长势，预测产量。',
      },
      {
        name: '灌溉管理',
        description: '我是灌溉管理员，想要一个灌溉监测智能图片识别应用。通过图像识别，监测土壤墒情，优化灌溉方案。',
      },
      {
        name: '农机作业',
        description: '作为农机手，我需要一个作业导航智能图片识别应用。通过图像识别，辅助农机精准作业。',
      },
      {
        name: '农产品质检',
        description: '我是质检员，需要一个品质检测智能图片识别应用。拍摄农产品照片，自动检测外观品质。',
      },
      {
        name: '温室监控',
        description: '作为温室管理员，我想要一个环境监控智能图片识别应用。通过图像识别，监测温室环境参数。',
      },
    ],
  },
  {
    name: '建筑',
    enname: 'construction',
    scenarios: [
      {
        name: '施工监控',
        description: '我是项目经理，需要一个施工安全智能图片识别应用。通过监控图像识别，实时监测施工现场安全状况。',
      },
      {
        name: '质量检测',
        description: '作为质检员，我想要一个工程质量智能图片识别应用。拍摄施工部位照片，自动检测施工质量。',
      },
      {
        name: '进度跟踪',
        description: '我是监理工程师，需要一个进度监测智能图片识别应用。通过图像识别，跟踪施工进度完成情况。',
      },
      {
        name: '材料管理',
        description: '作为材料管理员，我需要一个材料识别智能图片识别应用。拍摄材料照片，自动识别材料类型和数量。',
      },
      {
        name: '设备监控',
        description: '我是设备主管，想要一个设备状态智能图片识别应用。通过图像识别，监控施工设备运行状态。',
      },
      {
        name: '人员管理',
        description: '作为安全主管，我需要一个人员识别智能图片识别应用。通过图像识别，统计和管理施工人员。',
      },
      {
        name: '环境监测',
        description: '我是环保专员，需要一个环境监测智能图片识别应用。通过图像识别，监测施工环境影响。',
      },
      {
        name: '图纸识别',
        description: '作为技术员，我想要一个图纸信息智能图片识别应用。拍摄图纸照片，自动识别关键信息。',
      },
    ],
  },
  {
    name: '交通',
    enname: 'transportation',
    scenarios: [
      {
        name: '车牌识别',
        description: '我是交警，需要一个车牌信息智能图片识别应用。通过拍摄车辆照片，自动识别车牌号码，提高执法效率。',
      },
      {
        name: '交通监控',
        description: '作为交通管理员，我想要一个路况监测智能图片识别应用。通过监控图像识别，实时掌握交通状况。',
      },
      {
        name: '违章检测',
        description: '我是执法员，需要一个违章行为智能图片识别应用。通过图像识别，自动检测交通违法行为。',
      },
      {
        name: '车辆追踪',
        description: '作为追逃民警，我需要一个车辆追踪智能图片识别应用。通过图像识别，追踪目标车辆位置。',
      },
      {
        name: '事故分析',
        description: '我是事故处理员，想要一个事故现场智能图片识别应用。拍摄事故照片，自动分析事故原因。',
      },
      {
        name: '路况预测',
        description: '作为交通规划师，我需要一个路况预测智能图片识别应用。通过图像识别，预测交通拥堵情况。',
      },
      {
        name: '停车管理',
        description: '我是停车场管理员，需要一个车位管理智能图片识别应用。通过图像识别，管理停车位使用情况。',
      },
      {
        name: '车辆分类',
        description: '作为统计员，我想要一个车辆分类智能图片识别应用。拍摄车辆照片，自动识别车辆类型。',
      },
    ],
  },
  {
    name: '安防',
    enname: 'security',
    scenarios: [
      {
        name: '人脸识别',
        description:
          '我是安保主管，需要一个人脸识别智能图片识别应用。通过拍摄人脸照片，自动识别身份信息，进行门禁管理。',
      },
      {
        name: '行为分析',
        description: '作为监控员，我想要一个行为分析智能图片识别应用。通过图像识别，自动分析异常行为。',
      },
      {
        name: '入侵检测',
        description: '我是安防工程师，需要一个入侵检测智能图片识别应用。通过监控图像识别，及时发现入侵行为。',
      },
      {
        name: '物品识别',
        description: '作为安检员，我需要一个物品识别智能图片识别应用。拍摄物品照片，自动识别危险物品。',
      },
      {
        name: '区域监控',
        description: '我是区域主管，想要一个区域监控智能图片识别应用。通过图像识别，监控重点区域安全。',
      },
      {
        name: '轨迹追踪',
        description: '作为追踪专家，我需要一个轨迹追踪智能图片识别应用。通过图像识别，追踪目标移动轨迹。',
      },
      {
        name: '车辆识别',
        description: '我是停车场管理员，需要一个车辆识别智能图片识别应用。通过图像识别，识别可疑车辆。',
      },
      {
        name: '设备监控',
        description: '作为设备管理员，我想要一个设备状态智能图片识别应用。通过图像识别，监控安防设备状态。',
      },
    ],
  },
  {
    name: '旅游',
    enname: 'tourism',
    scenarios: [
      {
        name: '景点识别',
        description: '我是导游，需要一个景点信息智能图片识别应用。通过拍摄景点照片，自动识别景点信息，提供讲解服务。',
      },
      {
        name: '路线规划',
        description: '作为旅游规划师，我想要一个路线规划智能图片识别应用。通过图像识别，分析景点分布，规划最优路线。',
      },
      {
        name: '游客分析',
        description: '我是景区管理员，需要一个游客分析智能图片识别应用。通过图像识别，分析游客流量和行为。',
      },
      {
        name: '设施管理',
        description: '作为设施主管，我需要一个设施管理智能图片识别应用。拍摄设施照片，自动检测设施状态。',
      },
      {
        name: '环境监测',
        description: '我是环保专员，想要一个环境监测智能图片识别应用。通过图像识别，监测景区环境状况。',
      },
      {
        name: '安全监控',
        description: '作为安全主管，我需要一个安全监控智能图片识别应用。通过图像识别，监控景区安全状况。',
      },
      {
        name: '服务评价',
        description: '我是服务主管，需要一个服务评价智能图片识别应用。通过图像识别，分析游客满意度。',
      },
      {
        name: '文化展示',
        description: '作为文化专员，我想要一个文化展示智能图片识别应用。拍摄文物照片，自动识别并展示文化信息。',
      },
    ],
  },
  {
    name: '娱乐',
    enname: 'entertainment',
    scenarios: [
      {
        name: '表情识别',
        description: '我是游戏设计师，需要一个表情识别智能图片识别应用。通过拍摄人脸照片，自动识别表情，增强游戏互动。',
      },
      {
        name: '动作识别',
        description: '作为运动教练，我想要一个动作识别智能图片识别应用。通过图像识别，分析运动动作标准性。',
      },
      {
        name: '场景识别',
        description: '我是摄影师，需要一个场景识别智能图片识别应用。拍摄场景照片，自动识别场景类型，优化拍摄参数。',
      },
      {
        name: '人物识别',
        description: '作为活动策划师，我需要一个人物识别智能图片识别应用。拍摄人物照片，自动识别身份信息。',
      },
      {
        name: '道具识别',
        description: '我是道具师，想要一个道具识别智能图片识别应用。拍摄道具照片，自动识别道具类型和状态。',
      },
      {
        name: '舞台监控',
        description: '作为舞台总监，我需要一个舞台监控智能图片识别应用。通过图像识别，监控舞台表演效果。',
      },
      {
        name: '观众分析',
        description: '我是活动策划师，需要一个观众分析智能图片识别应用。通过图像识别，分析观众反应。',
      },
      {
        name: '特效识别',
        description: '作为特效师，我想要一个特效识别智能图片识别应用。拍摄特效照片，自动识别特效类型。',
      },
    ],
  },
  {
    name: '餐饮',
    enname: 'catering',
    scenarios: [
      {
        name: '菜品识别',
        description: '我是厨师，需要一个菜品识别智能图片识别应用。通过拍摄菜品照片，自动识别菜品类型和配料。',
      },
      {
        name: '食材检测',
        description: '作为采购主管，我想要一个食材检测智能图片识别应用。拍摄食材照片，自动检测食材新鲜度。',
      },
      {
        name: '卫生检查',
        description: '我是卫生主管，需要一个卫生检查智能图片识别应用。拍摄厨房照片，自动检查卫生状况。',
      },
      {
        name: '餐具识别',
        description: '作为餐具管理员，我需要一个餐具识别智能图片识别应用。拍摄餐具照片，自动识别餐具类型。',
      },
      {
        name: '顾客分析',
        description: '我是餐厅经理，想要一个顾客分析智能图片识别应用。通过图像识别，分析顾客用餐行为。',
      },
      {
        name: '库存管理',
        description: '作为库存管理员，我需要一个库存管理智能图片识别应用。拍摄库存照片，自动识别库存数量。',
      },
      {
        name: '设备监控',
        description: '我是设备主管，需要一个设备监控智能图片识别应用。通过图像识别，监控厨房设备状态。',
      },
      {
        name: '环境监测',
        description: '作为环境专员，我想要一个环境监测智能图片识别应用。通过图像识别，监测餐厅环境状况。',
      },
    ],
  },
  {
    name: '房地产',
    enname: 'realestate',
    scenarios: [
      {
        name: '房屋识别',
        description: '我是房产中介，需要一个房屋识别智能图片识别应用。通过拍摄房屋照片，自动识别房屋类型和特征。',
      },
      {
        name: '装修检测',
        description: '作为装修监理，我想要一个装修检测智能图片识别应用。拍摄装修照片，自动检测装修质量。',
      },
      {
        name: '环境评估',
        description: '我是环境评估师，需要一个环境评估智能图片识别应用。拍摄环境照片，自动评估环境状况。',
      },
      {
        name: '设施管理',
        description: '作为物业主管，我需要一个设施管理智能图片识别应用。拍摄设施照片，自动识别设施状态。',
      },
      {
        name: '安全监控',
        description: '我是安全主管，想要一个安全监控智能图片识别应用。通过图像识别，监控小区安全状况。',
      },
      {
        name: '绿化管理',
        description: '作为绿化主管，我需要一个绿化管理智能图片识别应用。拍摄绿化照片，自动识别植物状态。',
      },
      {
        name: '车位管理',
        description: '我是停车场管理员，需要一个车位管理智能图片识别应用。通过图像识别，管理车位使用情况。',
      },
      {
        name: '维修检测',
        description: '作为维修主管，我想要一个维修检测智能图片识别应用。拍摄维修部位照片，自动检测维修质量。',
      },
    ],
  },
  {
    name: '其他',
    enname: 'other',
    scenarios: [
      {
        name: '健康小助手',
        description:
          '我想要有效记录我的食物 卡路里摄入量 及 体重，请创建一个拍照识别食物卡路里 和 体重的应用，包含卡路里记录 和 体重记录 两个智能图片识别表单，以及应用 首页。',
      },
      {
        name: '设备监控',
        description: '我是设备工程师，需要一个设备监控智能图片识别应用。通过拍摄设备照片，自动识别设备运行状态。',
      },
      {
        name: '能源消耗分析',
        description: '作为能源主管，我想要一个能源分析智能图片识别应用。拍摄能源设备照片，自动分析能源消耗情况。',
      },
      {
        name: '环境监测',
        description: '我是环保工程师，需要一个环境监测智能图片识别应用。通过图像识别，监测环境污染状况。',
      },
      {
        name: '垃圾分类',
        description: '作为环保专员，我需要一个垃圾分类智能图片识别应用。拍摄垃圾照片，自动识别垃圾类型。',
      },
      {
        name: '气象监测',
        description: '我是气象员，想要一个气象监测智能图片识别应用。通过图像识别，监测天气变化情况。',
      },
      {
        name: '植物识别',
        description: '作为植物学家，我需要一个植物识别智能图片识别应用。拍摄植物照片，自动识别植物种类。',
      },
      {
        name: '动物识别',
        description: '我是动物学家，需要一个动物识别智能图片识别应用。拍摄动物照片，自动识别动物种类。',
      },
      {
        name: '文物识别',
        description: '作为考古学家，我想要一个文物识别智能图片识别应用。拍摄文物照片，自动识别文物信息。',
      },
      {
        name: '艺术品识别',
        description: '我是艺术品鉴定师，需要一个艺术品识别智能图片识别应用。拍摄艺术品照片，自动识别艺术品信息。',
      },
      {
        name: '服装设计',
        description: '作为服装设计师，我需要一个服装识别智能图片识别应用。拍摄服装照片，自动识别服装款式。',
      },
    ],
  },
];
