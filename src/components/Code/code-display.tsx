import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { Markdown } from '@lobehub/ui';

import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { useThrottleFn } from 'ahooks';

const isStartWithSlash = (str: string) => {
  return str.startsWith('`');
};

export const CanvasCodeDisplay: React.FC<{}> = () => {
  const [tabItems, setTabItems] = useState([]);
  const [activeTab, setActiveTab] = useState('main');
  const [currentCode, setCurrentCode] = useState('');
  const code = useAIArtifactStateStore((s) => s.code);
  const componentList = useAIArtifactStateStore((s) => s.componentList);

  const setDebouncedCode = useThrottleFn(
    (sourceCode: string) => {
      setCurrentCode(sourceCode);
    },
    {
      wait: 2000,
    },
  );

  useEffect(() => {
    const items = [
      { label: '页面', key: 'main' },
      ...componentList.map((comp) => ({
        label: comp.fileName,
        key: comp.fileName,
      })),
    ];
    setTabItems(items);
  }, [componentList]);

  useEffect(() => {
    setDebouncedCode.run(code);
  }, [code]);

  return (
    <div>
      <Tabs size="small" tabPosition="top" type="card" items={tabItems} activeKey={activeTab} onChange={setActiveTab} />
      <Markdown
        fullFeaturedCodeBlock
        style={{
          height: 'calc(100vh - 240px)',
          overflowY: 'auto',
        }}
      >
        {isStartWithSlash(currentCode)
          ? currentCode
          : `\`\`\`jsx
${currentCode}
\`\`\`
`}
      </Markdown>
    </div>
  );
};
