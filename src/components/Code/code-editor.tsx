import { useDebounceFn } from 'ahooks';
import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';

import { useAIArtifactStateStore } from '@/store/aiArtifactStore';

export const getCodeEditorComponent = async () => {
  try {
    return window.LeGao.__ctx__.App.components.YidaCodeField;
  } catch (error) {
    console.error('获取代码编辑器组件失败', error);
    return () => <div>Loading Code Editor Failed</div>;
  }
};

export const CanvasCodeEditor: React.FC<{}> = () => {
  const [CodeEditorComponentView, setCodeEditorComponentView] = useState<React.ComponentType | null>(null);
  const [tabItems, setTabItems] = useState([]);
  const [activeTab, setActiveTab] = useState('main');
  const [currentTabCode, setCurrentTabCode] = useState('');
  const code = useAIArtifactStateStore((s) => s.code);
  const componentList = useAIArtifactStateStore((s) => s.componentList);
  const setCode = useAIArtifactStateStore((s) => s.setCode);
  const setComponentCode = useAIArtifactStateStore((s) => s.setComponentCode);

  useEffect(() => {
    getCodeEditorComponent().then((view) => {
      setCodeEditorComponentView(view);
    });
  }, []);

  const getCurrentCode = () => {
    if (activeTab === 'main') {
      return code || 'const YidaComp = () => { return <div>思考中...</div> }';
    }
    const component = componentList.find((item) => item.fileName === activeTab);
    return component?.code || '';
  };

  const handleCodeChange = useDebounceFn(
    (value: string) => {
      if (activeTab === 'main') {
        setCode(value, { mode: 'update' });
      } else {
        setComponentCode(activeTab, value);
      }
    },
    {
      wait: 1200,
    },
  );

  useEffect(() => {
    const items = [
      { label: '页面', key: 'main' },
      ...componentList.map((comp) => ({
        label: comp.fileName,
        key: comp.fileName,
      })),
    ];
    setTabItems(items);
  }, [componentList]);

  useEffect(() => {
    const currentCode = getCurrentCode();
    setCurrentTabCode(currentCode);
  }, [activeTab, code, componentList]);

  if (!CodeEditorComponentView) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <Tabs size="small" tabPosition="top" type="card" items={tabItems} activeKey={activeTab} onChange={setActiveTab} />
      <CodeEditorComponentView
        language="javascript"
        value={currentTabCode}
        readOnly={false}
        onChange={handleCodeChange.run}
      />
    </div>
  );
};

export const setCodeFn = (
  value: string,
  options?: {
    mode?: 'create' | 'update';
  },
) => {
  const isCodeGenerated = useAIArtifactStateStore.getState().isCodeGenerated;
  if (!isCodeGenerated) {
    // 正在处理更新 diff 或者代码，若没有处理了，不需要再更新
    if (value?.trim?.()) {
      useAIArtifactStateStore.getState().setCode(value, options);
    }
    // 增加到反馈
  }
};
