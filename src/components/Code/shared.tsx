import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import React, { useEffect, useState } from 'react';
import { debounce, throttle } from 'lodash';
import { useAIChatStore } from '@/store/aiChatStore';
import { useDebounceFn } from 'ahooks';

export const getCodeEditorComponent = async () => {
  try {
    return window.LeGao.__ctx__.App.components.YidaCodeField;
  } catch (error) {
    console.error('获取代码编辑器组件失败', error);
    return () => <div>Loading Code Editor Failed</div>;
  }
};

export const CanvasCodeEditor: React.FC<{}> = () => {
  const [CodeEditorComponentView, setCodeEditorComponentView] = useState<React.ComponentType | null>(null);
  const code = useAIArtifactStateStore((s) => s.code);
  const setCode = useAIArtifactStateStore((s) => s.setCode);

  useEffect(() => {
    getCodeEditorComponent().then((view) => {
      setCodeEditorComponentView(view);
    });
  }, []);

  const handleCodeChange = useDebounceFn(
    (value) => {
      setCode(value, { mode: 'update' });
    },
    {
      wait: 1200,
    },
  );

  if (!CodeEditorComponentView) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {
        <CodeEditorComponentView
          language="javascript"
          value={code || 'const YidaComp = () => { return <div>hello world</div> }'}
          readOnly={false}
          onChange={handleCodeChange.run}
        />
      }
    </div>
  );
};

export const debouncedSetCode = (debouncedTime: number) => {
  return debounce((value: string) => {
    const isProcessing = useAIChatStore.getState().isProcessing;
    if (isProcessing) {
      // 正在处理更新 diff 或者代码，若没有处理了，不需要再更新
      useAIArtifactStateStore.getState().setCode(value);
    }
  }, debouncedTime);
};

export const throttleSetCode = (throttleTime: number) => {
  return throttle(
    (
      value: string,
      options?: {
        mode?: 'create' | 'update';
      },
    ) => {
      const isCodeGenerated = useAIArtifactStateStore.getState().isCodeGenerated;
      if (!isCodeGenerated) {
        // 正在处理更新 diff 或者代码，若没有处理了，不需要再更新
        useAIArtifactStateStore.getState().setCode(value, options);
        // 增加到反馈
      }
    },
    throttleTime,
    { trailing: true },
  );
};
