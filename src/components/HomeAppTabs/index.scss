.yida-home-tabs {
  margin-top: 20px;

  .tab-title {
    display: flex;
    align-items: center;

    .next-icon {
      margin-right: 5px;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .error-message {
    color: #f5222d;
    text-align: center;
    padding: 20px;
    animation: fadeIn 0.5s ease-in-out;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    animation: fadeIn 0.6s ease-in-out;

    .empty-icon {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 10px;
    }

    .empty-text {
      color: #999;
    }
  }

  .app-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    padding: 16px 0;
  }
}
