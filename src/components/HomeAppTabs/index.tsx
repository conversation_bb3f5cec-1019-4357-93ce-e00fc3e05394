import React, { useEffect, useState } from 'react';
import { Tab, Icon, Loading, Message } from '@alifd/next';
import AppCard from '@/components/AppCard';
import { AppItem } from '@/types/app';
import getLatelyAccessAppList from '@/apis/getLatelyAccessAppList';
import getQuickAccessAppList from '@/apis/getQuickAccessAppList';
import { ManageQuickAccess } from '@/apis/manageQuickAccess';
import { getAppList } from '@/apis';
import './index.scss';

const HomeAppTabs: React.FC<any> = () => {
  const [recentlyUsedApps, setRecentlyUsedApps] = useState<AppItem[]>([]);
  const [favoriteApps, setFavoriteApps] = useState<AppItem[]>([]);
  const [myCreatedApps, setMyCreatedApps] = useState<AppItem[]>([]);
  const [allApps, setAllApps] = useState<AppItem[]>([]);
  const [appTabsLoading, setAppTabsLoading] = useState<boolean>(false);
  const [favoriteAppsLoading, setFavoriteAppsLoading] = useState<boolean>(false);
  const [myCreatedAppsLoading, setMyCreatedAppsLoading] = useState<boolean>(false);
  const [allAppsLoading, setAllAppsLoading] = useState<boolean>(false);
  const [recentlyUsedError, setRecentlyUsedError] = useState<string>('');
  const [favoriteAppsError, setFavoriteAppsError] = useState<string>('');
  const [myCreatedAppsError, setMyCreatedAppsError] = useState<string>('');
  const [allAppsError, setAllAppsError] = useState<string>('');

  // 转换应用数据的通用函数
  const convertAppData = (app: any): AppItem => {
    const icon = app.icon || app.iconUrl || '📱'; // 默认图标
    const iconType = typeof icon === 'string' && icon.startsWith('http') ? 'image' : 'icon';
    const tarIcon = iconType === 'image' ? `${icon}?x-oss-process=image/resize,w_100` : icon;
    return {
      id: app.id || app.appId || app.appType,
      title: app.appName?.zh_CN || app.name || app.title,
      description: app.description?.zh_CN || '暂无',
      icon: tarIcon,
      iconType,
      createTime: app.gmtCreate || app.createTime,
      updateTime: app.lastEdit || app.gmtModified || app.updateTime,
      systemLink: app.systemLink,
    };
  };
  // 获取最近使用的应用列表
  useEffect(() => {
    const fetchRecentlyUsedApps = async () => {
      setAppTabsLoading(true);
      setRecentlyUsedError('');
      try {
        const response = await getLatelyAccessAppList({
          pageIndex: 1,
          pageSize: 8,
        });

        if (response && response.data) {
          const recentApps: AppItem[] = response.data.map(convertAppData);
          setRecentlyUsedApps(recentApps);
        }
      } catch (err) {
        console.error('获取最近使用应用列表失败:', err);
        setRecentlyUsedError('获取最近使用应用列表失败，请稍后重试');
      } finally {
        setAppTabsLoading(false);
      }
    };

    fetchRecentlyUsedApps();
  }, []);

  // 获取我收藏的应用列表
  useEffect(() => {
    const fetchFavoriteApps = async () => {
      setFavoriteAppsLoading(true);
      setFavoriteAppsError('');
      try {
        const response = await getQuickAccessAppList({
          pageIndex: 1,
          pageSize: 8,
        });

        if (response && response.data) {
          const favorites: AppItem[] = response.data.map(convertAppData);
          setFavoriteApps(favorites);
        }
      } catch (err) {
        console.error('获取收藏应用列表失败:', err);
        setFavoriteAppsError('获取收藏应用列表失败，请稍后重试');
      } finally {
        setFavoriteAppsLoading(false);
      }
    };

    fetchFavoriteApps();
  }, []);

  // 管理应用收藏状态
  const handleFavoriteAction = async (appType: string | number, isFavorite: boolean, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const res = await ManageQuickAccess({
        appType,
        isDeleted: isFavorite ? 'y' : 'n', // 'y'表示取消收藏，'n'表示收藏
      });

      if (res) {
        if (isFavorite) {
          Message.success('取消收藏成功');
          // 从收藏列表中移除该应用
          setFavoriteApps((prevApps) => prevApps.filter((app) => app.id !== appType));
        } else {
          Message.success('收藏成功');
          // 获取应用详情并添加到收藏列表
          const appToAdd = [...recentlyUsedApps, ...myCreatedApps, ...allApps].find((app) => app.id === appType);
          if (appToAdd) {
            setFavoriteApps((prevApps) => [appToAdd, ...prevApps]);
          }
        }
      } else {
        Message.error(isFavorite ? '取消收藏失败' : '收藏失败');
      }
    } catch (err) {
      console.error('管理应用收藏状态失败:', err);
      Message.error(isFavorite ? '取消收藏失败，请稍后重试' : '收藏失败，请稍后重试');
    }
  };

  // 取消收藏应用
  const handleUnfavorite = (appType: string | number, e: React.MouseEvent) => {
    handleFavoriteAction(appType, true, e);
  };

  // 收藏应用
  const handleFavorite = (appType: string | number, e: React.MouseEvent) => {
    handleFavoriteAction(appType, false, e);
  };

  // 获取我创建的应用列表
  useEffect(() => {
    const fetchMyCreatedApps = async () => {
      setMyCreatedAppsLoading(true);
      setMyCreatedAppsError('');
      try {
        const response = await getAppList({
          pageIndex: 1,
          pageSize: 8,
          creator: window.loginUser?.userId,
        });

        if (response && response.data) {
          const myApps: AppItem[] = response.data.map(convertAppData);
          setMyCreatedApps(myApps);
        }
      } catch (err) {
        console.error('获取我创建的应用列表失败:', err);
        setMyCreatedAppsError('获取我创建的应用列表失败，请稍后重试');
      } finally {
        setMyCreatedAppsLoading(false);
      }
    };
    fetchMyCreatedApps();
  }, []);

  // 获取全部应用列表
  useEffect(() => {
    const fetchAllApps = async () => {
      setAllAppsLoading(true);
      setAllAppsError('');
      try {
        // 尝试使用不同的参数组合获取全部应用
        const response = await getAppList({
          pageIndex: 1,
          pageSize: 32,
          orderField: 'data_gmt_create',
          appStatus: 'ONLINE',
          isAdmin: true,
        });
        if (response && response.data) {
          const apps: AppItem[] = response.data.map(convertAppData);
          setAllApps(apps);
        } else {
          // 如果没有数据，设置为空数组
          setAllApps([]);
        }
      } catch (err) {
        console.error('获取全部应用列表失败:', err);
        setAllAppsError('获取全部应用列表失败，请稍后重试');
        // 出错时也设置为空数组，避免界面显示问题
        setAllApps([]);
      } finally {
        setAllAppsLoading(false);
      }
    };

    fetchAllApps();
  }, []);

  return (
    <Tab className="yida-home-tabs">
      <Tab.Item
        title={
          <span className="tab-title">
            <Icon type="edit" /> 最近使用
          </span>
        }
        key="1"
      >
        {appTabsLoading ? (
          <div className="loading-container">
            <Loading size="medium" />
          </div>
        ) : recentlyUsedError ? (
          <div className="error-message">{recentlyUsedError}</div>
        ) : recentlyUsedApps.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <Icon type="kongbaiyingyong" />
            </div>
            <div className="empty-text">暂无最近使用的应用</div>
          </div>
        ) : (
          <div className="app-grid fade-in">
            {recentlyUsedApps.map((app) => {
              // 检查应用是否已经收藏
              const isFavorite = favoriteApps.some((favoriteApp) => favoriteApp.id === app.id);
              return (
                <div className="app-card-animation enter-animation" key={app.id}>
                  <AppCard
                    title={app.title}
                    description={app.description || ''}
                    icon={app.icon || ''}
                    iconType={app.iconType}
                    isFavorite={isFavorite}
                    onFavorite={isFavorite ? undefined : (e) => handleFavorite(app.id, e)}
                    onUnfavorite={isFavorite ? (e) => handleUnfavorite(app.id, e) : undefined}
                    onClick={() => window.open(app.systemLink, '_blank')}
                  />
                </div>
              );
            })}
          </div>
        )}
      </Tab.Item>
      <Tab.Item
        title={
          <span className="tab-title">
            <Icon type="star" /> 我收藏的
          </span>
        }
        key="2"
      >
        {favoriteAppsLoading ? (
          <div className="loading-container">
            <Loading size="medium" />
          </div>
        ) : favoriteAppsError ? (
          <div className="error-message">{favoriteAppsError}</div>
        ) : favoriteApps.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <Icon type="kongbaiyingyong" />
            </div>
            <div className="empty-text">暂无收藏的应用</div>
          </div>
        ) : (
          <div className="app-grid fade-in">
            {favoriteApps.map((app) => (
              <div className="app-card-animation enter-animation" key={app.id}>
                <AppCard
                  title={app.title}
                  description={app.description || ''}
                  icon={app.icon || ''}
                  iconType={app.iconType}
                  isFavorite={true}
                  onUnfavorite={(e) => handleUnfavorite(app.id, e)}
                  onClick={() => window.open(app.systemLink, '_blank')}
                />
              </div>
            ))}
          </div>
        )}
      </Tab.Item>
      <Tab.Item
        title={
          <span className="tab-title">
            <Icon type="employee-add" /> 我创建的
          </span>
        }
        key="3"
      >
        {myCreatedAppsLoading ? (
          <div className="loading-container">
            <Loading size="medium" />
          </div>
        ) : myCreatedAppsError ? (
          <div className="error-message">{myCreatedAppsError}</div>
        ) : myCreatedApps.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <Icon type="kongbaiyingyong" />
            </div>
            <div className="empty-text">暂无创建的应用</div>
          </div>
        ) : (
          <div className="app-grid fade-in">
            {myCreatedApps.map((app) => {
              // 检查应用是否已经收藏
              const isFavorite = favoriteApps.some((favoriteApp) => favoriteApp.id === app.id);
              return (
                <div className="app-card-animation enter-animation" key={app.id}>
                  <AppCard
                    title={app.title}
                    description={app.description || ''}
                    icon={app.icon || ''}
                    iconType={app.iconType}
                    isFavorite={isFavorite}
                    onFavorite={isFavorite ? undefined : (e) => handleFavorite(app.id, e)}
                    onUnfavorite={isFavorite ? (e) => handleUnfavorite(app.id, e) : undefined}
                    onClick={() => window.open(app.systemLink, '_blank')}
                  />
                </div>
              );
            })}
          </div>
        )}
      </Tab.Item>
      <Tab.Item
        title={
          <span className="tab-title">
            <Icon type="eye" /> 全部应用
          </span>
        }
        key="4"
      >
        {allAppsLoading ? (
          <div className="loading-container">
            <Loading size="medium" />
          </div>
        ) : allAppsError ? (
          <div className="error-message">{allAppsError}</div>
        ) : allApps.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <Icon type="kongbaiyingyong" />
            </div>
            <div className="empty-text">暂无应用数据</div>
          </div>
        ) : (
          <div className="app-grid fade-in">
            {allApps.map((app) => {
              // 检查应用是否已经收藏
              const isFavorite = favoriteApps.some((favoriteApp) => favoriteApp.id === app.id);
              return (
                <div className="app-card-animation enter-animation" key={app.id}>
                  <AppCard
                    title={app.title}
                    description={app.description || ''}
                    icon={app.icon || ''}
                    iconType={app.iconType}
                    isFavorite={isFavorite}
                    onFavorite={isFavorite ? undefined : (e) => handleFavorite(app.id, e)}
                    onUnfavorite={isFavorite ? (e) => handleUnfavorite(app.id, e) : undefined}
                    onClick={() => window.open(app.systemLink, '_blank')}
                  />
                </div>
              );
            })}
          </div>
        )}
      </Tab.Item>
    </Tab>
  );
};

export default HomeAppTabs;
