import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { useAIChatStore } from '@/store/aiChatStore';

export const CanvasLogger: React.FC<{}> = () => {
  const logger = useAIArtifactStateStore((s) => s.logger);
  const setEnableAutoScroll = useAIChatStore((s) => s.setEnableAutoScroll);

  const [initScroll, setInitScroll] = useState(false);
  const [stopAutoScroll, setStopAutoScroll] = useState(false);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const shouldAutoScrollRef = useRef<boolean>(true);

  // 自动滚动到底部的处理函数
  const scrollToBottom = useCallback(() => {
    // 禁用强制滚动功能，仅当shouldAutoScroll为true时才滚动
    const container = messagesContainerRef.current;
    if (container && shouldAutoScrollRef.current && !stopAutoScroll) {
      setTimeout(() => {
        container.scrollTop = container.scrollHeight;
      }, 0);
    }
  }, [stopAutoScroll]);

  // 添加滚动事件监听
  useEffect(() => {
    const handleScroll = () => {
      const container = messagesContainerRef.current;
      if (container) {
        const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
        const isAtBottom = scrollBottom < 50;
        if (isAtBottom !== shouldAutoScrollRef.current) {
          shouldAutoScrollRef.current = isAtBottom;
        }
      }
    };
    const container = messagesContainerRef.current;
    if (initScroll && container) {
      container.addEventListener('scroll', handleScroll);
    }
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [initScroll]);

  // 处理双击消息容器
  const handleDoubleClick = useCallback(() => {
    setStopAutoScroll(!stopAutoScroll);
    setEnableAutoScroll(!stopAutoScroll);
  }, [setEnableAutoScroll, stopAutoScroll]);

  // 监听消息变化，控制滚动
  useEffect(() => {
    scrollToBottom();
  }, [logger, scrollToBottom]);

  const formatAssResContent = (content: string) => {
    return content
      .replace?.(/!\[(.*?)\]\((.*?)\)/g, (a, b, c) => {
        return `<img src="${c}" alt="${b}" />`;
      })
      .replace(/\[(.*?)\]\(((http(s)?:)?\/\/.*?)\)/g, (a, b, c) => {
        return `<a href="${c}" target="_blank">${b}</a>`;
      });
  };

  return (
    <div
      style={{
        height: '100%',
        overflowY: 'auto',
      }}
      ref={(ref: any) => {
        messagesContainerRef.current = ref;
        setInitScroll(true);
      }}
      onDoubleClick={handleDoubleClick}
    >
      <div className="assistant-response" dangerouslySetInnerHTML={{ __html: formatAssResContent(logger) }} />
    </div>
  );
};
