import React, { useState, useEffect } from 'react';
import { useAIChatStore } from '@/store/aiChatStore';

interface TextChange {
  path: string;
  before: string;
  after: string;
  element: string;
  diff?: {
    added?: string[];
    removed?: string[];
    unchanged?: string[];
  };
}

interface EditMessageResponseProps {
  content: string;
}

/**
 * Component for rendering AI edit messages with action buttons
 */
const EditMessageResponse: React.FC<EditMessageResponseProps> = ({ content }) => {
  const [modifications, setModifications] = useState<TextChange[]>([]);
  const [title, setTitle] = useState('检测到文本编辑');
  const [summary, setSummary] = useState('');
  const [buttonText, setButtonText] = useState('根据上述用户变更做修改');

  // Access store
  const messages = useAIChatStore((s) => s.messages);
  const setMessages = useAIChatStore((s) => s.setMessages);
  const chatManagerRef = useAIChatStore((s) => s.chatManagerRef);

  // Parse content on mount
  useEffect(() => {
    try {
      // Extract message parts using regex
      const titleMatch = content.match(/### (.*?)\n/);
      if (titleMatch && titleMatch[1]) {
        setTitle(titleMatch[1]);
      }

      // Extract summary (all content between title and button)
      const summaryStart = content.indexOf('\n') + 1;
      const buttonStart = content.indexOf('<button');
      if (summaryStart > 0 && buttonStart > summaryStart) {
        setSummary(content.substring(summaryStart, buttonStart).trim());
      }

      // Extract modifications from data attribute
      const modMatch = content.match(/data-modifications="([^"]*)"/);
      if (modMatch && modMatch[1]) {
        const decodedMods = decodeURIComponent(modMatch[1]);
        setModifications(JSON.parse(decodedMods));
      }

      // Extract button text
      const buttonTextMatch = content.match(/>([^<]*)<\/button>/);
      if (buttonTextMatch && buttonTextMatch[1]) {
        setButtonText(buttonTextMatch[1]);
      }
    } catch (err) {
      console.error('Failed to parse edit message content', err);
    }
  }, [content]);

  // Handle button click
  const handleEditAction = () => {
    // Create a prompt based on the modifications
    const prompt = buttonText;

    // // Add a user message with the prompt
    // const userMessage = {
    //   id: `user-${Date.now()}`,
    //   isUser: true,
    //   content: prompt,
    //   timestamp: new Date(),
    //   isSystemLog: false,
    // };

    // const newMsgs = [...messages, userMessage];
    // setMessages(newMsgs);

    // Send the message to the chat manager
    if (chatManagerRef.current) {
      if (chatManagerRef.current.handleSendMessage) {
        chatManagerRef.current.handleSendMessage(prompt);
      } else if (chatManagerRef.current.handleMessage) {
        chatManagerRef.current.handleMessage(prompt, messages, setMessages);
      }
    }
  };

  // Helper function to truncate text for display
  const truncateText = (text: string, maxLength: number): string => {
    if (!text) return '';
    return text.length <= maxLength ? text : `${text.slice(0, maxLength - 3)}...`;
  };

  return (
    <div className="edit-message-response">
      <h3>{title}</h3>

      {/* Changes summary */}
      {modifications.length > 0 ? (
        <div className="edit-changes-list">
          {modifications.map((change, index) => (
            <div key={index} className="edit-change-item">
              <p>
                修改了 <strong>{change.element}</strong> 元素:
              </p>
              <div className="edit-change-diff">
                <div className="edit-before">
                  <span className="edit-label">原文本:</span>
                  {truncateText(change.before, 50)}
                </div>
                <div className="edit-after">
                  <span className="edit-label">新文本:</span>
                  {truncateText(change.after, 50)}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="edit-summary">{summary}</div>
      )}

      {/* Action button */}
      <button className="edit-action-button" onClick={handleEditAction}>
        {buttonText}
      </button>
    </div>
  );
};

export default EditMessageResponse;
