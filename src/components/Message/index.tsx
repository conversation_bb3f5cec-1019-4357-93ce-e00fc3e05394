import React from 'react';
import AssistantResponse from '@/components/AssistantResponse';
import EditMessageResponse from '@/components/Message/EditMessageResponse';
import { Card, Icon } from '@alifd/next';
import type { ChatMessage } from '@/utils/ChatManager';

interface MessageProps {
  message: ChatMessage;
  isProcessing: boolean;
  isActiveOutput: boolean;
  isLastMessage: boolean;
  formatTime: (date: Date) => string;
  getThinkingStatus: () => string;
  countdown: number;
}

const MessageComponent: React.FC<MessageProps> = ({
  message: msg,
  isProcessing,
  isActiveOutput,
  isLastMessage,
  formatTime,
  getThinkingStatus,
  countdown,
}) => {
  // Helper function to render user avatar
  const renderAvatar = () => {
    if (msg.isUser) {
      return <img src={window.loginUser?.avatar} alt={window.loginUser?.userName} />;
    }
    return (
      <img src="//img.alicdn.com/imgextra/i2/O1CN01rlehH625WAcAeTCNx_!!6000000007533-55-tps-24-24.svg" alt="CodeGen" />
    );
  };

  // Helper function to render message content
  const renderMessageContent = () => {
    if (msg.isUser && msg.isSpecialEditMessage !== 'true' && msg.isSpecialSelectNodeMessage !== 'true') {
      return renderUserMessage();
    } else if (msg.isSpecialEditMessage === 'true') {
      return renderSpecialEditMessage();
    } else {
      return renderAssistantMessage();
    }
  };

  // Helper function to render user message
  const renderUserMessage = () => {
    const { attachments, content } = msg;
    let newContent = content;
    if (attachments?.length) {
      const imageAttachments = attachments.filter((item) => item.type === 'image');
      const fileAttachments = attachments.filter((item) => item.type === 'file');
      const imageHtmlArr = imageAttachments.map((file: any) => {
        return `<img class="file-img" src="${file.url}" alt="${file.name}" />`;
      });
      const fileHtmlArr = fileAttachments.map((file: any) => {
        return `<div class="file-tump"><img src="${file.imgURL}" title="${file.name}" /><div class="file-name">${file.name}</div></div>`;
      });
      if (imageHtmlArr.length || fileHtmlArr.length) {
        newContent += `<div class="file-wrap">${imageHtmlArr.join('')}${fileHtmlArr.join('')}</div>`;
      }
    }
    return (
      <div
        className="user-response"
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{ __html: newContent as string }}
      />
    );
  };

  // Helper function to render special edit message
  const renderSpecialEditMessage = () => {
    return <EditMessageResponse content={msg.content as string} />;
  };

  // Helper function to render assistant message
  const renderAssistantMessage = () => {
    return (
      <>
        <div className="assistant-response">
          <AssistantResponse logs={msg.content} />
        </div>
      </>
    );
  };

  // Helper function to render thinking indicator and waiting response
  const renderThinkingAndWaiting = () => {
    if (!((isProcessing && !isActiveOutput && isLastMessage) || !msg.content)) {
      return null;
    }

    return (
      <>
        <div className="thinking-indicator">
          <div className="thinking-icon" />
          <span>{getThinkingStatus()}</span>
        </div>
        {renderWaitingResponse()}
      </>
    );
  };

  // Helper function to get countdown class
  const getCountdownClass = () => {
    return countdown <= 5 ? 'warning' : '';
  };

  // Helper function to render waiting response
  const renderWaitingResponse = () => {
    if (!(globalThis as any).waitingForUserResponse) {
      return null;
    }

    return (
      <div className="waiting-response-container">
        <Card contentHeight="auto" className="waiting-card">
          <div className="waiting-header">
            <img
              src="//img.alicdn.com/imgextra/i2/O1CN01UtvKf51i0LvNp44v4_!!6000000004350-54-tps-72-72.apng"
              width="16"
              height="16"
              className="waiting-icon"
              alt="等待"
            />
            <span>系统正在等待你的回答</span>
            <div className="countdown-timer">
              <span className="countdown-text">剩余时间：</span>
              <span className={`countdown-number ${getCountdownClass()}`}>{countdown}秒</span>
            </div>
          </div>
          <div className="waiting-question">{(globalThis as any).waitingForUserResponse.question}</div>
          <div className="waiting-instruction">
            <Icon type="arrow-down" size="small" className="instruction-icon" />
            <span>请在下方输入你的回复并点击发送</span>
          </div>
        </Card>
      </div>
    );
  };

  // Helper function to get message class
  const getMessageClass = () => {
    if (msg.isUser) {
      return 'user-message';
    } else if (msg.isSystemLog && msg.id?.startsWith('selection-') && msg.isSpecialSelectNodeMessage) {
      return 'ai-message';
    } else {
      return 'ai-message';
    }
  };

  // Helper function to get sender name
  const getSenderName = () => {
    if (msg.isUser) {
      return window.loginUser?.userName;
    } else {
      return 'CodeGen';
    }
  };

  return (
    <div key={msg.id} className={`message ${getMessageClass()}`}>
      <div className="message-header">
        <div className="message-avatar">{renderAvatar()}</div>
        <span className="message-sender">{getSenderName()}</span>
        <div className="message-header-actions">
          <span className="message-time">{formatTime(msg.timestamp)}</span>
        </div>
      </div>
      <div className={`message-content ${msg.isSystemLog ? 'system-log' : ''}`}>
        <div className="message-text">{renderMessageContent()}</div>
        {renderThinkingAndWaiting()}
      </div>
    </div>
  );
};

export default MessageComponent;
