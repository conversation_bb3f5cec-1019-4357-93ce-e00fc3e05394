import { useDebounceFn } from 'ahooks';
import React, { useEffect, useState } from 'react';

import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { <PERSON><PERSON>, Button } from 'antd';

export const getCodeEditorComponent = async () => {
  try {
    return window.LeGao.__ctx__.App.components.YidaCodeField;
  } catch (error) {
    console.error('Failed to get code editor component', error);
    return () => <div>Loading Code Editor Failed</div>;
  }
};

export const PrdEditor: React.FC = () => {
  const [CodeEditorComponentView, setCodeEditorComponentView] = useState<React.ComponentType | null>(null);
  const setPrdContentReady = useAIArtifactStateStore((s) => s.setPRDReady);
  const isPRDReady = useAIArtifactStateStore((s) => s.prdReady);
  const prdIsGenerating = useAIArtifactStateStore((s) => s.isPrdInGenerating);
  const prdContent = useAIArtifactStateStore((s) => s.prdContent);
  const setPrdContent = useAIArtifactStateStore((s) => s.setPrdContent);

  useEffect(() => {
    getCodeEditorComponent().then((view) => {
      setCodeEditorComponentView(view);
    });
  }, []);

  const handlePrdContentChange = useDebounceFn(
    (value: string) => {
      setPrdContent(value);
    },
    {
      wait: 1000,
    },
  );

  if (!CodeEditorComponentView) {
    return <div>Loading PRD Editor...</div>;
  }

  return (
    <div className="prd-editor-container" style={{ height: '100%' }}>
      {!!prdContent && (
        <>
          <Button
            loading={prdIsGenerating}
            disabled={isPRDReady}
            type="primary"
            style={{
              marginBottom: '12px',
            }}
            onClick={() => {
              setPrdContentReady(prdContent);
            }}
          >
            {prdIsGenerating ? '生成中...' : '继续生成应用'}
          </Button>
          <div style={{ marginBottom: '12px' }}>
            <Alert type="info" message="请确认和编辑产品功能说明后，点击继续生成后开始正式生成应用" />
          </div>
        </>
      )}
      <CodeEditorComponentView
        language="markdown"
        value={prdContent || '# 产品功能说明 \n\n等待 AI 生成...'}
        readOnly={isPRDReady}
        onChange={handlePrdContentChange.run}
      />
    </div>
  );
};
