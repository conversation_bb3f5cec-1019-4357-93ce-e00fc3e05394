import { Form, Input, Modal, Radio, Row, Switch, Tabs } from 'antd';
import React, { useState } from 'react';

import { OPEN_ROUTER_V3_MODEL, useAISettingStore } from '@/store/aiSettingStore';

import type { GenerateAppComplexityLevel } from '@/tools/GenerateBizPRDTool/prd-gen.share';
export interface IProps {
  visible: boolean;
  onClose: () => void;
}

export const SettingDialog = (props: IProps) => {
  const { visible, onClose } = props;
  const [activeTab, setActiveTab] = useState('1');
  const {
    modelType,
    model,
    temperature,
    useTemplate,
    appComplexity,
    uiFramework,
    enableCodeGenPRDContent,
    setEnableCodeGenPRDContent,
    setModelType,
    setModel,
    mainModel,
    setMainModel,
    setTemperature,
    setUseTemplate,
    setUiAIEditing,
    uiAIEditing,
    setAppComplexity,
    setUiFramework,
    bailianSk,
    deepseekSk,
    openRouterSk,
    setBailianSk,
    setDeepseekSk,
    setOpenRouterSk,
  } = useAISettingStore();

  return (
    <Modal title="设置" open={visible} onCancel={onClose} width={800} footer={null}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: '1',
            label: '模型设置',
            children: (
              <Form layout="vertical">
                <Form.Item label="Manus运行推理模型">
                  <Radio.Group
                    value={mainModel}
                    onChange={(e) => setMainModel(e.target.value)}
                    options={[
                      { label: 'qwen-max', value: 'qwen-max' },
                      { label: 'qwen3-235b-a22b', value: 'qwen3-235b-a22b' },
                      { label: 'qwq-32b', value: 'qwq-32b' },
                      { label: 'qwq-plus', value: 'qwq-plus' },
                      { label: 'dingtalk-qwen3-235b', value: 'dingtalk-qwen3-235b' },
                      { label: 'dingtalk-qwen-max', value: 'dingtalk-qwen-max' },
                    ]}
                  />
                </Form.Item>

                <Form.Item label="代码生成模型服务商">
                  <Radio.Group
                    value={modelType}
                    onChange={(e) => setModelType(e.target.value)}
                    options={[
                      { label: '百炼', value: 'aliyun' },
                      { label: '钉钉', value: 'ding' },
                      { label: 'DeepSeek', value: 'deepseek' },
                      { label: 'OpenRouter', value: 'openrouter' },
                    ]}
                  />
                </Form.Item>

                <Form.Item label="代码生成模型">
                  <Radio.Group
                    value={model}
                    onChange={(e) => setModel(e.target.value)}
                    options={[
                      { label: 'Claude 3.7', value: 'anthropic/claude-3.7-sonnet' },
                      { label: 'Claude 4 Sonnet', value: 'anthropic/claude-sonnet-4' },
                      { label: 'DeepSeek V3', value: OPEN_ROUTER_V3_MODEL },
                      { label: 'Qwen3', value: 'qwen3' },
                      { label: 'dingtalk-qwen3', value: 'dingtalk-qwen3-235b' },
                      { label: 'Google Gemini Pro 2.5', value: 'google/gemini-2.5-pro-exp-03-25:free' },
                      { label: 'DeepSeek Reasoner', value: 'deepseek-reasoner' },
                      { label: 'DeepSeek Chat', value: 'deepseek-chat' },
                    ]}
                  />
                </Form.Item>

                <Form.Item label="应用复杂度">
                  <Radio.Group
                    value={appComplexity}
                    onChange={(e) => setAppComplexity(e.target.value as GenerateAppComplexityLevel)}
                    options={[
                      { label: '简单', value: 'easy' },
                      { label: '中等', value: 'medium' },
                      { label: '复杂', value: 'hard' },
                    ]}
                  />
                </Form.Item>

                <Form.Item label="UI组件库">
                  <Radio.Group
                    value={uiFramework}
                    onChange={(e) => setUiFramework(e.target.value)}
                    options={[
                      { label: 'Ant Design', value: 'antd' },
                      { label: 'Radix UI', value: 'radix-ui' },
                    ]}
                  />
                </Form.Item>

                <Form.Item label="温度">
                  <Input value={temperature} onChange={(e) => setTemperature(e.target.value)} />
                </Form.Item>
                <Row>
                  <Form.Item label="代码生成强制启用PRD内容">
                    <Switch checked={enableCodeGenPRDContent} onChange={setEnableCodeGenPRDContent} />
                  </Form.Item>
                  <Form.Item label="使用代码预设模板">
                    <Switch checked={useTemplate} onChange={setUseTemplate} />
                  </Form.Item>
                  <Form.Item label="AI 编辑 & 选择">
                    <Switch checked={uiAIEditing} onChange={setUiAIEditing} />
                  </Form.Item>
                </Row>
              </Form>
            ),
          },
          {
            key: '2',
            label: 'API密钥设置',
            children: (
              <Form layout="vertical">
                <Form.Item label="百炼API密钥">
                  <Input.Password
                    value={bailianSk}
                    onChange={(e) => setBailianSk(e.target.value)}
                    placeholder="请输入百炼API密钥"
                  />
                </Form.Item>
                <Form.Item label="DeepSeek API密钥">
                  <Input.Password
                    value={deepseekSk}
                    onChange={(e) => setDeepseekSk(e.target.value)}
                    placeholder="请输入DeepSeek API密钥"
                  />
                </Form.Item>
                <Form.Item label="OpenRouter API密钥">
                  <Input.Password
                    value={openRouterSk}
                    onChange={(e) => setOpenRouterSk(e.target.value)}
                    placeholder="请输入OpenRouter API密钥"
                  />
                </Form.Item>
              </Form>
            ),
          },
        ]}
      />
    </Modal>
  );
};
