// SideBar 组件样式
.sidebar-toggle {
  position: fixed;
  top: 10px;
  left: 6px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s ease;
  border-radius: 4px;
  &:hover {
    transform: translateY(-1px);
  }
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 250px;
  height: 100vh;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 99;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  transform: translateX(-250px);

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .sidebar-logo {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .app-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .animatedText {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .new-chat-button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      margin: 0 10px;
      background: linear-gradient(119deg, #c676ff 0%, #654cff 41%, #405eff 75%, #007fff 99%);
      border-radius: 15px;
      cursor: pointer;
      transition: all 0.2s ease;
      i {
        margin-right: 6px;
        font-size: 14px;
        color: #fff;
      }
      span {
        font-size: 14px;
        color: #fff;
      }
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        transform: translateY(-1px);
        background: linear-gradient(119deg, #c676ff 0%, #654cff 41%, #405eff 75%, #007fff 99%);
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .time-category {
      margin-bottom: 24px;

      .category-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
      }

      .app-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .app-item {
          display: flex;
          align-items: center;
          padding: 8px;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            background-color: #f5f5f5;
          }

          .app-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }

            .icon {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 4px;
              font-size: 14px;
            }
          }

          .app-name {
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px 0;

      .empty-icon {
        font-size: 32px;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 14px;
        color: #999;
      }
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 32px 0;
    }

    .error-message {
      color: #f5222d;
      text-align: center;
      padding: 16px;
    }
  }
}
