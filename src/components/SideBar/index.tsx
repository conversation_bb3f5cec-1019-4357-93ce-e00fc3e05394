import React, { useEffect, useState } from 'react';
import { Icon, Loading } from '@alifd/next';
import './index.scss';
import { getAppList } from '@/apis';

// 应用数据类型定义
interface AppItem {
  id: number | string;
  title: string;
  icon?: string;
  iconType?: 'image' | 'icon';
  description?: string;
  createTime?: string;
  updateTime?: string;
  systemLink?: string;
}

// 侧边栏应用数据类型，按时间分类
interface SidebarApps {
  today: AppItem[];
  week: AppItem[];
  month: AppItem[];
}

interface SideBarProps {
  visible: boolean;
  visibleChatBot: boolean;
  toggleSidebar: () => void;
}

// 默认侧边栏应用数据，在API加载前显示
const DEFAULT_SIDEBAR_APPS: SidebarApps = {
  today: [
    { id: 1, title: '门店巡检', icon: '🔍' },
    { id: 2, title: '设计平台工作台', icon: '🎨' },
  ],
  week: [
    { id: 3, title: '智能客服工作台', icon: '📞' },
    { id: 4, title: '在线社区首页', icon: '❤️' },
  ],
  month: [
    { id: 7, title: '周报管理系统', icon: '📊' },
    { id: 8, title: '幼儿趣学乐园', icon: '👶' },
  ],
};

const SideBar: React.FC<SideBarProps> = ({ visible, visibleChatBot, toggleSidebar }) => {
  const [sidebarApps, setSidebarApps] = useState<SidebarApps>(DEFAULT_SIDEBAR_APPS);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  // 获取应用列表数据
  useEffect(() => {
    const fetchAppList = async () => {
      setLoading(true);
      setError('');
      try {
        const response = await getAppList({
          pageIndex: 1,
          pageSize: 50,
          orderField: 'data_gmt_create',
        });

        if (response && response.data) {
          // 处理应用数据，按时间分类
          const now = new Date();
          const today = new Date(now.setHours(0, 0, 0, 0));
          const weekAgo = new Date(today);
          weekAgo.setDate(today.getDate() - 7);
          const monthAgo = new Date(today);
          monthAgo.setDate(today.getDate() - 30);

          const todayApps: AppItem[] = [];
          const weekApps: AppItem[] = [];
          const monthApps: AppItem[] = [];

          response.data.forEach((app: any) => {
            const icon = app.icon || app.iconUrl || '📱'; // 默认图标
            const iconType = typeof icon === 'string' && icon.startsWith('http') ? 'image' : 'icon';
            const tarIcon = iconType === 'image' ? `${icon}?x-oss-process=image/resize,w_100` : icon;
            const appItem: AppItem = {
              id: app.id || app.appId || app.appType,
              title: app.appName?.zh_CN || app.name || app.title,
              description: app.description?.zh_CN || app.description,
              icon: tarIcon,
              iconType,
              createTime: app.gmtCreate || app.createTime,
              updateTime: app.lastEdit || app.gmtModified || app.updateTime,
            };

            // 根据创建或更新时间分类
            const updateDate = new Date(appItem.updateTime || appItem.createTime || Date.now());

            if (updateDate >= today) {
              todayApps.push(appItem);
            } else if (updateDate >= weekAgo) {
              weekApps.push(appItem);
            } else if (updateDate >= monthAgo) {
              monthApps.push(appItem);
            } else {
              monthApps.push(appItem); // 超过30天的也放入月分类
            }
          });

          setSidebarApps({
            today: todayApps,
            week: weekApps,
            month: monthApps,
          });
        }
      } catch (err) {
        console.error('获取应用列表失败:', err);
        setError('获取应用列表失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchAppList();
  }, []);

  const renderAppItem = (items: any[]) => {
    return items.map((app: any) => {
      return (
        <div key={app.id} className="app-item">
          <div className="app-icon">
            {app.iconType === 'image' ? (
              <img src={app.icon} alt={app.title} />
            ) : (
              <i
                className={`appList-icon iconfont icon-${app.icon.split('%%')[0]} icon`}
                style={{ backgroundColor: app.icon.split('%%')[1] }}
              />
            )}
          </div>
          <div className="app-name">{app.title}</div>
        </div>
      );
    });
  };

  return (
    <>
      {!visibleChatBot && (
        <div className="sidebar-toggle" onClick={toggleSidebar}>
          <img
            src="https://img.alicdn.com/imgextra/i1/O1CN01WTdKn61kYMa0NaXjU_!!6000000004695-55-tps-20-20.svg"
            alt="展开侧边栏"
          />
        </div>
      )}
      <div className="sidebar">
        <div className="sidebar-header">
          <div className="sidebar-logo">
            <div className="app-icon">
              <img
                src="//img.alicdn.com/imgextra/i2/O1CN01rlehH625WAcAeTCNx_!!6000000007533-55-tps-24-24.svg"
                alt="CodeGen"
              />
            </div>
            <span className="animatedText">CodeGen</span>
          </div>
          <div className="new-chat-button">
            <Icon type="add" size="small" />
            <span>开始新对话</span>
          </div>
        </div>
        <div className="sidebar-content">
          {loading ? (
            <div className="loading-container">
              <Loading size="medium" />
            </div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : (
            <>
              {sidebarApps.today.length > 0 && (
                <div className="time-category">
                  <div className="category-title">今天</div>
                  <div className="app-list">{renderAppItem(sidebarApps.today)}</div>
                </div>
              )}

              {sidebarApps.week.length > 0 && (
                <div className="time-category">
                  <div className="category-title">7天内</div>
                  <div className="app-list">{renderAppItem(sidebarApps.week)}</div>
                </div>
              )}

              {sidebarApps.month.length > 0 && (
                <div className="time-category">
                  <div className="category-title">30天内</div>
                  <div className="app-list">{renderAppItem(sidebarApps.month)}</div>
                </div>
              )}

              {sidebarApps.today.length === 0 && sidebarApps.week.length === 0 && sidebarApps.month.length === 0 && (
                <div className="empty-state">
                  <div className="empty-icon">📋</div>
                  <div className="empty-text">暂无应用</div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default SideBar;
