export const defaultCardSchema: any = {
  schemaType: 'superform',
  schemaVersion: '5.0',
  pages: [
    {
      utils: [],
      componentsMap: [
        {
          package: '@/components/vc-yida-report',
          version: '1.0.6',
          componentName: 'RootHeader',
        },
        {
          package: '@/components/vc-yida-report',
          version: '1.0.6',
          componentName: 'RootContent',
        },
        {
          package: '@/components/vc-yida-report',
          version: '1.0.6',
          componentName: 'RootFooter',
        },
        {
          package: '@/components/vc-yida-report',
          version: '1.0.6',
          componentName: 'Page',
        },
        {
          package: '@/components/vc-yida-report',
          version: '1.0.6',
          componentName: 'YoushuTable',
        },
      ],
      componentsTree: [
        {
          componentName: 'Page',
          id: 'node_ocm9o7axne1',
          props: {
            pageStyle: ':root {\n  background-color: #f2f3f5;\n}\n',
            containerStyle: {},
            userVariables: [
              {
                text: '工号',
                id: 'varWorkNo',
              },
              {
                text: '部门名称',
                id: 'varDeptName',
              },
              {
                text: '所属公司编号',
                id: 'varCorpNo',
              },
              {
                text: '部门编码',
                id: 'varDeptNo',
              },
            ],
            templateVersion: '1.0.0',
            className: 'page_m9o7d9ml',
            params: [],
          },
          css: 'body {\n  background-color: #f2f3f5;\n}\n',
          methods: {
            __initMethods__: {
              type: 'js',
              source: 'function (exports, module) { /*set actions code here*/ }',
              compiled: 'function (exports, module) { /*set actions code here*/ }',
            },
          },
          lifeCycles: {
            constructor: {
              type: 'js',
              compiled:
                "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
              source:
                "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
            },
            componentWillUnmount: null,
            componentDidMount: null,
          },
          children: [
            {
              componentName: 'RootHeader',
              id: 'node_ocm9o7axne2',
              props: {},
            },
            {
              componentName: 'RootContent',
              id: 'node_ocm9o7axne3',
              props: {
                layout: [],
                contentBgColor: 'transparent',
                rglSwitch: true,
              },
              children: [],
            },
            {
              componentName: 'RootFooter',
              id: 'node_ocm9o7axne4',
              props: {},
            },
          ],
        },
      ],
      id: '${formUuid}',
    },
  ],
  actions: {
    module: {
      source: '',
      compiled: '',
    },
    list: [],
  },
};
