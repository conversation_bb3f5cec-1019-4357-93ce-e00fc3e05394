export const defaultDataModuleSchema: any = {
  componentName: 'YoushuTable',
  id: 'node_xxx',
  props: {
    cid: 'node_xxx',
    showComponentTitle: true,
    componentTitle: {
      type: 'i18n',
      zh_CN: 'xxx',
      en_US: 'xxx',
    },
    componentTitleTextAlign: 'LEFT',
    titleTipContent: {
      type: 'i18n',
      zh_CN: '',
      en_US: '',
    },
    titleTipIconName: 'help',
    headerSize: 'medium',
    link: {
      hasLink: false,
      content: {
        type: 'i18n',
        zh_CN: '更多',
        en_US: 'More',
      },
      onlyIcon: true,
    },
    exportData: {
      supportExport: false,
      passType: 'NO_PASS',
      exportType: 'BROWSER',
      filterList: null,
      exportPromptFilter: null,
      ignoreSwitch: true,
    },
    openRefresh: true,
    showFieldSelectIcon: true,
    enabledCache: true,
    auth: [],
    fieldId: 'YoushuTable_xxx',
    afterFetch:
      '/**\n* 对返回的数据做一些自定义处理\n* 返回数据文档： https://www.yuque.com/yida/support/xgg4ps\n* data: 返回的数据 \n* extraInfo: { meta: [], cardParams: {} }，meta 代表数据元信息，cardParams 代表卡片参数信息\n*/\nfunction afterFetch(data, extraInfo) {\n  return data;\n}',
    __style__: {},
    mockData: [],
    dataSetModelMap: {
      table: {
        dataViewQueryModel: {
          cubeCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
          fieldDefinitionList: [
            {
              cubeCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
              isDim: false,
              alias: 'field_m9p1f7v8',
              aliasName: {
                type: 'i18n',
                zh_CN: '日',
              },
              classifiedCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
              fieldCode: 'dateField__m9m60zr7',
              dataType: 'DATE',
              aggregateType: 'COUNT',
              timeGranularityType: 'DAY',
            },
          ],
          fieldList: ['field_m9p1f7v8'],
          filterList: [],
          orderByList: [],
          cubeTenantId: 'ding5d17e3add038d44535c2f4657eb6378f',
        },
        fieldList: [
          {
            title: {
              type: 'i18n',
              zh_CN: '日',
            },
            classifiedCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
            cubeCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
            fieldCode: 'dateField__m9m60zr7',
            isDimension: 'false',
            dataType: 'DATE',
            timeGranularityType: 'DAY',
            format: {
              type: 'NONE',
            },
            link: [
              {
                type: 'NONE',
              },
            ],
            drillList: [],
            aggregateType: 'COUNT',
            orderBy: {
              type: 'NONE',
              reference: 'field_m9p1f7v8',
            },
            fieldKey: 'field_m9p1f7v8',
            visible: true,
            beUsedTimes: 1,
            isVisible: 'y',
            timeFormat: 'yyyy-MM-dd',
            id: 'dateField__m9m60zr75',
            text: '日',
          },
        ],
        youshuDataType: 'real',
        cubeCodes: ['FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO'],
        columnFields: [
          {
            title: {
              type: 'i18n',
              zh_CN: '日',
            },
            classifiedCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
            cubeCode: 'FORM_D6967F09D274498BBA9F9CF6D8102E4BQCUO',
            fieldCode: 'dateField__m9m60zr7',
            isDimension: 'false',
            dataType: 'DATE',
            timeGranularityType: 'DAY',
            format: {
              type: 'NONE',
            },
            link: [
              {
                type: 'NONE',
              },
            ],
            drillList: [],
            aggregateType: 'COUNT',
            orderBy: {
              type: 'NONE',
              reference: 'field_m9p1f7v8',
            },
            fieldKey: 'field_m9p1f7v8',
            visible: true,
            beUsedTimes: 1,
            isVisible: 'y',
            timeFormat: 'yyyy-MM-dd',
            id: 'dateField__m9m60zr75',
            text: '日',
          },
        ],
        filterList: [],
        limit: '',
        mockData: [],
      },
    },
    userConfig: [
      {
        name: 'table',
        title: '配置数据',
        items: [
          {
            name: 'columnFields',
            title: '表格列',
            setterName: 'ColumnFieldSetter',
            tip: {
              content: '体验版最多添加 30 个字段，所有付费版最多添加 100 个字段',
            },
            setterProps: {
              showFormatTab: true,
              showEditTab: true,
              showAliasSetting: true,
              supportDynamicAlias: true,
              showDataLink: true,
              conditionStyle: true,
              showTableColumnTab: true,
              maxFieldNum: 100,
              showBatchSet: true,
              batchSetFields: [
                'text',
                'title',
                'message',
                'aggregateType',
                'align',
                'format_type',
                'format_decimalDigit',
                'width',
                'rowStatus',
                'groupTitle',
              ],
            },
          },
        ],
      },
    ],
    settings: {
      rglConfig: {
        w: 6,
        h: 21,
        isHeightAuto: true,
      },
      size: 'medium',
      wordSize: 'medium:14',
      theme: 'split',
      mergeCell: false,
      fixedHeader: false,
      maxBodyHeight: '300',
      fixedColumnIndex: 1,
      isReverseTable: false,
      showReversedHeader: false,
      isUniqueRows: false,
      pagination: {
        isPagination: false,
        pageSize: 10,
        size: 'small',
        type: 'normal',
        pageShowCount: 5,
        showPageSelect: false,
      },
      isTree: false,
      idField: '',
      pidField: '',
      isLeaf: '',
      drilldownFilterList: '',
      defaultExpand: false,
      seqNumColWidth: 50,
      seqNumBackgroundSize: 10,
      seqNumStyles: [
        {
          backgroundColor: '#ffb71e',
          color: '#ffffff',
        },
        {
          backgroundColor: '#c2c2c2',
          color: '#ffffff',
        },
        {
          backgroundColor: '#d3846b',
          color: '#ffffff',
        },
      ],
      calcTableSpan:
        '/* function calcTableSpan(rowIndex, colIndex, dataIndex, record) {\nif (rowIndex === 2 && colIndex === 1) {\n  return {\n    // 合并3行 \n    rowSpan: 3,\n  };\n}\n\nif (rowIndex === 0 && colIndex === 2) {\n  return {\n    // 合并2列\n    colSpan: 2,\n  };\n}\n} */',
      container: {
        height: 88,
      },
    },
    titleTip: false,
    showCopyData: false,
    enableFieldSelect: false,
    defaultSelectedFields: '',
    hasFullscreen: false,
    copyAsImg: false,
    height: null,
    isHeightAuto: true,
  },
};
