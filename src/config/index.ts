// import dotenv from 'dotenv';
import { getLocal } from '@ali/yc-utils';
import { Config } from '../types/types';

export const DEFAULT_APP_TYPE = window?.__defaultCodeGenAppType || 'APP_WN5BIJCYKYB8KQKSXEA7';
export const DEFAULT_DEBUG_PAGE_ID = 'FORM-EE6E2B1CBEAB4DE9B033BD625E5395FA147I';
export const DEFAULT_APP_PREVIEW_FORM_UUID = 'FORM-9C1A8AAB8BC3435094C6B4161C69C068KGRL';

// 浏览器环境中的配置管理
// 加载环境变量
// dotenv.config();

/** @type {*} */
const defaultConfig: Config = {
  openaiApiKey: getLocal('bailian-sk') || 'sk-626f4cd588d643d0bf4f9464366dbd6b',
  deepseekV3ApiKey: getLocal('deepseek-sk') || '***********************************', // xiayang deepseek-v3
  openRouterApiKey:
    getLocal('openRouter-sk') || '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',
  googleApiKey: 'xxx',
  googleSearchEngineId: 'xxx',
  logLevel: 'info',
  maxSteps: 20,
  defaultTimeout: 30000,
};

// 从本地存储加载配置
function loadConfigFromStorage(): Partial<Config> {
  try {
    const storedConfig = localStorage.getItem('manusConfig');
    return storedConfig ? JSON.parse(storedConfig) : {};
  } catch (error) {
    console.error('Failed to load config from localStorage:', error);
    return {};
  }
}

// 保存配置到本地存储
function saveConfigToStorage(config: Config): void {
  try {
    localStorage.setItem('manusConfig', JSON.stringify(config));
  } catch (error) {
    console.error('Failed to save config to localStorage:', error);
  }
}

// 配置验证
function validateConfig(config: Config): void {
  if (!config.openaiApiKey) {
    console.warn('OpenAI API key is not set');
  }

  if (config.maxSteps < 1) {
    throw new Error('maxSteps must be greater than 0');
  }

  if (config.defaultTimeout < 1000) {
    throw new Error('defaultTimeout must be at least 1000ms');
  }
}

// 合并默认配置和存储的配置
const mergedConfig: Config = {
  ...defaultConfig,
  ...loadConfigFromStorage(),
};

// 获取配置
export function getConfig(): Config {
  validateConfig(mergedConfig);
  return mergedConfig;
}

// 更新配置
export function updateConfig(updates: Partial<Config>): Config {
  const newConfig = { ...mergedConfig, ...updates };
  validateConfig(newConfig);
  saveConfigToStorage(newConfig);
  Object.assign(mergedConfig, updates);
  return newConfig;
}
