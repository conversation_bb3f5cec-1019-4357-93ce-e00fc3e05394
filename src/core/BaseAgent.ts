/* eslint-disable no-await-in-loop */
import * as Agent from '@/agents';
import { LLMService } from '@/services/llmService';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import * as Tool from '@/tools';
/* eslint-disable @typescript-eslint/member-ordering */
import { agentRunOptions, LLMConfig, streamType, ToolCall, ToolSchema } from '@/types/types';
import { AgentStatus } from '@/types/types';
import { log } from '@/utils/decorators/log';
import { Logger } from '@/utils/Logger';
import { Memory } from '@/utils/Memory';
import { MessageUtils } from '@/utils/Message';
import { ToolCollection } from '@/utils/ToolCollection';
import { UserFeedbackLogger } from '@/utils/UserFeedbackLogger';

export interface BaseAgentConfig {
  name: string;
  nameCn: string;
  description: string;
  welcomeMessage?: string;
  systemPrompt?: string;
  nextStepPrompt?: string;
  planningSystemPrompt?: string;
  planningSummarySystemPrompt?: string;
  planningSummaryUserPrompt?: string;
  isMainAgent?: boolean;
  parameters?: ToolSchema;
  /**
   * 所有的动态系统提示词需要在这里定义
   */
  dynamicPromptProvider?: {
    /**
     * 在规划之前执行的函数，返回的 string 会作为额外的上下文添加到系统提示词中
     */
    beforePlanning?: (this: BaseAgent, userContext: string) => Promise<string>;
  };
  tools?: ToolCollection | Array<keyof typeof Tool | null>;
  agents?: Array<keyof typeof Agent | null>;
  feedbackLogger?: UserFeedbackLogger;
  memory?: Memory;
  logger?: Logger;
  isStoppable?: boolean;
  maxMessages?: number;
  llmConfig?: LLMConfig;
  toolChoices?: 'none' | 'auto' | 'required';
  agentType?: 'BaseAgent' | 'PlanningAgent' | 'CoderAgent' | 'ManusAgent';
}
/**
 * 基础代理类 - 实现代理的核心功能
 */
export class BaseAgent {
  name: string;
  nameCn: string;
  description: string;
  feedbackLogger: UserFeedbackLogger;
  memory: Memory;
  status = AgentStatus.IDLE;
  tools: ToolCollection;
  toolChoices: BaseAgentConfig['toolChoices'];
  config: BaseAgentConfig;

  protected llm: LLMService;
  protected logger: Logger;
  protected systemPrompt: string;
  protected nextStepPrompt: string;
  protected toolCalls: ToolCall[] = [];
  protected specialTools: string[] = ['terminate'];
  protected isStopped = false;
  protected isFromPlanningAgent = false;
  protected agentType: BaseAgentConfig['agentType'];

  constructor(config: BaseAgentConfig) {
    this.config = config;
    this.name = config.name;
    this.nameCn = config.nameCn;
    this.description = config.description;
    this.logger = config.logger || new Logger();
    this.feedbackLogger = config.feedbackLogger || new UserFeedbackLogger();
    this.memory = config.memory || new Memory(config.maxMessages || 100);
    this.llm = new LLMService(config.llmConfig || {});
    this.systemPrompt = config.systemPrompt;
    this.nextStepPrompt = config.nextStepPrompt;
    this.tools = config.tools as ToolCollection;
    this.toolChoices = config.toolChoices || 'auto';
    // 确保工具集中包含必要的核心工具
    this.ensureSpecialTools();
  }
  async stopExecution(): Promise<void> {
    this.isStopped = true;
    const allMemory = this.memory.getAllMessages();
    const lastMessage = allMemory[allMemory.length - 1];
    if (lastMessage?.role === 'assistant') {
      this.memory.removeLastMessage();
    }
    this.memory.addMessage(MessageUtils.assistantMessage('⚠️ 用户已手动停止运行，未给出最终回复'));
    // 中断当前正在执行的工具
    if (this.tools) {
      this.tools.interrupt();
    }
    // 中断 LLM 服务
    if (this.llm) {
      this.llm.interrupt();
    }
    // 重置工具中断状态
    if (this.tools) {
      this.tools.resetInterrupt();
    }
    this.status = AgentStatus.FINISHED;
    this.logger.info('代理执行已停止');
  }
  /**
   * 运行代理
   * @param context 上下文信息
   * @returns 代理执行结果
   */
  @log()
  async run(context?: string, options?: agentRunOptions): Promise<string> {
    if (this.isStopped) return '';
    if (context) {
      this.memory.addMessage(MessageUtils.userMessage(context, options));
    }
    useAIAgentStateStore.getState().setCurrentExecutingAgent(this);
    // 执行步骤
    const results = await this.execute();
    this.status = AgentStatus.IDLE;
    this.logger.info('代理完成当前任务, 状态恢复为IDLE');
    return results.length ? results.join('\n') : '未执行任何步骤';
  }
  /**
   * 执行基础步骤
   * @returns 执行结果
   */
  @log()
  protected async execute(): Promise<string[]> {
    if (this.isStopped) return [];
    const shouldAct = await this.think();
    if (!shouldAct) {
      return ['思考完成 - 无需执行操作'];
    }
    const result = await this.act();
    return result ? [result] : [];
  }
  /**
   * 思考
   * @returns 是否需要执行操作
   */
  protected async think(): Promise<boolean> {
    if (this.isStopped) return false;
    // 设置状态为思考中
    this.status = AgentStatus.THINKING;
    this.logger.info('代理开始执行任务，状态设置为THINKING');
    // 添加下一步提示词
    if (this.nextStepPrompt) {
      const userMsg = MessageUtils.userMessage(this.nextStepPrompt);
      this.memory.addMessage(userMsg);
    }
    try {
      const messages = this.memory.getAllMessages();
      const systemPromptStr = `${this.systemPrompt}`;
      const systemMsg = this.systemPrompt ? [MessageUtils.systemMessage(systemPromptStr)] : ['empty'];

      // 使用流式输出
      const response = await this.llm.askStream(
        messages,
        systemMsg,
        this.tools.toParams(),
        'auto',
        (type: streamType, data: any) => {
          switch (type) {
            case streamType.REASONING:
              this.feedbackLogger.reasoning(data);
              break;
            case streamType.THINKING:
              this.feedbackLogger.thinking(data);
              break;
            case streamType.TOOL_CALL:
              this.feedbackLogger.toolCall(data.tool, data.args);
              break;
          }
        },
      );
      // 获取工具调用
      this.toolCalls = response.toolCalls || [];
      if (this.toolCalls.length > 0) {
        this.logger.info(`🛠️ ${this.name} 选择了 ${this.toolCalls.length} 个工具使用`);
        this.logger.info(`🧰 准备使用的工具: ${this.toolCalls.map((call) => call.function.name).join(', ')}`);
      } else if (response.content) {
        this.logger.info(`💡 ${this.name} 没有选择工具，但提供了内容`);
      }
      // 处理工具选择模式
      if (this.toolChoices === 'none') {
        if (this.toolCalls.length > 0) {
          this.logger.warning(`🤔 ${this.name} 尝试使用工具，但工具模式设置为 'none'`);
        }
        if (response.content) {
          this.memory.addMessage(MessageUtils.assistantMessage(response.content));
          return true;
        }
        return false;
      }
      // 创建助手消息
      const assistantMsg =
        this.toolCalls.length > 0
          ? MessageUtils.fromToolCalls(this.toolCalls, response.content)
          : MessageUtils.assistantMessage(response.content);
      this.memory.addMessage(assistantMsg);

      // 处理 'required' 模式
      if (this.toolChoices === 'required' && this.toolCalls.length === 0) {
        return true; // 会在act()中处理
      }
      return this.toolCalls.length > 0;
    } catch (error) {
      this.logger.error(`思考过程出错: ${error}`);
      return false;
    }
  }

  /**
   * 执行工具调用
   * @param command 工具调用命令
   * @returns 工具执行结果
   */
  protected async act(): Promise<string> {
    if (this.isStopped) return '';
    // 没有工具调用，但工具模式是required
    if (!this.toolCalls.length) {
      if (this.toolChoices === 'required') {
        throw new Error('需要工具调用，但没有收到任何工具调用');
      }
      // 如果没有工具调用，返回最后一条消息内容
      const lastMessage = this.memory.getRecentMessages(1)[0];
      const content = lastMessage?.content || '没有内容或命令需要执行';
      this.logger.info(
        `📝 ${this.name} 没有使用工具，返回内容: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`,
      );
      return content;
    }

    // 执行每个工具调用
    const results: string[] = [];
    for (const command of this.toolCalls) {
      const { name } = command.function || {};
      try {
        const result = await this.executeToolCall(command);
        // 执行特殊工具
        await this.handleSpecialTool(name);
        // 创建工具响应消息
        const toolMsg = MessageUtils.toolMessage(result, name, command.id);
        this.memory.addMessage(toolMsg);
        results.push(result);
      } catch (error) {
        const errorMessage = `工具 '${command.function.name}' 执行失败: ${
          error instanceof Error ? error.message : String(error)
        }`;
        this.logger.error(`🚨 ${errorMessage}`);
        // 添加错误消息到记忆中
        const toolMsg = MessageUtils.toolMessage(`错误: ${errorMessage}`, name, command.id);
        this.memory.addMessage(toolMsg);
        results.push(`错误: ${errorMessage}`);
      }
    }
    // 合并所有结果
    return results.join('\n\n');
  }

  /**
   * 执行工具调用
   * @param command 工具调用命令
   * @returns 工具执行结果
   */
  protected async executeToolCall(command: ToolCall): Promise<string> {
    if (this.isStopped) return '';
    const { name, arguments: preArgs } = command?.function || {};
    if (!name) return '错误: 无效的命令格式';
    if (!this.tools?.toolMap?.[name]) return `错误: 未知工具 '${name}'`;

    try {
      // 记录工具调用
      this.feedbackLogger.toolCall(command.function.name, command.function.arguments);

      // 解析参数
      let args: any;
      if (typeof preArgs === 'string') {
        try {
          // 清理控制字符
          const cleanArgStr = preArgs.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, '');
          args = JSON.parse(cleanArgStr);
        } catch (parseError) {
          this.logger.error(`JSON解析错误: ${parseError.message}`);
          this.logger.info(`原始参数字符串: ${preArgs}`);
          // 如果无法解析，尝试使用简单结构
          args = { response: preArgs };
        }
      } else {
        args = preArgs;
      }
      this.logger.info(`🔧 正在激活工具: '${name}'...`);
      this.logger.info(`工具"${name}"参数: ${JSON.stringify(args, null, 2)}`);

      // 设置状态为ACTING
      if (this.isStopped) return '';
      this.status = AgentStatus.ACTING;
      const result = await this.tools.execute(name, args, this);
      // 记录工具结果
      if (name !== 'step_complete' && this.tools.toolMap[name].agent?.agentType !== 'PlanningAgent') {
        this.feedbackLogger.toolResult(result);
      }
      // 记录工具执行结果
      this.logger.info(`工具 '${name}' 执行结果: ${JSON.stringify(result).substring(0, 50)}...`);
      return JSON.stringify(result) || '执行完成，无输出';
    } catch (error) {
      // 如果出错，也记录结果
      this.feedbackLogger.toolResult(`错误: ${error instanceof Error ? error.message : String(error)}`);
      const errorMsg = `⚠️ 工具 '${name}' 遇到问题: ${error}`;
      this.logger.error(errorMsg);
      return `错误: ${errorMsg}`;
    }
  }

  /**
   * 处理特殊工具
   * @param name 工具名称
   */
  protected handleSpecialTool(name: string) {
    if (!name) return;
    const isSpecialTool = this.specialTools.map((n) => n.toLowerCase()).includes(name.toLowerCase());
    if (!isSpecialTool) return;
    this.logger.info(`执行特殊工具: ${name}`);
    // 检查是否是终止工具
    if (name.toLowerCase() === 'terminate') {
      this.logger.info('收到终止命令');
      this.status = AgentStatus.FINISHED;
    }
    this.handleOtherSpecialTool(name);
  }

  /**
   * 确保必要的特殊工具在工具集中
   */
  protected ensureSpecialTools(): void {
    if (!this.tools?.toolMap) {
      this.logger.warning('工具集未正确初始化');
      return;
    }
    // 检查并确保terminate工具在工具集中存在
    // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
    const hasTerminateTool = Object.values(this.tools.toolMap).some(
      (tool) => tool?.name?.toLowerCase() === 'terminate',
    );
    if (!hasTerminateTool && this.specialTools.includes('terminate')) {
      this.tools.addTool(new Tool.TerminateTool());
    }
    // 确保其他特殊工具在工具集中
    this.ensureOtherSpecialTool();
  }
  /**
   * 处理其他特殊工具
   * @param name 工具名称
   */
  protected handleOtherSpecialTool(name: string): void {}
  /**
   * 确保其他特殊工具在工具集中
   */
  protected ensureOtherSpecialTool(): void {}
}
