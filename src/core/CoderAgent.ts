/* eslint-disable @typescript-eslint/member-ordering */

/* eslint-disable @iceworks/best-practices/recommend-polyfill */
import { createPage, getFormSchema, saveFormSchema } from '@/apis';
import { setCodeFn } from '@/components/Code/code-editor';
import { DEFAULT_APP_TYPE, DEFAULT_DEBUG_PAGE_ID } from '@/config';
import { CodeService } from '@/services/codeService';
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { useAIChatStore } from '@/store/aiChatStore';
import { IMessage, useAISettingStore } from '@/store/aiSettingStore';
import { extractPageCode, generatePageSchema, retrieveCodeInfo } from '@/tools/GenerateCodeTool/code';
import { streamType } from '@/types/types';
import { getLocal } from '@ali/yc-utils';

import { BaseAgent, BaseAgentConfig } from './BaseAgent';

import type { ChatMessage } from '@/utils/ChatManager';
import type { IPromptBuilderContext } from '@/services/llmService';
export type CoderScenarioType = 'page' | 'print';

export class CoderAgent extends BaseAgent {
  // 多步骤Agent私有属性
  protected maxSteps = 20;
  protected currentStep = 0;
  protected duplicateThreshold = 2;
  private pageId: string;
  /**
   * 生成的源代码
   */
  protected code: string = '';
  /**
   * CodeGen 场景
   * - page: 源代码页面生成
   * - print: 打印页面生成
   */
  protected scenario: CoderScenarioType = 'page';
  protected steps: string[] = ['创建页面', '生成页面代码', '保存页面信息', '预览页面'];

  constructor(config: BaseAgentConfig) {
    super(config);
    this.name = config.name || 'Coder';
    this.description = config.description || '代码生成工具人';
    this.specialTools = ['terminate', 'step_complete'];
  }
  /**
   * 运行代理
   * @param request 用户请求
   * @returns 代理执行结果
   */
  protected async execute(): Promise<string[]> {
    return [];
  }

  setScenario = (scenario: CoderScenarioType) => {
    this.scenario = scenario;
  };

  setCode = (code: string) => {
    this.code = code;
  };

  createPage = async (name: string) => {
    const data = await createPage({
      appType: DEFAULT_APP_TYPE,
      name,
    });
    if (!data?.pageId) {
      throw new Error('界面创建失败，请稍后重试');
    }
    this.pageId = data.pageId;
  };

  getPageSchema = async (pageId: string) => {
    const schemaRes = await getFormSchema({
      formUuid: pageId,
      appType: DEFAULT_APP_TYPE,
      schemaVersion: 'V5',
    });
    return schemaRes;
  };

  savePageSchema = async (pageId: string, schema: any) => {
    const res = await saveFormSchema({
      appType: DEFAULT_APP_TYPE,
      formUuid: pageId,
      content: schema,
      schemaVersion: 'V5',
    });
    return res;
  };

  setSteps = (steps: string[]) => {
    this.steps = steps;
  };

  getCurrentPageId = () => {
    // in debug mode, we use a fixed page id
    return this.pageId;
  };

  getAssistantMessage = (step: number, mode: 'create' | 'update' = 'create') => {
    const steps = this.steps;
    const realStep = mode === 'create' ? steps : steps.slice(1);
    const stepContent = realStep
      .map((item, index) => `${step > index ? '✅' : '⏳'} 步骤${index + 1}：${item}`)
      .join('\n');
    let progressBar = '';
    const percentage = Math.min(Math.round((step / realStep.length) * 100), 100);
    const progressWidth = 20;
    const filledBlocks = Math.floor(percentage / (100 / progressWidth));

    // 使用绿色字符表示已完成部分，灰色字符表示未完成部分
    for (let i = 0; i < progressWidth; i++) {
      progressBar += i < filledBlocks ? '🟩' : '⬜'; // 使用彩色方块emoji替代ANSI颜色代码
    }

    return `
开始执行页面生成:

${stepContent}\n

📊 执行进度: ${progressBar} ${step}/${realStep.length} (${percentage}%)\n
    `;
  };

  handleMessage = async (inputStr: string, messages: ChatMessage[], setMessages: (messages: ChatMessage[]) => void) => {
    const assistantMessage = {
      id: `assistant_${+new Date()}`,
      content: '',
      isUser: false,
      timestamp: new Date(),
    };
    const mode = this.pageId ? 'update' : 'create';
    if (!this.pageId) {
      assistantMessage.content = this.getAssistantMessage(0, mode);
      setMessages([...messages, assistantMessage]);

      await this.createPage('AI-Code测试页面');
      assistantMessage.content = this.getAssistantMessage(1, mode);
      if (getLocal('yida_manus_useTemplate')) {
        this.code = await retrieveCodeInfo(inputStr, 'template');
      }
    } else {
      assistantMessage.content = this.getAssistantMessage(0, mode);
    }
    setMessages([...messages, assistantMessage]);
    const history = messages
      .slice(-20)
      .map(({ isUser, content }) => ({ role: isUser ? 'user' : 'assistant', content }))
      .filter((item) => item.role === 'user');

    const messageList: IMessage[] = [
      {
        role: 'system',
        content: '{{render "code-gen.code-gen-entry"}}',
      },
      ...(history as IMessage[]),
    ];
    if (this.code) {
      messageList.push({
        role: 'user',
        content: `
            请基于当前内容进行修改，当前index.jsx代码为:
            \`\`\`jsx
            ${this.code || ''}
            \`\`\`
          `,
      });
    }
    const setArtifactModeFn = useAIArtifactStateStore.getState().setArtifactMode;
    setArtifactModeFn('code');
    useAIArtifactStateStore.getState().setIsCodeGenerated(false);
    const settings = useAISettingStore.getState();
    const service = new CodeService(
      {
        model: settings.model,
        temperature: parseFloat(settings.temperature || '1'),
      },
      settings.modelType,
      getLocal('yida_manus_sk') || 'sk-6a2de6a7f72b4da0968ba3f20cf685d4',
    );

    const systemMsg: any[] = [];
    const msgs: any[] = [];
    let systemFlag = false;
    messageList.forEach((item) => {
      if (item.role === 'system') {
        systemFlag = true;
        systemMsg.push(item);
      } else {
        msgs.push(item);
      }
    });

    const uiFramework = useAISettingStore.getState().uiFramework;
    const response = await service.askStream<IPromptBuilderContext>(
      msgs,
      systemMsg,
      [],
      'none',
      (type: streamType, data: any) => {
        if (type === streamType.THINKING) {
          setCodeFn(data, { mode: this.code ? 'update' : 'create' });
        }
      },
      false,
      {
        context: {
          isModifyCode: !!this.code,
          isDynamicDataSource: false,
          enableYidaComponents: false,
          enableAntd: uiFramework === 'antd',
          enableRadixUI: uiFramework === 'radix-ui',
          scenarioContext: '',
          appContext: '{}',
          pageContext: {
            basicInfo: '{}',
            cubeInfo: '{}',
            dataSourceAPIInfo: '{}',
          },
          scenario: {
            normal: true,
          },
        },
      },
    );
    try {
      assistantMessage.content = this.getAssistantMessage(mode === 'create' ? 2 : 1, mode);
      setMessages([...messages, assistantMessage]);
      const newCode = extractPageCode(response.content, this.code);
      this.code = newCode;
      useAIArtifactStateStore.getState().setCode(newCode, { mode: 'update' });
      useAIArtifactStateStore.getState().setIsCodeGenerated(true);
      const schema = await generatePageSchema(this.pageId, newCode, {});
      await this.savePageSchema(this.pageId, schema);
      assistantMessage.content = this.getAssistantMessage(mode === 'create' ? 3 : 2, mode);
      setMessages([...messages, assistantMessage]);
      await this.previewPage();
      assistantMessage.content = this.getAssistantMessage(mode === 'create' ? 4 : 3, mode);
      setMessages([...messages, assistantMessage]);
    } catch (error) {
      useAIArtifactStateStore.getState().setErrorMsg(error.message);
      useAIChatStore.getState().setIsProcessing(false);
    } finally {
      // reload the iframe
      useAIArtifactStateStore.getState().previewIframeRef?.contentWindow?.location.reload();
    }
  };

  async previewPage() {
    useAIArtifactStateStore.getState().setArtifactMode('preview');
    if (!this.pageId) {
      this.pageId = DEFAULT_DEBUG_PAGE_ID;
    }
    const previewUrl = `/${DEFAULT_APP_TYPE}/preview/${this.pageId}?navConfig.type=none`;
    // 发送预览地址更新消息
    window.postMessage(
      {
        type: 'previewReady',
        url: previewUrl,
      },
      '*',
    );
  }

  async think(): Promise<boolean> {
    return true;
  }

  async act(): Promise<string> {
    return '思考完成 - 无需执行操作';
  }
}
