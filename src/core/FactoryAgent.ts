import * as Agent from '@/agents';
import * as Tool from '@/tools';
import { Memory } from '@/utils/Memory';
import { FactoryAgentTool } from '@/utils/Tool';
import { ToolCollection } from '@/utils/ToolCollection';
import { UserFeedbackLogger } from '@/utils/UserFeedbackLogger';
import { BaseAgent, BaseAgentConfig } from './BaseAgent';
import { CoderAgent } from './CoderAgent';
import { PlanningAgent } from './PlanningAgent';
import { ToolSchema } from '@/types/types';

export interface AgentConfig extends BaseAgentConfig {
  feedbackLogger?: UserFeedbackLogger;
  memory?: Memory;
  tools?: Array<keyof typeof Tool | null>;
  agents?: Array<keyof typeof Agent | null>;
  parameters?: ToolSchema;
}

export class FactoryAgent {
  static createAgent(config: AgentConfig): BaseAgent {
    const { agentType, agents, tools, ...restConfig } = config;
    const baseConfig: BaseAgentConfig = {
      ...restConfig,
      tools: FactoryAgent.initTools(config),
    };
    switch (config.agentType) {
      case 'BaseAgent':
        return new BaseAgent(baseConfig);
      case 'PlanningAgent':
        return new PlanningAgent(baseConfig);
      case 'CoderAgent':
        return new CoderAgent(baseConfig);
      default:
        throw new Error(`未知的 Agent 类型: ${config.agentType}`);
    }
  }
  /**
   * 初始化工具
   * @param config 配置
   * @returns 工具
   */
  static initTools(config: AgentConfig): ToolCollection {
    const normalTools = config.tools.map((toolName: keyof typeof Tool) => {
      return new Tool[toolName]({
        logger: config.logger,
        feedbackLogger: config.feedbackLogger,
      });
    });
    const agentTools = config.agents.map((agentConfigName: keyof typeof Agent) => {
      const agentConfig = Agent[agentConfigName];
      const agent = FactoryAgent.createAgent({
        ...agentConfig,
        logger: config.logger,
        feedbackLogger: config.feedbackLogger,
      });
      return new FactoryAgentTool(agent);
    });
    return new ToolCollection(...agentTools, ...normalTools);
  }
}
