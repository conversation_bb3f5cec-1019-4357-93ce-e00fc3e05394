/* eslint-disable no-await-in-loop */
/* eslint-disable @typescript-eslint/member-ordering */
/* eslint-disable @iceworks/best-practices/recommend-polyfill */
import { AgentStatus } from '@/types/types';
import type { BaseAgentConfig } from './BaseAgent';
import { BaseAgent } from './BaseAgent';
import { log } from '@/utils/decorators/log';

export class ManusAgent extends BaseAgent {
  // 多步骤Agent私有属性
  protected maxSteps = 20;
  protected currentStep = 0;
  protected duplicateThreshold = 2;

  constructor(config: BaseAgentConfig) {
    super(config);
    this.name = config.name || 'Manus';
    this.description = config.description || '一个能够使用多种工具解决各种任务的多功能代理';
    this.specialTools = ['terminate', 'step_complete'];
    this.agentType = 'ManusAgent';
  }

  /**
   * 运行代理
   * @param 循环运行用户请求
   * @returns 代理执行结果
   */
  @log()
  protected async execute(): Promise<string[]> {
    if (this.isStopped) return [];
    const results: string[] = [];
    try {
      let steps = 0;
      // 记录初始循环条件
      this.logger.info(
        `📝 [STEP] 开始运行循环，初始状态: 子步骤=${steps}, 最大子步骤=${this.maxSteps}, 状态=${this.status}`,
      );
      // 循环状态
      let isWhileStatus = true;
      while (isWhileStatus) {
        steps++;
        // 设置状态为思考中
        this.status = AgentStatus.THINKING;
        this.currentStep = steps;
        this.logger.info(`📝 [STEP] 正在执行子步骤 ${steps}/${this.maxSteps}, 当前状态: ${this.status}`);
        const executeResult = await super.execute();
        const stepResult = executeResult.join('\n');
        this.logger.info(
          `📝 [STEP] 子步骤 ${steps} 完成，结果: ${stepResult.substring(0, 100)}${
            stepResult.length > 100 ? '...' : ''
          }`,
        );
        this.logger.info(`📝 [STEP] 子步骤 ${steps} 执行后的状态: ${this.status}`);
        // 检查是否卡住
        if (this.isStuck()) {
          this.handleStuckState();
          this.logger.warning('检测到代理可能卡住，已添加额外提示以改变策略');
        }
        results.push(`子步骤 ${steps}: ${stepResult}`);
        // 处理特殊状态
        if ((this.status as string) === AgentStatus.NEXTSTEP) {
          this.logger.info('📝 [STEP] 检测到NEXTSTEP状态，跳过当前主步骤。');
        }
        // 更新循环状态
        isWhileStatus = steps < this.maxSteps && ![AgentStatus.FINISHED, AgentStatus.NEXTSTEP].includes(this.status);
        // 在循环结束前检查状态
        this.logger.info(`📝 [STEP] 子步骤 ${steps} 结束，状态=${this.status}, 继续循环=${isWhileStatus}`);
      }
      // 记录循环退出原因
      if (steps >= this.maxSteps) {
        this.logger.warning(`已达到最大子步骤数 (${this.maxSteps})`);
        results.push(`终止: 已达到最大子步骤数 (${this.maxSteps})`);
      } else if ((this.status as string) === AgentStatus.FINISHED) {
        this.logger.info('📝 [STEP] 代理已完成任务，状态设置为FINISHED');
        results.push('完成: 代理已成功完成任务');
      }
    } finally {
      this.currentStep = 0;
    }
    return results;
  }
  /**
   * 检查代理是否卡在循环中
   * 通过检测连续相同的助手回复来判断
   * @returns 是否处于卡住状态
   */
  protected isStuck(): boolean {
    const messages = this.memory.getAllMessages();
    // 至少需要两条消息才能判断是否卡住
    if (messages.length < 2) {
      return false;
    }
    const lastMessage = messages[messages.length - 1];
    // 如果最后一条消息没有内容，则不能判断是否卡住
    if (!lastMessage.content || lastMessage.role !== 'assistant') {
      return false;
    }
    // 计算前面出现相同内容的助手消息数量
    let duplicateCount = 0;
    for (let i = messages.length - 2; i >= 0; i--) {
      const msg = messages[i];
      if (msg.role === 'assistant' && msg.content === lastMessage.content) {
        duplicateCount++;
        if (duplicateCount >= this.duplicateThreshold) {
          return true;
        }
      }
    }
    return false;
  }
  /**
   * 处理代理卡住的状态
   * 通过向nextStepPrompt添加提示，鼓励代理尝试新的策略
   */
  private handleStuckState(): void {
    const stuckPrompt =
      '检测到重复响应。请考虑新的策略，避免重复已尝试过的无效路径，请调用 step_complete 或者 terminate 结束，或者尝试使用不同的工具或方法来解决问题。';
    // 确保nextStepPrompt存在
    if (!this.nextStepPrompt) {
      this.nextStepPrompt = '';
    }
    this.nextStepPrompt = `${stuckPrompt}`;
    this.logger.warning(`代理检测到卡住状态。添加提示: ${stuckPrompt}`);
  }
  /**
   * 处理特殊工具
   * @param name 工具名称
   */
  handleOtherSpecialTool(name: string) {
    // 处理step_complete工具，标记当前步骤完成
    if (name.toLowerCase() === 'step_complete') {
      this.logger.info('收到步骤完成命令');
      // 设置状态为NEXTSTEP，表示当前步骤完成
      this.status = AgentStatus.NEXTSTEP;
    }
  }
  /**
   * 确保其他特殊工具在工具集中
   */
  ensureOtherSpecialTool(): void {
    // // 检查PlanningTool工具在工具集中存在
    // const hasPlanningTool = Object.values(this.tools.toolMap).some(
    //   (tool) => tool.name.toLowerCase() === 'planning'
    // );
    // if (!hasPlanningTool) {
    //   this.tools.addTool(new PlanningTool());
    // }
  }
}
