/* eslint-disable no-await-in-loop */
/* eslint-disable @typescript-eslint/member-ordering */
import { BaseAgent, BaseAgentConfig } from '@/core/BaseAgent';
import { PlanningTool } from '@/tools/PlanningTool';
import { AgentStatus, PlanStepStatus, streamType } from '@/types/types';
import { log } from '@/utils/decorators/log';
import { MessageUtils } from '@/utils/Message';
import { FileData } from '@/utils/UserFeedbackCard';
import { uniqueId } from '@ali/ve-utils';

import { ManusAgent } from './ManusAgent';

import type { PlanData, Message, StepInfo, ExecutionResult, PlanStatusData } from '@/types/types';
export class PlanningAgent extends BaseAgent {
  // 私有属性
  private planningTool: PlanningTool;
  private activePlanId: string;
  private currentStepIndex: number | null = null;
  private maxSteps = 20;
  private maxRetries = 3;
  private retryDelay = 1000; // 1秒
  private manusAgent: BaseAgent;
  private planningSystemPrompt: string;
  private planningSummarySystemPrompt: string;
  private planningSummaryUserPrompt: string;

  constructor(config: BaseAgentConfig) {
    // 调用父类构造函数
    super(config);
    this.name = config.name || 'PlanningAgent';
    this.description = config.description || '一个能够使用多种工具解决各种任务的多功能代理';
    this.specialTools = [];
    this.agentType = 'PlanningAgent';
    // PlanningAgent特有的初始化逻辑
    this.activePlanId = `plan${uniqueId(null, '', '')}_${Date.now()}`;
    this.planningTool = new PlanningTool();
    this.manusAgent = new ManusAgent(config);
    this.planningSystemPrompt = config.planningSystemPrompt;
    this.planningSummarySystemPrompt = config.planningSummarySystemPrompt;
    this.planningSummaryUserPrompt = config.planningSummaryUserPrompt;
  }

  /**
   * 展示任务计划结果
   * @param planStatus 计划状态字符串
   */
  protected logPlanResult(planStatus: string): void {
    if (planStatus && planStatus.length > 100) {
      this.logger.info(`计划状态: ${planStatus.substring(0, 100)}...`);
    } else {
      this.logger.info(`计划状态: ${planStatus}`);
    }
  }

  protected updateFeedbackLogger(): void {
    // 设置日志的主AgentId
    this.feedbackLogger.setMainPlanAgentId(this.activePlanId);
    // 设置日志是否处于主Agent状态
    const isMainPlanAgent = this.activePlanId === this.feedbackLogger.getMainPlanAgentId();
    this.feedbackLogger.setIsMainPlanAgent(isMainPlanAgent);
  }
  /**
   * 执行计划
   * @returns 执行结果数组
   */
  @log()
  protected async execute(): Promise<string[]> {
    if (this.isStopped) return [];
    this.updateFeedbackLogger();
    try {
      if (!this.manusAgent) {
        throw new Error('没有可用的manus代理');
      }
      // 只在控制台记录日志
      this.logger.info('开始执行任务计划...');
      // 向用户提供反馈
      this.feedbackLogger.response('开始分析你的请求...');
      // 获取用户输入
      const userMessages = this.memory.getAllMessages();
      const lastUserMessage = userMessages.find((msg) => msg.role === 'user');
      const inputText = lastUserMessage?.content || '';
      if (inputText) {
        const planResult = await this.createInitialPlan(inputText);
        if (!planResult?.success) {
          const errorMsg = `创建计划失败: ${planResult?.message}`;
          this.logger.error(errorMsg);
          this.feedbackLogger.response(`抱歉，我无法为你的请求创建执行计划: ${planResult?.message}`);
          return []; // 返回空数组，不向用户显示错误
        }
        this.logger.info('初始计划创建成功');
        // 获取并显示生成的计划
        const planData = await this.getCurrentPlanData();
        if (planData) {
          this.feedbackLogger.response('根据需求分析，为你制定如下计划：');
          // 初始化计划状态 - 确保步骤数量正确
          const statusData = {
            title: planData.title,
            completed: 0,
            total: planData.steps.length,
            steps: planData.steps.map((step, index) => ({
              description: step,
              status: index === 0 ? 'in_progress' : 'not_started',
            })),
          };
          this.feedbackLogger.plan(statusData as PlanStatusData);
        }
      }
      // 开始执行计划
      let stepCount = 0;
      // 获取计划总步骤数
      const planData = await this.getCurrentPlanData();
      const totalPlanSteps = planData ? planData.steps.length : this.maxSteps;
      this.logger.info(`计划总步骤数: ${totalPlanSteps}`);
      // 确保不超过最大步骤数和计划总步骤数
      const maxIterations = Math.min(this.maxSteps, totalPlanSteps);
      this.logger.info(`最大执行步骤数: ${maxIterations}`);
      while (stepCount < maxIterations && !this.isStopped) {
        // 获取当前步骤信息
        const [stepIndex, stepInfo] = await this.getCurrentStepInfo();
        this.currentStepIndex = stepIndex;
        // 如果没有找到下一个可执行的步骤或已经完成所有步骤，退出循环
        if (stepIndex === null || !stepInfo) {
          break;
        }
        const stepMsg = `开始执行步骤 ${stepIndex + 1}: ${stepInfo.text}`;
        this.logger.info(stepMsg);
        try {
          // 执行步骤，包含重试逻辑
          await this.executeWithRecovery(stepInfo);
          if (this.manusAgent.status === AgentStatus.FINISHED || this.isStopped) {
            break;
          }
          // 继续下一个步骤
          stepCount++;
        } catch (error) {
          const errorMsg = `执行步骤 ${stepIndex + 1} 时出错: ${error}`;
          this.logger.error(errorMsg);
          continue;
        }
        // 每执行完一个步骤，更新计划状态
        const updatedPlanData = await this.getCurrentPlanData();
        if (updatedPlanData) {
          const completedSteps = updatedPlanData.stepStatuses.filter(
            (status) => status === PlanStepStatus.COMPLETED,
          ).length;
          const statusData = {
            title: updatedPlanData.title,
            completed: completedSteps,
            total: updatedPlanData.steps.length,
            steps: updatedPlanData.steps.map((step, index) => ({
              description: step,
              status: updatedPlanData.stepStatuses[index],
            })),
          };
          this.feedbackLogger.plan(statusData);
        }
      }
      if (this.isStopped) return [];
      // 更新日志状态
      this.updateFeedbackLogger();
      // 如果循环是因为达到最大步骤数而结束的，则记录警告
      if (stepCount > maxIterations && this.currentStepIndex !== null) {
        const warningMsg = `已达到最大步骤数 ${maxIterations}，强制结束执行`;
        this.logger.warning(warningMsg);
      }
      // 记录完成状态，包含最终结果
      this.feedbackLogger.thinking('当前计划步骤执行完成，整体计划执行结果总结如下：');

      // 无论如何执行最终总结
      const finalResult = await this.finalizePlan();

      // 记录完成状态，包含最终结果
      this.feedbackLogger.complete('当前计划执行全部完成');

      // 计划结束时也需要包含统计信息
      const startTime = Number(this.activePlanId.split('_')[2]);
      const executionTime = Math.round((Date.now() - startTime) / 1000);
      const finalPlanData = await this.getCurrentPlanData();
      const completedSteps =
        finalPlanData?.steps.filter((_, index) => {
          return finalPlanData.stepStatuses[index] === PlanStepStatus.COMPLETED;
        }).length || 0;
      // 计算工具调用次数 (从feedbackLogger中获取)
      const toolCallsCount = this.feedbackLogger
        ? this.feedbackLogger.getStepLogs().filter((log) => log.type === 'tool_call').length
        : 0;
      // 格式化总执行时间
      let totalTimeFormatted = '';
      if (executionTime < 60) {
        totalTimeFormatted = `${executionTime}秒`;
      } else {
        const minutes = Math.floor(executionTime / 60);
        const seconds = executionTime % 60;
        totalTimeFormatted = `${minutes}分${seconds}秒`;
      }
      const executionData = {
        totalTime: totalTimeFormatted,
        stepsCompleted: completedSteps,
        toolCallsCount,
        planId: this.activePlanId,
      };
      this.feedbackLogger.planEnd('计划步骤已全部执行完成，感谢你的耐心等待。', executionData);
      // 重置当前步骤索引
      this.currentStepIndex = null;
      // 返回最终结果
      return [finalResult || '暂无'];
    } catch (error) {
      const errorMsg = `执行任务计划时出错: ${error}`;
      this.logger.error(errorMsg);
      this.feedbackLogger.response(`执行过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
      // 确保重置当前步骤索引
      this.currentStepIndex = null;
      return [errorMsg];
    }
  }

  /**
   * 获取当前计划数据
   * @returns 计划数据
   */
  private async getCurrentPlanData(): Promise<PlanData | null> {
    if (this.isStopped) return null;
    try {
      this.logger.info(`尝试获取计划数据，planId: ${this.activePlanId}`);

      if (!this.activePlanId) {
        this.logger.error('获取计划数据失败：activePlanId为空');
        return null;
      }

      const result = await this.planningTool.execute({
        command: 'get',
        planId: this.activePlanId,
      });

      this.logger.info(`获取到计划文本: ${result.substring(0, 300)}...`);

      if (typeof result === 'string') {
        const planData = this.parsePlanText(result);
        this.logger.info(`解析后计划数据: ${planData ? '成功' : '为null'}`);
        return planData;
      }

      return result as PlanData;
    } catch (error) {
      this.logger.error(`获取计划数据失败: ${error}`);
      return null;
    }
  }

  private async executeWithRecovery(stepInfo: StepInfo): Promise<string> {
    if (this.isStopped) return '';
    let lastError: Error | null = null;
    let result = '';
    for (let retryCount = 0; retryCount < this.maxRetries; retryCount++) {
      try {
        if (this.isStopped) return '';
        if (retryCount > 0) {
          const retryMsg = `第 ${retryCount + 1} 次重试执行步骤...`;
          this.logger.info(retryMsg);
          result += `${retryMsg}\n`;
        }
        const stepResult = await this.executeStep(stepInfo);
        result += `${stepResult}\n`;
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        const errorMsg = `步骤执行失败 (尝试 ${retryCount + 1}/${this.maxRetries}): ${error}`;
        this.logger.error(errorMsg);
        result += `${errorMsg}\n`;
        if (retryCount < this.maxRetries - 1) {
          const delay = this.retryDelay * (retryCount + 1);
          const delayMsg = `等待 ${delay / 1000} 秒后重试...`;
          this.logger.info(delayMsg);
          result += `${delayMsg}\n`;
          await new Promise((resolve) => setTimeout(resolve, delay));
          continue;
        }
        if (this.currentStepIndex !== null) {
          try {
            const blockMsg = '已达到最大重试次数，标记步骤为阻塞状态';
            this.logger.warning(blockMsg);
            result += `${blockMsg}\n`;
            await this.planningTool.execute({
              command: 'mark_step',
              planId: this.activePlanId,
              stepIndex: this.currentStepIndex,
              stepStatus: PlanStepStatus.BLOCKED,
              stepNotes: `执行失败: ${lastError.message}`,
            });
          } catch (markError) {
            const markErrorMsg = `标记步骤状态失败: ${markError}`;
            this.logger.error(markErrorMsg);
            result += `${markErrorMsg}\n`;
          }
        }
      }
    }
    throw lastError || new Error('步骤执行失败，已达到最大重试次数');
  }

  private async createInitialPlan(request: string): Promise<ExecutionResult | null> {
    if (this.isStopped) return null;
    this.logger.info(`正在创建初始计划，ID: ${this.activePlanId}`);
    const { dynamicPromptProvider } = this.config;
    let dynamicPlanningPrompt = '';
    let systemMessagePrompt = this.planningSystemPrompt;
    if (dynamicPromptProvider?.beforePlanning) {
      const fileData: FileData = {
        id: `file${uniqueId(null, '', '')}`,
        type: 'file',
      };
      this.feedbackLogger.file('分析需求生成 PRD 中...', fileData);
      dynamicPlanningPrompt = await dynamicPromptProvider.beforePlanning.call(this, request);
      this.feedbackLogger.file('分析需求生成 PRD 已完成', fileData);
      // 构建第一条 User Message
      const userMessageForManus: Message = MessageUtils.userMessage(
        `完整PRD描述如下: \n${dynamicPlanningPrompt || '暂无'}`,
      );
      systemMessagePrompt += `\n## Extra Context\n${dynamicPlanningPrompt || '暂无'}`;
      this.manusAgent.memory.addMessage(userMessageForManus);
    } else {
      this.manusAgent.memory.addMessage(MessageUtils.userMessage(request));
    }

    const systemMessage: Message = MessageUtils.systemMessage(systemMessagePrompt);
    const userMessage: Message = MessageUtils.userMessage(`创建一个合理的计划来完成以下任务 : ${request}`);
    try {
      const response = await this.llm.askStream(
        [userMessage],
        [systemMessage],
        [
          {
            type: 'function',
            function: {
              name: this.planningTool.name,
              description: this.planningTool.description,
              parameters: this.planningTool.toParam(),
            },
          },
        ],
        'auto',
        undefined,
      );
      if (response.toolCalls) {
        for (const toolCall of response.toolCalls) {
          if (toolCall.function.name === 'planning') {
            const args =
              typeof toolCall.function.arguments === 'string'
                ? JSON.parse(toolCall.function.arguments)
                : toolCall.function.arguments;

            args.planId = this.activePlanId;
            const result = await this.planningTool.execute({
              command: 'create',
              planId: this.activePlanId,
              ...args,
            });
            this.logger.info(`计划创建结果: ${result}`);
            return { success: true, message: '计划创建成功' };
          }
        }
      }
      this.logger.info('创建默认计划');
      await this.planningTool.execute({
        command: 'create',
        planId: this.activePlanId,
        title: `计划: ${request.slice(0, 50)}${request.length > 50 ? '...' : ''}`,
        steps: ['分析请求', '执行任务', '验证结果'],
      });
      return { success: true, message: '默认计划创建成功' };
    } catch (error) {
      this.logger.error(`创建初始计划时出错: ${error}`);
      return { success: false, message: `创建计划失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private async getCurrentStepInfo(): Promise<[number | null, StepInfo | null]> {
    try {
      const planText = await this.planningTool.execute({
        command: 'get',
        planId: this.activePlanId,
      });
      const planData = await this.parsePlanText(planText);
      if (!planData) {
        return [null, null];
      }
      // 首先获取计划的总步骤数
      const totalSteps = planData.steps.length;
      // 检查是否所有步骤都已完成
      const allCompleted = planData.stepStatuses.every(
        (status) => status === PlanStepStatus.COMPLETED || status === PlanStepStatus.BLOCKED,
      );
      if (allCompleted) {
        this.logger.info('所有步骤已完成或被阻塞，没有可执行的步骤');
        return [null, null];
      }
      // 首先检查是否有IN_PROGRESS状态的步骤，这应该优先处理
      for (let i = 0; i < totalSteps; i++) {
        if (planData.stepStatuses[i] === PlanStepStatus.IN_PROGRESS) {
          const step = planData.steps[i];
          const typeMatch = step.match(/\[([A-Z_]+)\]/);
          // 不需要再标记为IN_PROGRESS，因为它已经是这个状态了
          return [
            i,
            {
              text: step.replace(/\[[^\]]*\]/g, '').trim(),
              type: typeMatch ? typeMatch[1].toLowerCase() : undefined,
            },
          ];
        }
      }
      // 如果没有IN_PROGRESS的步骤，则寻找第一个NOT_STARTED的步骤
      for (let i = 0; i < totalSteps; i++) {
        if (planData.stepStatuses[i] === PlanStepStatus.NOT_STARTED) {
          const step = planData.steps[i];
          const typeMatch = step.match(/\[([A-Z_]+)\]/);
          // 标记为IN_PROGRESS状态
          await this.planningTool.execute({
            command: 'mark_step',
            planId: this.activePlanId,
            stepIndex: i,
            stepStatus: PlanStepStatus.IN_PROGRESS,
          });
          return [
            i,
            {
              text: step.replace(/\[[^\]]*\]/g, '').trim(),
              type: typeMatch ? typeMatch[1].toLowerCase() : undefined,
            },
          ];
        }
      }
      // 没有找到活动或未开始的步骤，说明所有步骤已完成或被阻塞
      this.logger.info('没有找到活动或未开始的步骤，所有步骤已完成或被阻塞');
      return [null, null];
    } catch (error) {
      this.logger.error(`查找当前步骤时出错: ${error}`);
      return [null, null];
    }
  }

  /**
   * 执行单个步骤
   * @param stepInfo 步骤信息
   * @returns 步骤执行结果
   */
  @log()
  private async executeStep(stepInfo: StepInfo): Promise<string> {
    if (this.isStopped) return '';
    try {
      // 重要：确保记录正确的步骤序号，并检查该步骤是否有效
      if (this.currentStepIndex === null) {
        throw new Error('无效的步骤序号');
      }
      // 获取当前计划状态，检查步骤是否已完成
      const planStatus = await this.planningTool.execute({
        command: 'get',
        planId: this.activePlanId,
      });
      // 解析计划文本，检查步骤状态
      const planData = await this.parsePlanText(planStatus);
      if (
        planData &&
        this.currentStepIndex < planData.steps.length &&
        planData.stepStatuses[this.currentStepIndex] === PlanStepStatus.COMPLETED
      ) {
        // 步骤已完成，返回提示信息
        return `步骤 ${this.currentStepIndex + 1} 已完成，无需重复执行`;
      }
      // 记录步骤开始
      await this.feedbackLogger.stepStart(stepInfo.text, this.currentStepIndex);
      let result = '';
      result += '获取当前计划状态...\n';
      this.logPlanResult(planStatus);
      result += `=== 当前计划状态 ===\n${planStatus}\n===================\n`;
      // 过滤掉之前的计划步骤
      this.manusAgent.memory.clearPlanUserMessage();
      const stepPrompt = `${planStatus}\n\n你正在执行步骤 ${this.currentStepIndex + 1}: "${stepInfo.text}"`;
      this.logger.info('开始执行步骤...');
      result += '开始执行步骤...\n';
      const stepResult = await this.manusAgent.run(stepPrompt, { isPlanMsg: true });
      await this.markStepCompleted();
      await this.feedbackLogger.stepEnd();
      const completeMsg = '步骤执行完成';
      this.logger.info(completeMsg);
      result += `${completeMsg}\n`;
      result += `${stepResult}\n`;
      return result;
    } catch (error) {
      const errorMsg = `执行步骤 ${
        this.currentStepIndex !== null ? this.currentStepIndex + 1 : '未知'
      } 时出错: ${error}`;
      this.logger.error(errorMsg);
      throw error;
    }
  }

  private async markStepCompleted(): Promise<void> {
    if (this.isStopped) return;
    if (this.currentStepIndex !== null) {
      try {
        // 检查步骤的当前状态
        const planStatus = await this.planningTool.execute({
          command: 'get',
          planId: this.activePlanId,
        });

        const planData = await this.parsePlanText(planStatus);
        if (
          planData &&
          this.currentStepIndex < planData.steps.length &&
          planData.stepStatuses[this.currentStepIndex] !== PlanStepStatus.COMPLETED
        ) {
          // 只有当步骤未完成时才标记为完成
          await this.planningTool.execute({
            command: 'mark_step',
            planId: this.activePlanId,
            stepIndex: this.currentStepIndex,
            stepStatus: PlanStepStatus.COMPLETED,
          });
          this.logger.info(`步骤 ${this.currentStepIndex + 1} 已自动标记为完成`);
        } else {
          this.logger.info(`步骤 ${this.currentStepIndex + 1} 已完成，无需重复标记`);
        }
      } catch (error) {
        this.logger.error(`标记步骤完成时出错: ${error}`);
      }
    }
  }

  private async finalizePlan(): Promise<string> {
    if (this.isStopped) return '';
    try {
      // 通知开始生成总结状态
      this.feedbackLogger.notifySummaryStatus(true);
      this.logger.info('所有步骤已完成，正在生成总结...');
      // 获取执行计划和步骤状态
      const planText = await this.planningTool.execute({
        command: 'get',
        planId: this.activePlanId,
      });
      this.logPlanResult(planText);
      if (!this.planningSummarySystemPrompt) return '';
      // 构建更全面的系统提示词
      const systemMessage: Message = {
        role: 'system',
        content: this.planningSummarySystemPrompt,
      };
      // 构建包含全面上下文的用户消息
      const userMessage: Message = {
        role: 'user',
        content: this.planningSummaryUserPrompt,
      };
      // 获取LLM响应
      this.manusAgent.memory.addMessage(userMessage);
      const userMessages = this.manusAgent.memory.getAllMessages();
      const response = await this.llm.askStream(
        userMessages,
        [systemMessage],
        this.manusAgent.tools.toParams(),
        'auto',
        (type: streamType, data: any) => {
          this.feedbackLogger.toolResult(data);
        },
        true,
        {},
      );
      // 格式化结果
      const summary = typeof response === 'string' ? response : response?.content || JSON.stringify(response, null, 2);
      // 通知总结生成完成
      this.feedbackLogger.notifySummaryStatus(false);
      return summary;
    } catch (error) {
      this.logger.error(`使用 LLM 完成计划时出错: ${error}`);
      const result = '计划完成。生成总结时出错。请查看执行详情了解更多信息。';
      // 出错时也需要通知总结状态结束
      this.feedbackLogger.notifySummaryStatus(false);
      return result;
    }
  }

  private async parsePlanText(planText: string): Promise<PlanData | null> {
    try {
      const lines = planText.split('\n');
      const titleMatch = lines[0].match(/计划: (.*?) \(ID: (.*?)\)/);
      if (!titleMatch) {
        return null;
      }
      const steps: string[] = [];
      const stepStatuses: PlanStepStatus[] = [];
      const stepNotes: string[] = [];
      let inSteps = false;
      for (const line of lines) {
        if (line.startsWith('步骤:')) {
          inSteps = true;
          continue;
        }
        if (inSteps) {
          const stepMatch = line.match(/^\d+\. (\[[^\]]+\]) (.*)/);
          if (stepMatch) {
            const status = this.parseStatusMark(stepMatch[1]);
            const step = stepMatch[2];
            steps.push(step);
            stepStatuses.push(status);
            stepNotes.push('');
          } else if (line.startsWith('   备注: ')) {
            stepNotes[stepNotes.length - 1] = line.substring(8);
          }
        }
      }
      return {
        title: titleMatch[1],
        steps,
        stepStatuses,
        stepNotes,
      };
    } catch (error) {
      this.logger.error(`解析计划文本时出错: ${error}`);
      return null;
    }
  }

  private parseStatusMark(mark: string): PlanStepStatus {
    switch (mark) {
      case '[✓]':
        return PlanStepStatus.COMPLETED;
      case '[→]':
        return PlanStepStatus.IN_PROGRESS;
      case '[!]':
        return PlanStepStatus.BLOCKED;
      default:
        return PlanStepStatus.NOT_STARTED;
    }
  }
}
