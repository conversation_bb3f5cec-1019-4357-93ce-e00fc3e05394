import { useEffect, useRef } from 'react';
import { useAIChatStore } from '@/store/aiChatStore';

interface TextChange {
  path: string;
  before: string;
  after: string;
  element: string;
  diff?: {
    added?: string[];
    removed?: string[];
    unchanged?: string[];
  };
}

interface TextModificationsMessage {
  type: 'aiCanvas.textModifications';
  modifications: TextChange[];
}

/**
 * Hook to listen for text modifications from the native-editing component
 * and insert a special message into the chatbox
 */
export const useAINativeEditing = () => {
  const setMessages = useAIChatStore((s) => s.setMessages);
  const messages = useAIChatStore((s) => s.messages);
  const chatManagerRef = useAIChatStore((s) => s.chatManagerRef);

  // Flag to prevent duplicate messages
  const lastModificationId = useRef<string | null>(null);

  // Inject styles for edit messages
  useEffect(() => {
    injectEditMessageStyles();
  }, []);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const data = event.data as TextModificationsMessage;
      if (data?.type === 'aiCanvas.textModifications' && data.modifications?.length > 0) {
        // Generate a unique ID based on modification content
        const modificationId = JSON.stringify(data.modifications);

        // Skip if we've already processed this exact set of modifications
        if (lastModificationId.current === modificationId) {
          return;
        }
        lastModificationId.current = modificationId;

        // Create a formatted summary of changes
        const changesSummary = data.modifications
          .map((change) => {
            return `- 修改了 ${change.element} 元素: "${truncateText(change.before, 20)}" → "${truncateText(
              change.after,
              20,
            )}"`;
          })
          .join('\n'); // Create a special message for our component - match expected ChatMessage type
        const specialMessage = {
          id: `edit-${Date.now()}`,
          isUser: true,
          timestamp: new Date(),
          content: `### 编辑了以下内容\n${changesSummary}\n\n<button class="edit-action-button" data-modifications="${encodeURIComponent(
            JSON.stringify(data.modifications),
          )}">根据上述用户变更做修改</button>`,
          isSpecialEditMessage: 'true', // Must be a string to match ChatMessage type
          isSystemLog: false,
        };

        // Add the new message to the chat
        // The EditMessageResponse component will handle the button click event
        setMessages([...messages, specialMessage]);
      }
    };

    // Listen for messages from any source (including iframes)
    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [messages, setMessages, chatManagerRef]);
};

// Helper function to truncate text for display
function truncateText(text: string, maxLength: number): string {
  if (!text) return '';
  return text.length <= maxLength ? text : `${text.slice(0, maxLength - 3)}...`;
}

// Helper function to add styles for the edit message component
// This ensures styles are injected even if the CSS file hasn't loaded yet
function injectEditMessageStyles() {
  const styleId = 'edit-message-styles';
  if (!document.getElementById(styleId)) {
    const styleEl = document.createElement('style');
    styleEl.id = styleId;
    styleEl.textContent = `
      .edit-action-button {
        background-color: #3b82f6;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        margin-top: 10px;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s;
      }
      .edit-action-button:hover {
        background-color: #2563eb;
      }
      .edit-message-response {
        background-color: rgba(59, 130, 246, 0.05);
        padding: 12px;
        border-radius: 4px;
        border-left: 3px solid #3b82f6;
        margin-top: 8px;
      }
    `;
    document.head.appendChild(styleEl);
  }
}
