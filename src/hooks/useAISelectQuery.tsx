import { useEffect, useRef } from 'react';
import { useAIChatStore } from '@/store/aiChatStore';

export function useAISelectQuery() {
  const setMessages = useAIChatStore((s) => s.setMessages);
  const messages = useAIChatStore((s) => s.messages);

  // Ref to store selection message ID
  const selectionMessageIdRef = useRef<string | null>(null);

  useEffect(() => {
    // Function to handle element selection messages from the element-selector
    const handleElementSelected = (event: MessageEvent) => {
      if (event.data.type === 'aiCanvas.elementSelected') {
        const element = event.data.element;

        // Create a formatted message with element details
        const formattedContent = `
I want to modify Selected element:
- Tag: ${element.tagName}
- Classes: ${element.classes || 'none'}
- ID: ${element.id || 'none'}
- Path: ${element.path}
- Text: ${element.text || 'none'}
Based on my query. DO NOT modify the outer area content.
        `.trim();

        const now = new Date();

        // Always create a new message for each selection to keep history
        const selectionMessageId = `selection-${Date.now()}`;
        selectionMessageIdRef.current = selectionMessageId;

        // Add the new selection message to the chat
        setMessages([
          ...messages,
          {
            id: selectionMessageId,
            isUser: true,
            isSystemLog: true,
            content: formattedContent,
            isSpecialSelectNodeMessage: 'true',
            timestamp: now,
          },
        ]);
      } else if (event.data.type === 'aiCanvas.selectionModeDisabled') {
        // When selection mode is disabled, we no longer need to track the current selection ID
        // but we keep all selection messages in history
        selectionMessageIdRef.current = null;
      }
    };

    // Add event listener for messages from element-selector iframe
    window.addEventListener('message', handleElementSelected);

    // Clean up event listener
    return () => {
      window.removeEventListener('message', handleElementSelected);
    };
  }, [messages, setMessages]);
}
