export const mockGenerateComplexHelloWorldPage = async () => {
  return Promise.resolve({
    content: `HelloWorld 页面说明：
炫酷欢迎页

实现具有视觉吸引力的动态欢迎页面，包含入场动效、交互式按钮和动态文本展示能力

页面组成（组件表）

* 组件名称：AnimationComponent
* 功能简述：实现首屏入场动画效果
* 组件属性：
  | 字段名称       | 类型                              | 说明                     |
  |----------------|-----------------------------------|--------------------------|
  | duration       | number                            | 动画持续时间（毫秒）     |
  | animationType  | 'fade' | 'slide' | 'zoom'       | 动画类型                 |
  | onAnimationEnd | () => void                        | 动画结束回调函数         |

  \`\`\`typescript
  interface AnimationProps {
    duration: number;
    animationType: 'fade' | 'slide' | 'zoom';
    onAnimationEnd?: () => void;
  }
    \`\`\`

* 其它要求：需要支持 CSS 动画库（如 framer-motion），初始渲染时自动触发动画

* 组件名称：WelcomeText
* 功能简述：动态展示欢迎词
* 组件属性：
  | 字段名称   | 类型          | 说明                     |
  |------------|---------------|--------------------------|
  | text       | string        | 展示的文本内容           |
  | textColor  | string        | 文字颜色（HEX/RGB）      |
  | fontSize   | number        | 字体大小（px）           |
  | animate    | boolean       | 是否启用渐显动画         |

  \`\`\`typescript
  interface WelcomeTextProps {
    text: string;
    textColor: string;
    fontSize: number;
    animate?: boolean;
  }
    \`\`\`

* 其它要求：文本需要支持响应式布局，适配不同屏幕尺寸

* 组件名称：ButtonComponent
* 功能简述：交互式动态按钮
* 组件属性：
  | 字段名称     | 类型                             | 说明                     |
  |--------------|----------------------------------|--------------------------|
  | buttonText   | string                           | 按钮显示文本             |
  | onClick      | () => void                       | 点击事件处理器           |
  | buttonType   | 'primary' | 'secondary'          | 按钮样式类型             |
  | hoverEffect  | boolean                          | 是否启用悬停动效         |

  \`\`\`typescript
  interface ButtonProps {
    buttonText: string;
    onClick: () => void;
    buttonType?: 'primary' | 'secondary';
    hoverEffect?: boolean;
  }
    \`\`\`

其它备忘

* 页面层通过以下方式组合组件：
  \`\`\`tsx



    \`\`\`
* 动画组件与文本组件通过独立的状态控制，避免联动依赖
* 颜色方案使用霓虹灯风格（cyberpunk）配色方案
`,
    error: '',
    thinking: '',
  } as any);
};
