export const mockGenerateLandingPage = async () => {
  return Promise.resolve(`
import { Theme } from "@radix-ui/themes";
import DynamicIcon from "lucide-react";
import { motion } from "framer-motion";
import { HeroSection } from "./HeroSection.component";
import { KeyFeatures } from "./KeyFeatures.component";
import { AdvantageSection } from "./AdvantageSection.component";
import { CallToAction } from "./CallToAction.component";

export const YidaComp = (props) => {
  const heroProps = {
    title: "钉钉宜搭 - 企业级低代码开发平台",
    subtitle: "快速构建企业应用，无需编码，随需而变",
    backgroundUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  };

  const featureList = [
    {
      iconUrl: "https://img.icons8.com/ios-filled/100/4a90e2/cloud-sync.png",
      title: "低代码开发",
      description: "通过可视化拖拽快速构建企业应用，无需专业开发经验"
    },
    {
      iconUrl: "https://img.icons8.com/ios-filled/100/4a90e2/mobile-app.png",
      title: "移动适配",
      description: "自动适配PC和移动端，随时随地访问应用"
    },
    {
      iconUrl: "https://img.icons8.com/ios-filled/100/4a90e2/api-settings.png",
      title: "开放集成",
      description: "丰富的API接口，轻松对接钉钉和企业现有系统"
    },
    {
      iconUrl: "https://img.icons8.com/ios-filled/100/4a90e2/security-checked.png",
      title: "企业级安全",
      description: "基于钉钉的企业级安全体系，保障数据安全"
    }
  ];

  const advantageList = [
    {
      imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&auto=format&fit=crop",
      title: "可视化搭建",
      description: "通过拖拽组件即可快速构建企业级应用，无需编写代码，降低技术门槛",
      isImageLeft: true
    },
    {
      imageUrl: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&auto=format&fit=crop",
      title: "丰富组件库",
      description: "内置100+企业级组件，覆盖表单、表格、图表等常见业务场景",
      isImageLeft: false
    },
    {
      imageUrl: "https://images.unsplash.com/photo-1555774698-0b77e0d5fac6?w=800&auto=format&fit=crop",
      title: "多端适配",
      description: "一次搭建，多端适配，自动生成PC和移动端界面，提升开发效率",
      isImageLeft: true
    }
  ];

  const ctaProps = {
    primaryLabel: "开始使用",
    secondaryLabel: "查看文档",
    onPrimaryClick: () => console.log("Primary button clicked"),
    onSecondaryClick: () => console.log("Secondary button clicked")
  };

  return (
    <Theme appearance="light" accentColor="blue" radius="large">
      <div className="flex flex-col gap-12 pb-12">
        <HeroSection {...heroProps} />
        <KeyFeatures features={featureList} />
        <AdvantageSection advantages={advantageList} />
        <div className="px-4">
          <CallToAction {...ctaProps} />
        </div>
      </div>
    </Theme>
  );
};
      `);
};
//   }
