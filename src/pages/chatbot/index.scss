/* 聊天界面样式 */
:root {
  --primary-color: #d6d8ff;
  --secondary-color: #52c41a;
  --background-color: #f7f8fa;
  --card-background: #ffffff;
  --text-color: #333333;
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  --transition-speed: 0.3s;
  --input-background: #f5f5f7;
  --input-border-radius: 12px;
  --button-border-radius: 50%;
  --waiting-color: #faad14;
  --resizer-width: 6px;
  --resizer-color: #eee;
  --resizer-hover-color: #ddd;
}
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background-color);
  .vc-rootconten {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
}
.chat-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  position: relative;
  overflow: hidden;
  height: 100vh;
  .next-card-title {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0 !important;
    font-weight: normal;
    &:before {
      display: none;
    }
  }
  .normal-card {
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    background-color: var(--card-background);
  }
  .preview-card {
    flex: 1;
    height: 100%;
    margin-left: 12px;
  }
  .next-card-body {
    height: calc(100% - 48px);
    .next-card-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px;
      border: 1px solid rgba(126, 134, 142, 0.16);
      border-radius: 6px;
      .messages-container {
        flex: 1;
        overflow-y: auto;
      }
    }
  }
  /* 当右侧面板隐藏时，让左侧区域居中 */
  &.hidden {
    display: none;
  }
  &.right-panel-hidden {
    .chat-card {
      margin: 0 auto;
    }
    .preview-card {
      width: 0 !important;
      flex: 0;
      min-width: 0;
      transition: width 0.3s ease;
    }
  }
}
.chat-card {
  width: 480px;
  height: 100%;
  display: flex;
  flex-direction: column;
  will-change: transform, width;
  transition: width 0.1s ease;
  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  /* 聊天头部样式 */
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 10px 16px;
    position: relative;
    .sidebar-toggle2 {
      margin: 5px 10px 0 0;
      cursor: pointer;
      z-index: 1000;
      img {
        width: 20px;
        height: 20px;
      }
    }
    .title {
      font-size: 14px;
      color: var(--text-color);
      // font-weight: bold;
    }
    .title-area {
      align-items: center;
      overflow: hidden;
      max-width: calc(100% - 50px);
      .title {
        cursor: pointer;
      }
    }
    .header-actions {
      display: flex;
      align-items: center;
      z-index: 11;
      height: 40px;
      .header-button {
        display: flex;
        margin-left: 10px;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: rgba(23, 26, 29, 0.6);
        &:hover {
          color: #0089ff;
        }
      }
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: var(--card-background);
  scroll-behavior: smooth;
  & > div {
    padding: 20px 0;
  }
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
  .message {
    display: flex;
    margin-bottom: 24px;
    animation: fadeIn 0.3s ease;
    flex-direction: column;
    &.user-message {
      .message-header {
        flex-direction: row-reverse;
        align-self: flex-end;
      }
      .message-content {
        border-radius: var(--border-radius);
        align-self: flex-end;
        background-color: var(--primary-color);
        color: white;
        border-top-right-radius: 4px;
      }
    }
    &.ai-message {
      .message-header {
        align-self: flex-start;
      }
      .message-content {
        .message-text {
          border-left: 3px solid #e5e6e8;
        }
      }
    }
    .message-header {
      display: flex;
      gap: 8px;
      margin-bottom: 10px;
      font-size: 12px;
      align-items: center;
      .message-avatar {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform var(--transition-speed) ease;
        overflow: hidden;
        &:hover {
          transform: scale(1.05);
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .message-sender {
        color: #5c5e60;
      }
      .message-header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        .message-time {
          font-size: 10px;
          color: #999;
        }
      }
    }
    .message-content {
      max-width: 90%;
      background-color: #fff;
      padding: 12px 0;
      .user-response {
        color: #000;
        sup {
          display: none;
        }
        .file-wrap {
          display: flex;
          flex-direction: row;
          gap: 10px;
          flex-wrap: wrap;
          .file-img {
            max-width: 100%;
            max-height: 200px;
          }
          .file-tump {
            max-width: 100px;
            .file-name {
              font-size: 12px;
              height: 20px;
              line-height: 20px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
            img {
              width: 60px;
              height: 60px;
              display: block;
            }
          }
        }
      }
      .message-text {
        font-size: 14px;
        line-height: 1.5;
        padding: 0 12px;
        white-space: pre-wrap;
        word-break: break-word;
        pre {
          margin: 0;
          padding: 15px;
          background-color: #f5f5f5;
          border-radius: 5px;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
          font-size: 13px;
        }
        .thinking-content {
          margin: 10px 0;
          padding: 0 10px;
          pre {
            margin: 10px 0;
            background-color: #f8f9fa;
            border: 1px solid #eaecef;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          }
        }
      }
      .edit-message-response {
        background-color: rgba(59, 130, 246, 0.05);
        padding: 12px;
        border-radius: 4px;
        border-left: 3px solid #3b82f6;
      }
    }
  }
}

/* Edit message styles */
.edit-message-response {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  h3 {
    color: #3b82f6;
    font-size: 16px;
    margin-bottom: 10px;
  }

  button.edit-action-button {
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 10px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;

    &:hover {
      background-color: #2563eb;
    }

    &:active {
      background-color: #1d4ed8;
    }
  }
}

/* Edit message response component styles */
.edit-message-response {
  background-color: rgba(59, 130, 246, 0.05);
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  h3 {
    color: #3b82f6;
    font-size: 16px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 12px;
  }

  .edit-changes-list {
    margin-bottom: 16px;

    .edit-change-item {
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      p {
        margin: 4px 0;
      }

      .edit-change-diff {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-left: 16px;
        margin-top: 4px;

        .edit-before {
          color: #dc2626;
        }

        .edit-after {
          color: #16a34a;
        }

        .edit-label {
          font-weight: 500;
          margin-right: 4px;
        }
      }
    }
  }

  .edit-action-button {
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin-top: 10px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #2563eb;
    }

    &:active {
      background-color: #1d4ed8;
      transform: translateY(1px);
    }
  }
}

.assistant-response {
  word-wrap: break-word;
  white-space: pre-wrap;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
  background: none;
  border: none;
  overflow: visible;
  h2 {
    display: inline-block;
    font-size: 15px;
    line-height: 3em;
  }
  h3 {
    display: inline-block;
    font-size: 14px;
    line-height: 2.5em;
  }
  img {
    max-width: 100%;
    max-height: 200px;
    margin-right: 5px;
    margin-bottom: 5px;
  }
  pre {
    font-size: 13px;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
    border: 1px solid #e1e4e8;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
  }
  .thinking-content {
    margin: 10px 0;
    padding: 0 10px;
    pre {
      margin: 10px 0;
      background-color: #f8f9fa;
      border: 1px solid #eaecef;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
  }
  // 进度条样式
  &:contains('█') {
    .progress-bar {
      height: 10px;
      border-radius: 5px;
      background: linear-gradient(90deg, var(--primary-color) var(--progress), #e0e0e0 var(--progress));
      margin: 5px 0;
    }
  }
  // 步骤样式
  .step {
    padding: 5px 0;
    margin: 2px 0;
    border-left: 2px solid #f0f0f0;
    padding-left: 10px;
    &.active {
      border-left-color: var(--primary-color);
      background-color: rgba(78, 110, 242, 0.05);
    }
    &.completed {
      border-left-color: var(--secondary-color);
    }
  }
}

/* ReactMarkdown样式 */
.assistant-response-markdown {
  code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
  }
  pre {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
  }
  blockquote {
    border-left: 4px solid #ddd;
    margin-left: 0;
    padding-left: 16px;
    color: #555;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
  }
  ul,
  ol {
    padding-left: 24px;
  }
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f5f5f5;
    }
  }
  img {
    max-width: 100%;
    height: auto;
  }
}

/* DeepSeek 风格的输入框容器 */
.bottom-container {
  padding: 6px 16px 16px;
  position: relative;
  .action-buttons {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin: 0 0 4px 12px;
    .next-btn {
      font-size: 12px;
      color: rgba(23, 26, 29, 0.6);
      &:hover {
        color: #0089ff;
      }
    }
  }
}

/* 等待用户回复的提示框样式 */
.waiting-response-container {
  margin: 16px 0;
  .waiting-card {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    box-shadow: 0 2px 8px rgba(250, 173, 20, 0.2);
    border-radius: 8px;
    animation: pulse 2s infinite;
  }
  .next-card-body {
    padding: 0 !important;
  }
  .waiting-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: var(--waiting-color);
    font-weight: bold;
    .countdown-timer {
      flex: 1;
      text-align: right;
      margin-left: auto;
      align-items: center;
      font-size: 14px;
      .countdown-text {
        color: #666;
        margin-right: 4px;
      }
      .countdown-number {
        color: #333;
        font-weight: 500;
        min-width: 40px;
        text-align: center;
        &.warning {
          color: #ff4d4f;
          animation: blink 1s infinite;
        }
      }
    }
  }
  .waiting-icon {
    margin-right: 8px;
    vertical-align: middle;
    display: inline-block;
  }
  .waiting-question {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 8px;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    border-left: 3px solid var(--waiting-color);
  }
  .waiting-instruction {
    display: flex;
    align-items: center;
    margin-top: 8px;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    color: #333;
    font-size: 13px;
    border-left: 3px solid #52c41a;
  }
  .instruction-icon {
    margin-right: 8px;
    color: #52c41a;
    animation: bounce 1s infinite;
  }
}

.select-mode {
  .next-menu-item-inner {
    font-size: 12px;
  }
}

/* 设置对话框样式 */
.next-dialog {
  border-radius: var(--border-radius);
  overflow: hidden;
  .next-dialog-body {
    padding: 24px;
  }
  .next-form-item {
    margin-bottom: 16px;
  }
  .next-input {
    width: 100%;
    border-radius: 4px;
  }
  .next-btn {
    border-radius: 4px;
    transition: all var(--transition-speed) ease;
  }
  .next-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

/* 加载动画 */
.loading-indicator {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 16px;
  &:after {
    content: ' ';
    display: block;
    width: 12px;
    height: 12px;
    margin: 2px;
    border-radius: 50%;
    border: 2px solid #fff;
    border-color: #fff transparent #fff transparent;
    animation: loading-indicator 1.2s linear infinite;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-card {
    height: calc(100vh - 40px);
    max-width: 100%;
  }
  .message {
    max-width: 85%;
  }
  .chat-header span {
    font-size: 16px;
  }
  .message-input {
    padding: 15px 45px 10px 15px !important;
    min-height: 45px !important;
  }
  .send-button {
    right: 25px;
    bottom: 20px;
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
  }
}

@media (max-width: 480px) {
  .chat-container {
    padding: 10px;
  }
  .messages-container {
    padding: 10px;
  }
  .message-avatar {
    width: 32px;
    height: 32px;
  }
  .message-content {
    max-width: calc(100% - 40px);
    padding: 8px 12px;
  }
  .tool-buttons {
    margin-right: 5px;
    gap: 5px;
  }
  .tool-button {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
  }
}

/* 系统日志消息样式 */
.system-log {
  background-color: #f0f8ff !important;
  border-left: 3px solid #4e6ef2;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  opacity: 0.9;
  transition: all 0.3s ease;
  .message-text {
    color: #666;
    min-height: 20px;
    position: relative;
    white-space: pre-wrap;
    &::after {
      content: '|';
      position: absolute;
      right: -2px;
      animation: cursor-blink 1s infinite;
    }
  }
  .message-sender {
    color: #5c5e60;
  }
  &:hover {
    opacity: 1;
  }
}

/* 思考中指示器样式 */
.thinking-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--waiting-color);
  margin: 8px 0;
  opacity: 0.8;
}
.thinking-icon {
  width: 20px;
  height: 20px;
  margin-right: 2px;
  vertical-align: middle;
  display: inline-block;
  background: url('//img.alicdn.com/imgextra/i2/O1CN01UtvKf51i0LvNp44v4_!!6000000004350-54-tps-72-72.apng') no-repeat
    center center;
  background-size: 20px 20px;
}

/* 右侧面板样式 */
.right-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  top: 8px;
}

.right-panel-actions {
  display: flex;
  align-items: center;
}

.vertical-divider {
  height: 15px;
  width: 1px;
  background-color: #e8e8e8;
  margin: auto 12px;
}

.next-btn.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(23, 26, 29, 0.6);
  &:hover {
    color: #0089ff;
  }
}

/* 当右侧面板隐藏时，让左侧区域居中 */
.chat-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  background-color: var(--background-color);
  position: relative;
  overflow: hidden;
  height: 100vh;
  &.right-panel-hidden {
    .chat-card {
      margin: 0 auto;
    }
    .preview-card {
      width: 0 !important;
      flex: 0;
      min-width: 0;
      transition: width 0.3s ease;
    }
  }
}

/* Selection message styles */
.selection-message {
  background-color: rgba(249, 115, 22, 0.05);
  border-left: 3px solid #f97316 !important;

  .message-content {
    padding: 8px 12px;
  }

  .message-text {
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 13px;
    color: #333;
  }

  .message-header {
    margin-bottom: 4px;
  }
}

@keyframes highlight-pulse {
  0% {
    background-color: rgba(249, 115, 22, 0.05);
  }
  50% {
    background-color: rgba(249, 115, 22, 0.1);
  }
  100% {
    background-color: rgba(249, 115, 22, 0.05);
  }
}

.selection-message {
  animation: highlight-pulse 2s infinite;
}

@keyframes cursor-blink {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(250, 173, 20, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0);
  }
}
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.33);
    opacity: 0;
  }
  80%,
  100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}
@keyframes pulse-dot {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
  }
}
@keyframes loading-indicator {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
#iframe-preview {
  width: 100%;
  height: 100%;
  border: none;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Selection message styles */
.selection-message {
  background-color: rgba(249, 115, 22, 0.05);
  border-left: 3px solid #f97316 !important;

  .message-content {
    padding: 8px 12px;
  }

  .message-text {
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 13px;
    color: #333;
  }

  .message-header {
    margin-bottom: 4px;
  }

  animation: highlight-pulse 2s infinite;
}

@keyframes highlight-pulse {
  0% {
    background-color: rgba(249, 115, 22, 0.05);
  }
  50% {
    background-color: rgba(249, 115, 22, 0.1);
  }
  100% {
    background-color: rgba(249, 115, 22, 0.05);
  }
}
