import './index.scss';

/* eslint-disable react/no-deprecated */
import React, { useCallback, useEffect, useRef, useState } from 'react';

import {
  AppQuickChatBotConfig,
  ExcelAgentConfig,
  ManusChatBotConfig,
  PolymindAgentConfig,
  ToolBotConfig,
} from '@/agents';
import ChatBoxInput from '@/components/ChatBoxInput';
import ChooseTemplate from '@/components/ChooseTemlate';
import MessageComponent from '@/components/Message';
import { SettingDialog } from '@/components/SettingDialog';
import { CoderAgent } from '@/core/CoderAgent';
import { AgentConfig } from '@/core/FactoryAgent';
import { useAINativeEditing } from '@/hooks/useAINativeEditing';
import { useAISelectQuery } from '@/hooks/useAISelectQuery';
import { useAIChatStore } from '@/store/aiChatStore';
import { ChatManager } from '@/utils/ChatManager';
import { Logger } from '@/utils/Logger';
import { isPreEnv, setLocal } from '@ali/yc-utils';
import { initAICanvasWebC } from '@ali/yida-webc';
import { Icon, Message } from '@alifd/next';

import RightArtifact from './right';

const isOnlineDEVMode = location.search.includes('SHOW_DEV_PANEL=true');

export const ChatBot: React.FC<any> = ({ visible, toggleSidebar, setVisibleChatBot }) => {
  // 聊天消息状态
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const messages = useAIChatStore((s) => s.messages) || [];
  const setMessages = useAIChatStore((s) => s.setMessages);

  const setInputValue = useAIChatStore((s) => s.setChatInputValue);
  const isProcessing = useAIChatStore((s) => s.isProcessing);
  const setIsProcessing = useAIChatStore((s) => s.setIsProcessing);
  const setIsWaitingForResponse = useAIChatStore((s) => s.setIsWaitingForResponse);
  const isWaitingForResponse = useAIChatStore((s) => s.isWaitingForResponse);
  const currentAgent = useAIChatStore((s) => s.agentType);
  const setCurrentAgent = useAIChatStore((s) => s.setAgentType);
  const setCoderAgent = useAIChatStore((s) => s.setCoderAgent);
  const setChatInputRef = useAIChatStore((s) => s.setChatInputRef);
  const setChatManagerRef = useAIChatStore((s) => s.setChatManagerRef);
  const setMsgContainerRef = useAIChatStore((s) => s.setMsgContainerRef);
  const setEnableAutoScroll = useAIChatStore((s) => s.setEnableAutoScroll);
  const countdownInterval = useAIChatStore((s) => s.countdownInterval);
  const setCountdownInterval = useAIChatStore((s) => s.setCountdownInterval);
  const isUserFocusedOnInput = useAIChatStore((s) => s.isUserFocusedOnInput);

  // Initialize AI Native Editing hook to listen for text modification messages
  useAINativeEditing();

  // Initialize AI Select Query hook to capture element selection events
  useAISelectQuery();

  const isRightPanelVisible = useAIChatStore((s) => s.isRightPanelVisible);
  const setRightPanelVisible = useAIChatStore((s) => s.setRightPanelVisible);
  const countdown = useAIChatStore((s) => s.countdown);
  const setCountdown = useAIChatStore((s) => s.setCountdown);

  const activeOutputTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const inputRef = useRef<any>(null);
  const chatManagerRef = useRef<any>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const shouldAutoScrollRef = useRef<boolean>(true);
  const containerRef = useRef<HTMLDivElement>(null);

  // 创建单页面内使用state
  const [isActiveOutput, setIsActiveOutput] = useState(false);
  const [stopAutoScroll, setStopAutoScroll] = useState(false);
  const [isSummarizing, setIsSummarizing] = useState(false);
  const [settingVisible, setSettingVisible] = useState(false);
  const [initScroll, setInitScroll] = useState(false);

  // 自动滚动到底部的处理函数
  const scrollToBottom = useCallback(() => {
    // 禁用强制滚动功能，仅当shouldAutoScroll为true时才滚动
    const container = messagesContainerRef.current;
    if (container && shouldAutoScrollRef.current && !stopAutoScroll) {
      setTimeout(() => {
        container.scrollTop = container.scrollHeight;
      }, 0);
    }
  }, [stopAutoScroll]);

  // 监听消息变化，控制滚动
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 创建 ChatManager
  const createChatManager = useCallback(
    (agentConfig: AgentConfig) => {
      const callbacks = {
        setMessages, // 使用原始的setMessages函数
        setIsProcessing,
        setIsSummarizing,
      };
      return new ChatManager(agentConfig, callbacks);
    },
    [setMessages, setIsProcessing, setIsSummarizing],
  );

  useEffect(() => {
    setMessages([]);
    setLocal('yida_manus_currentAgent', currentAgent);
    if (currentAgent !== 'page-print') {
      (async () => {
        if (!window.webc) {
          // eslint-disable-next-line require-atomic-updates
          await initAICanvasWebC();
        }
      })();
    }
    if (currentAgent === 'page-no-thinking') {
      chatManagerRef.current = new CoderAgent({
        logger: new Logger(),
        tools: [],
        systemPrompt: '',
        nextStepPrompt: '',
        llmConfig: {
          maxTokens: 7000,
        },
      } as any);
      setCoderAgent(chatManagerRef.current);
    } else if (currentAgent === 'app_quick') {
      chatManagerRef.current = createChatManager(AppQuickChatBotConfig);
    } else if (currentAgent === 'tool') {
      chatManagerRef.current = createChatManager(ToolBotConfig);
    } else if (currentAgent === 'polymind') {
      chatManagerRef.current = createChatManager(PolymindAgentConfig);
    } else if (currentAgent === 'excel') {
      chatManagerRef.current = createChatManager(ExcelAgentConfig);
    } else {
      chatManagerRef.current = createChatManager(ManusChatBotConfig);
    }
    // 自动聚焦输入框
    setTimeout(() => {
      if (inputRef.current?.textarea) {
        inputRef.current.textarea.focus();
      }
    }, 500);
  }, [createChatManager, currentAgent, setCoderAgent, setMessages]);

  // 添加滚动事件监听
  useEffect(() => {
    const handleScroll = () => {
      const container = messagesContainerRef.current;
      if (container) {
        const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
        const isAtBottom = scrollBottom < 50;
        if (isAtBottom !== shouldAutoScrollRef.current) {
          shouldAutoScrollRef.current = isAtBottom;
          // console.log(`滚动状态：${isAtBottom ? '已恢复自动滚动' : '已禁用自动滚动'}, 距底部: ${scrollBottom}px`);
        }
      }
    };
    const container = messagesContainerRef.current;
    if (initScroll && container) {
      container.addEventListener('scroll', handleScroll);
    }
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [initScroll]);

  // 清空输入框
  const clearInputValue = useCallback(() => {
    setInputValue('');
    if (inputRef.current?.textarea) {
      inputRef.current.textarea.value = '';
    }
  }, []);

  // 监听 globalThis.waitingForUserResponse 变化
  useEffect(() => {
    const checkWaitingState = () => {
      const newWaitingState = Boolean(globalThis.waitingForUserResponse);
      if (newWaitingState !== isWaitingForResponse) {
        // 首次从非等待状态变为等待状态
        if (newWaitingState && !isWaitingForResponse) {
          // 仅在首次变为等待状态且用户没有聚焦输入框时清空输入框
          if (!isUserFocusedOnInput) {
            clearInputValue();
            // 聚焦输入框
            if (inputRef.current?.textarea) {
              inputRef.current.textarea.focus();
            }
          }
          // 重置倒计时
          setCountdown(30);
        } else if (!newWaitingState && isWaitingForResponse) {
          // 清除倒计时
          if (countdownInterval) {
            clearInterval(countdownInterval);
            setCountdownInterval(null);
          }
        }
        // 更新状态
        setIsWaitingForResponse(newWaitingState);
      }
    };
    // 初始检查
    checkWaitingState();
    // 设置一个更长的间隔来减少干扰，只是为了捕获状态变化
    const intervalId = setInterval(checkWaitingState, 2000);
    return () => {
      clearInterval(intervalId);
    };
  }, [isWaitingForResponse, clearInputValue, isUserFocusedOnInput, setIsWaitingForResponse, countdownInterval]);

  // 监听 isActiveOutput 变化，控制倒计时
  useEffect(() => {
    if (globalThis.waitingForUserResponse && !isActiveOutput) {
      // 当等待框显示时，启动倒计时
      let currentCountdown = countdown;
      const interval = setInterval(() => {
        if (currentCountdown <= 1) {
          clearInterval(interval);
          // 倒计时结束时，清除等待状态
          globalThis.waitingForUserResponse = undefined;
          setIsWaitingForResponse(false);
          setCountdown(0);
        } else {
          currentCountdown--;
          setCountdown(currentCountdown);
        }
      }, 1000);
      setCountdownInterval(interval);
    }
  }, [isActiveOutput, globalThis.waitingForUserResponse]);

  // 监听 isProcessing 变化
  useEffect(() => {
    // 当任务完成时（isProcessing 从 true 变为 false），确保不显示运行中
    if (!isProcessing) {
      setIsActiveOutput(false);
      // 清除之前的定时器
      if (activeOutputTimeoutRef.current) {
        clearTimeout(activeOutputTimeoutRef.current);
        activeOutputTimeoutRef.current = null;
      }
    }
  }, [isProcessing]);

  // 处理双击消息容器
  const handleDoubleClick = useCallback(() => {
    // 双击消息容器不再进行任何滚动相关操作
    setStopAutoScroll(!stopAutoScroll);
    setEnableAutoScroll(!stopAutoScroll);
    console.log(`双击消息容器，停止自动滚动：${stopAutoScroll}`);
  }, [setEnableAutoScroll, stopAutoScroll]);

  useEffect(() => {
    setChatInputRef(inputRef as any);
    setChatManagerRef(chatManagerRef);
    setMsgContainerRef(messagesContainerRef);
    setEnableAutoScroll(true);
  }, [
    inputRef,
    chatManagerRef,
    messagesContainerRef,
    setChatInputRef,
    setChatManagerRef,
    setMsgContainerRef,
    setEnableAutoScroll,
  ]);

  // 获取思考状态文本
  const getThinkingStatus = () => {
    if (globalThis.waitingForUserResponse) {
      return '等待中';
    }
    if (isSummarizing) {
      return '总结中';
    }
    return '运行中';
  };

  // 监听消息变化，控制isActiveOutput
  useEffect(() => {
    if (messages.length > 0) {
      // 当有新消息时，设置为活动输出
      setIsActiveOutput(true);
      // 清除之前的定时器
      if (activeOutputTimeoutRef.current) {
        clearTimeout(activeOutputTimeoutRef.current);
        activeOutputTimeoutRef.current = null;
      }
      // 设置新的定时器，在一段时间后（例如1秒）将isActiveOutput设置为false
      activeOutputTimeoutRef.current = setTimeout(() => {
        setIsActiveOutput(false);
        activeOutputTimeoutRef.current = null;
      }, 1500);
    }
    // 清理函数
    return () => {
      if (activeOutputTimeoutRef.current) {
        clearTimeout(activeOutputTimeoutRef.current);
        activeOutputTimeoutRef.current = null;
      }
    };
  }, [messages]);

  // 日期格式化函数
  const formatTime = (date: Date): string => {
    const pad = (num: number) => String(num).padStart(2, '0');
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    return `${hours}:${minutes}`;
  };

  const goHomeHandler = () => {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.delete('scb');
    const url = window.location.origin + window.location.pathname + '?' + urlParams.toString();
    window.history.pushState({}, '', url);
    setVisibleChatBot(false);
  };

  return (
    <div
      className={`chat-container ${visible ? 'fade-in' : 'hidden'} ${!isRightPanelVisible ? 'right-panel-hidden' : ''}`}
      ref={containerRef}
    >
      <div className="chat-card normal-card">
        <div className="chat-header">
          <div className="sidebar-toggle2" onClick={() => toggleSidebar()}>
            <img
              src="https://img.alicdn.com/imgextra/i1/O1CN01WTdKn61kYMa0NaXjU_!!6000000004695-55-tps-20-20.svg"
              alt="展开侧边栏"
            />
          </div>
          <div className="title-area">
            <span className="title" onClick={goHomeHandler}>
              CodeGen{' '}
            </span>
          </div>
          <div className="header-actions">
            {(isPreEnv() || isOnlineDEVMode) && (
              <Icon type="set" size="small" className="header-button" onClick={() => setSettingVisible(true)} />
            )}
            {!isRightPanelVisible && (
              <Icon
                type="toggle-right"
                size="small"
                className="header-button"
                onClick={() => setRightPanelVisible(true)}
              />
            )}
          </div>
        </div>
        <div
          className="messages-container"
          ref={(ref: any) => {
            messagesContainerRef.current = ref;
            setInitScroll(true);
          }}
          onDoubleClick={handleDoubleClick}
        >
          <div style={{ display: 'none' }} className="thinking-icon" />
          <div>
            {messages.map((msg, index) => (
              <MessageComponent
                key={msg.id}
                message={msg}
                isProcessing={isProcessing}
                isActiveOutput={isActiveOutput}
                isLastMessage={index === messages.length - 1}
                formatTime={formatTime}
                getThinkingStatus={getThinkingStatus}
                countdown={countdown}
              />
            ))}
          </div>
        </div>
        <div className="bottom-container">
          <div className="action-buttons">
            <ChooseTemplate
              onSelectTemplate={(promptText: string) => {
                if (isProcessing) {
                  Message.warning('正在运行中，请稍候再试...');
                  return false;
                }
                setCurrentAgent('app_quick');
                setTimeout(() => {
                  try {
                    (async () => {
                      await chatManagerRef.current?.handleSendMessage(promptText);
                    })();
                  } catch (error) {
                    Message.error('处理失败，请重试');
                  }
                }, 500);
                return true;
              }}
            />
            {/* <Button text className="help-button" type="normal">
              <Icon type="help" size="small" /> 使用帮助
            </Button> */}
          </div>
          <ChatBoxInput inputRef={inputRef} chatManagerRef={chatManagerRef} />
        </div>
      </div>
      <RightArtifact />
      <SettingDialog visible={settingVisible} onClose={() => setSettingVisible(false)} />
    </div>
  );
};
