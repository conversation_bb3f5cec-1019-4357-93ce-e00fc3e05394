import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Card, Icon } from '@alifd/next';
import { Artifact } from '@/components/Artifacts';
import { useAIChatStore } from '@/store/aiChatStore';
import { ArtifactMode, useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { Segmented } from 'antd';

export const RightArtifact: React.FC = () => {
  const currentAgent = useAIChatStore((s) => s.agentType);
  const previewUrl = useAIArtifactStateStore((s) => s.artifactPreviewUrl);
  const artifactMode = useAIArtifactStateStore((s) => s.artifactMode);
  const setArtifactMode = useAIArtifactStateStore((s) => s.setArtifactMode);
  const setPreviewUrl = useAIArtifactStateStore((s) => s.setArtifactPreviewUrl);
  const setRightPanelVisible = useAIChatStore((s) => s.setRightPanelVisible);

  useEffect(() => {
    const handlePreviewReady = (event: MessageEvent) => {
      if (event.data?.type === 'previewReady' && event.data?.url) {
        if (previewUrl === event.data.url) return;
        setPreviewUrl(event.data.url);
        // const dom: any = window.document.querySelector('#iframe-preview');
        // dom?.contentWindow?.location.reload();
      }
    };
    window.addEventListener('message', handlePreviewReady);
    return () => {
      window.removeEventListener('message', handlePreviewReady);
    };
  }, [previewUrl, setPreviewUrl]);

  const printOpt = {
    label: '打印预览',
    value: 'print',
  };
  const previewOpt = {
    label: '预览',
    value: 'preview',
  };
  const previewOptions = currentAgent === 'page-print' ? printOpt : previewOpt;

  // 处理关闭面板的点击事件
  const handleClosePanel = () => {
    setRightPanelVisible(false);
  };

  return (
    <Card
      className="preview-card normal-card"
      title={
        <div className="right-panel-header">
          <Segmented
            options={[
              previewOptions,
              {
                label: 'PRD',
                value: 'prd',
              },
              {
                label: '代码',
                value: 'code',
              },
              {
                label: '日志',
                value: 'logger',
              },
            ]}
            value={artifactMode}
            onChange={(value) => {
              setArtifactMode(value as ArtifactMode);
            }}
          />
          <div className="right-panel-actions">
            {currentAgent !== 'page-print' ? (
              <>
                <Button
                  size="small"
                  disabled={!previewUrl}
                  onClick={() => {
                    let tarUrlPathname = previewUrl
                      .replace(/\/preview\//, '/workbench/')
                      .replace(/navConfig\.type=none/, '');
                    if (currentAgent === 'page-no-thinking') {
                      window.open(tarUrlPathname?.replace('?', '') + '?navConfig.type=none', '_blank');
                    } else {
                      // jump to workbench index page of the app to see the whole page
                      tarUrlPathname = tarUrlPathname.split('/').slice(0, 3).join('/');
                      window.open(tarUrlPathname + '/', '_blank');
                    }
                  }}
                >
                  <Icon type="fasong" size="small" /> 访问应用
                </Button>
                <Button
                  size="small"
                  disabled={!previewUrl}
                  style={{ marginLeft: 8 }}
                  onClick={() => {
                    const editUrl = previewUrl.replace(/(preview|workbench)/, 'admin');
                    window.open(editUrl, '_blank');
                  }}
                >
                  <Icon type="edit" size="small" /> 编辑应用
                </Button>
              </>
            ) : null}
            <div className="vertical-divider" />
            <Button className="close-button" size="medium" text onClick={handleClosePanel}>
              <Icon type="close" size="small" />
            </Button>
          </div>
        </div>
      }
      contentHeight="100%"
    >
      <Artifact />
    </Card>
  );
};

export default RightArtifact;
