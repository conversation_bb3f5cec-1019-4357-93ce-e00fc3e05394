.yida-home-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(120deg, #fff 0%, #f0f2ff 100%);
  transition: all 0.3s ease;

  .yida-home-container-inner {
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
    .main-container {
      max-width: 1200px;
      margin: 0 auto;
      &.hidden {
        display: none;
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    margin: 0 auto;
  }

  &.sidebar-visible {
    padding-left: 250px;
    .sidebar-toggle {
      left: 210px;
    }
    .sidebar {
      transform: translateX(0);
    }
  }

  .error-message {
    padding: 16px;
    color: #f5222d;
    text-align: center;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 32px;
      margin-bottom: 12px;
      color: #d9d9d9;
      .next-icon:before {
        font-size: 32px !important;
      }
    }

    .empty-text {
      font-size: 14px;
      color: #999;
    }
  }

  &::before {
    content: '';
    position: absolute;
    z-index: 1;
    top: 100px;
    left: 50%;
    transform: translateX(270px);
    width: 300px;
    height: 300px;
    pointer-events: none;
    border-radius: 300px;
    overflow: hidden;
    opacity: 0.6;
    pointer-events: none;
    background: conic-gradient(
      from 180deg at 50% 50%,
      #fac7c7 -21deg,
      #b3acff 42deg,
      #a1bdff 147deg,
      #d6a3ff 192deg,
      #b3acff 237deg,
      #fac7c7 339deg,
      #b3acff 402deg
    );
    filter: blur(80px);
    animation: gradientRotate 10s linear infinite, breatheEffect 6s ease-in-out infinite,
      floatEffect 12s ease-in-out infinite;

    @keyframes gradientRotate {
      0% {
        background: conic-gradient(
          from 0deg at 50% 50%,
          #fac7c7 -21deg,
          #b3acff 42deg,
          #a1bdff 147deg,
          #d6a3ff 192deg,
          #b3acff 237deg,
          #fac7c7 339deg,
          #b3acff 402deg
        );
      }
      100% {
        background: conic-gradient(
          from 360deg at 50% 50%,
          #fac7c7 -21deg,
          #b3acff 42deg,
          #a1bdff 147deg,
          #d6a3ff 192deg,
          #b3acff 237deg,
          #fac7c7 339deg,
          #b3acff 402deg
        );
      }
    }

    @keyframes breatheEffect {
      0%,
      100% {
        opacity: 0.6;
        transform: scale(1.5);
        filter: blur(80px);
      }
      50% {
        transform: scale(1);
        opacity: 0.8;
        filter: blur(60px);
      }
    }

    @keyframes floatEffect {
      0%,
      100% {
        transform: translateX(270px) translateY(0);
      }
      25% {
        transform: translateX(290px) translateY(-20px);
      }
      50% {
        transform: translateX(270px) translateY(-40px);
      }
      75% {
        transform: translateX(250px) translateY(-20px);
      }
    }
  }

  &::after {
    content: '';
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: url('https://img.alicdn.com/imgextra/i2/O1CN01lVIP0Y1dU3Xd1FS4F_!!6000000003738-2-tps-110-110.png');
    background-repeat: repeat;
    background-position: center center;
  }

  .header {
    text-align: center;
    padding: 12vh 0 7vh;
    position: relative;
  }

  .title-img {
    width: 682px;
    height: 99px;
  }

  .subtitle {
    font-size: 14px;
    color: #666;
    opacity: 0.8;
  }

  .search-section {
    margin: 0 auto 32px;
    width: 736px;
    position: relative;
    .chatbox-container {
      position: relative;
      border-radius: 12px;
      // border: 1px solid rgba(131, 137, 143, 0.24);
      transition: all 0.3s ease;
      z-index: 2;
      &:hover {
        box-shadow: 0 0 20px rgba(9, 24, 70, 0.2);
      }
    }
    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background: conic-gradient(
        from 180deg at 50% 50%,
        rgba(250, 199, 199, 0.6) -21deg,
        rgba(179, 172, 255, 0.6) 42deg,
        rgba(161, 189, 255, 0.6) 147deg,
        rgba(214, 163, 255, 0.6) 192deg,
        rgba(179, 172, 255, 0.6) 237deg,
        rgba(250, 199, 199, 0.6) 339deg,
        rgba(179, 172, 255, 0.6) 402deg
      );
      filter: blur(10px);
    }
  }

  .search-input {
    width: 100%;
    border: none;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    textarea {
      font-size: 14px;
      line-height: 22px;
      &::placeholder {
        color: rgba(24, 28, 31, 0.4);
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 6px;
    justify-content: flex-start;
    margin-top: 8px;
  }

  .action-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding-right: 6px;
    border: none;
    background: transparent;
    cursor: pointer;
    color: #666;
    font-size: 13px;
    transition: all 0.3s;
    height: 22px;
    line-height: 20px;
    &:hover {
      color: #333;
      background: rgba(224, 228, 255, 0.7);
      border-radius: 6px;
    }
    img {
      width: 16px;
      height: 16px;
      opacity: 0.7;
    }
  }

  .send-btn {
    position: absolute;
    right: 16px;
    bottom: 10px;
    img {
      width: 20px;
      height: 20px;
      filter: grayscale(100%);
      opacity: 0.4;
      transition: all 0.5s;
    }
    &.actived img {
      opacity: 1;
      filter: none;
    }
  }

  .add-attachment-btn {
    position: absolute;
    right: 52px;
    bottom: 10px;
    color: #999;
    transition: all 0.3s;
    &:hover {
      color: #004cff;
    }
  }

  .quick-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 10vh;
    justify-content: center;
    flex-wrap: wrap;
    padding: 0 20px;
  }

  .quick-action {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    cursor: pointer;
    color: #666;
    font-size: 13px;
    transition: all 0.3s;
    border: 1px solid rgba(224, 228, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.02);
    white-space: nowrap;

    &:hover {
      background: #fff;
      border-color: #e0e4ff;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
      transform: translateY(-1px);
    }
  }

  .quick-action-icon {
    margin-right: 6px;
    font-size: 14px;
  }

  .button-icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
  }

  .yida-home-tabs {
    .next-tabs-bar {
      margin-bottom: 32px;
      border-bottom: none;
      .next-tabs-tab-inner {
        padding: 10px 16px;
      }
      .tab-title {
        font-size: 13px;
      }
      .next-tabs-nav {
        display: flex;
        gap: 16px;
        justify-content: center;
        .next-tabs-tab {
          border-radius: 8px;
          transition: all 0.5s linear;
          &::before {
            display: none;
          }
          &.active {
            color: #fff;
            background: linear-gradient(119deg, #c676ff 0%, #654cff 41%, #405eff 75%, #007fff 99%);
          }
        }
      }
    }
  }

  .app-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
    margin-top: 20px;
    position: relative;
    padding: 0 20px;
  }
}

/* 添加渐入动画类 */
.fade-in {
  display: '';
  animation: fadeIn 0.5s ease-in-out;
}

/* 为不同的卡片添加延迟动画效果 */
@for $i from 1 through 30 {
  .enter-animation:nth-child(#{$i}) {
    animation: fadeInUp 0.5s ease-in-out;
    animation-delay: #{$i * 0.05}s;
    animation-fill-mode: both;
  }
}

/* 定义渐入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 定义从下往上渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
