// 临时 css
.next-shell-header {
  display: none !important;
}

#App .vc-page-yida-container-origin .vc-rootcontent {
  margin-top: 0 !important;
}

#root > div > ul,
#root > div h3,
#root > div .preview > .markdown {
  display: none !important;
}
#root > div > div {
  margin: 0 !important;
}

.vc-deep-container-entry {
  padding: 0 !important;
  margin: 0 !important;
}

.vc-page-content-1180 .vc-rootcontent {
  max-width: 100% !important;
}

*:has(> #usage) {
  padding: 0 !important;
}
