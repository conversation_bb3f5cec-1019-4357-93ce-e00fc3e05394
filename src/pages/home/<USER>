import ReactDOM from 'react-dom/client';
import React, { useState } from 'react';
import ChatBoxInput from '@/components/ChatBoxInput';
import SideBar from '@/components/SideBar';
import HomeAppTabs from '@/components/HomeAppTabs';
import { ChatBot } from '../chatbot';
import './index.scss';
import './temp.scss';

// 使用从 types/app.ts 导入的类型定义

const quickActionData = [
  {
    icon: '📅',
    title: '生成卡路里识别应用',
  },
  {
    icon: '☕',
    title: '生成下午茶订餐应用',
  },
  {
    icon: '🎯',
    title: '创建目标管理应用',
  },
];

const SIDEBAR_STATE_KEY = 'yida_manus_sidebar_state';

const HomePage: React.FC = () => {
  const [visibleChatBot, setVisibleChatBot] = useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('scb') === '1';
  });
  const [sidebarVisible, setSidebarVisible] = useState(() => {
    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);
    return savedState ? JSON.parse(savedState) : false;
  });
  const handleSend = () => {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.append('scb', '1');
    const url = window.location.origin + window.location.pathname + '?' + urlParams.toString();
    window.history.pushState({}, '', url);
    setVisibleChatBot(true);
  };
  const toggleSidebar = () => {
    const newState = !sidebarVisible;
    setSidebarVisible(newState);
    localStorage.setItem(SIDEBAR_STATE_KEY, JSON.stringify(newState));
  };
  return (
    <>
      <div className={`yida-home-container ${sidebarVisible ? 'sidebar-visible' : ''}`}>
        <SideBar visible={sidebarVisible} visibleChatBot={visibleChatBot} toggleSidebar={toggleSidebar} />
        <div className="yida-home-container-inner">
          <div className={`main-container ${visibleChatBot ? 'hidden' : ''}`}>
            <div className="header enter-animation">
              <img
                className="title-img"
                src="https://img.alicdn.com/imgextra/i2/O1CN01214w0F1SHj6dypKIU_!!6000000002222-2-tps-2729-396.png"
                alt="让你的想法快速变成应用"
              />
            </div>
            <div className="search-section enter-animation">
              <ChatBoxInput onSend={handleSend} />
            </div>

            <div className="quick-actions enter-animation">
              {quickActionData.map((item) => (
                <div className="quick-action">
                  <span className="quick-action-icon">{item.icon}</span>
                  <span className="title">{item.title}</span>
                </div>
              ))}
            </div>
            <HomeAppTabs />
          </div>
          <ChatBot setVisibleChatBot={setVisibleChatBot} toggleSidebar={toggleSidebar} visible={visibleChatBot} />
        </div>
      </div>
    </>
  );
};

ReactDOM.createRoot(document.querySelector('.yida-manus-container')).render(<HomePage />);
