/* eslint-disable @iceworks/best-practices/recommend-polyfill */
/* eslint-disable @typescript-eslint/indent */
import { getCsrfToken } from '@ali/yc-utils';

interface RecordAgentActionParams {
  threadId: string;
  parentTaskId: string;
  runId: string;
  thought: string;
  actionName:
    | 'write_and_execute_bash'
    | 'str_file_editor'
    | 'browser_use'
    | 'code_file_editor'
    | 'web_search'
    | 'html'
    | 'page'
    | 'click'
    | 'open_url';
  actionNameCn?: string;
  params: CodeParams | HtmlParams;
}

interface RecordAgentActionResponse {
  success: boolean;
  result?: string;
  errorCode?: string;
  errorMsg?: string;
}

interface CodeParams {
  desc?: string;
  language?: string; // 'python'|'js'
  code?: string;
  // markdown的三种模式，仅预览｜两栏｜仅编辑
  mode?: 'preview' | 'split' | 'edit';
}

interface HtmlParams {
  desc?: string;
  url?: string;
  title?: string;
  favicon?: string;
}

/**
 * 记录代理动作
 * @param params 请求参数
 * @returns Promise<RecordAgentActionResponse>
 */
export async function recordAgentAction(params: RecordAgentActionParams): Promise<RecordAgentActionResponse> {
  const response = await fetch('/query/open/actions/recordAgentAction.json', {
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
    body: JSON.stringify({
      _csrf_token: getCsrfToken(),
      ...params,
    }),
  });
  return await response.json();
}

// 更新canvas数据

interface UpdateCanvasDataParams {
  taskId: string;
  threadId: string;
  runId: string;
  canvasData: CodeParams | HtmlParams;
}

export async function updateCanvasData(params: UpdateCanvasDataParams) {
  const response = await fetch('/query/open/actions/updateCanvasData.json', {
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
    body: JSON.stringify({
      _csrf_token: getCsrfToken(),
      ...params,
    }),
  });
  return await response.json();
}

// 提交子任务的结果

interface SubmitAgentActionObservationParams {
  threadId: string;
  taskId: string;
  runId: string;
  canvasData: CodeParams | HtmlParams;
}

interface SubmitAgentActionObservationResponse {
  success: boolean;
  result?: string;
  errorCode?: string;
  errorMsg?: string;
}

export async function submitAgentActionObservation(
  params: SubmitAgentActionObservationParams,
): Promise<SubmitAgentActionObservationResponse> {
  const response = await fetch('/query/open/actions/submitAgentActionObservation.json', {
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
    body: JSON.stringify({
      _csrf_token: getCsrfToken(),
      ...params,
    }),
  });
  return await response.json();
}

// 最终任务结果提交
interface CardData {
  thought: string;
  finalize: boolean;
  deepThink: boolean;
}

interface SendObservationEventParams {
  actionTaskId: string;
  canvasData: CodeParams | HtmlParams;
  cardData: CardData;
}

interface SendObservationEventResponse {
  success: boolean;
  result?: string;
  errorCode?: string;
  errorMsg?: string;
}

export async function sendObservationEvent(params: SendObservationEventParams): Promise<SendObservationEventResponse> {
  const response = await fetch('/query/open/actions/sendObservationEvent.json', {
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
    body: JSON.stringify({
      _csrf_token: getCsrfToken(),
      ...params,
    }),
  });
  return await response.json();
}
