import { wait } from '@ali/yc-utils';
import { recordAgentAction, updateCanvasData, submitAgentActionObservation } from './apis';
import { UserFeedbackLogger } from '@/utils/UserFeedbackLogger';
import { UpdatePrinterEffect, FeedbackType } from '@/utils/UpdatePrinterEffect';

function formatMessage(message: string) {
  const codeArr = [] as any;
  return message
    .replace(/(<b>|<\/b>)/g, '**')
    .replace(/<CODE>([\s\S]*?)<\/CODE>/gm, (a, b) => {
      codeArr.push(b);
      return `$${codeArr.length - 1}$`;
    })
    .replace(/<[^>]+>/g, '')
    .replace(/\n/g, '<br/>')
    .replace(/\$(\d+)\$/g, (a, b) => {
      const tarJsx = codeArr.shift();
      if (/^```jsx/.test(tarJsx)) {
        return `${tarJsx}\n\`\`\``;
      }
      return tarJsx;
    });
}

export class UserFeedbackLoggerForMobile extends UserFeedbackLogger {
  private threadId: string;
  private parentTaskId: string;
  private mainTaskId: string;
  private taskId: string;
  private runId: string;
  private stepNumber: number;
  private stepTitle: string;
  constructor(config: any = {}) {
    super(config);
    this.threadId = config.threadId;
    this.parentTaskId = config.parentTaskId;
    this.runId = config.runId;
    // 仅输出Step日志
    const maxCharsPerBatch = config.maxCharsPerBatch || 10;
    this.mainPrinterEffect = new UpdatePrinterEffect({
      logObj: {
        logs: this.logs,
      },
      maxCharsPerBatch,
      outputFun: (text: string) => {
        if (this.stopped) return;
        this.updateMainToMobile(text);
      },
    });
    this.stepPrinterEffect = new UpdatePrinterEffect({
      logObj: this.stepLogObj,
      maxCharsPerBatch,
      outputFun: (text: string) => {
        if (this.stopped) return;
        this.updateMessageToMobile(text);
      },
    });
  }
  async startMainTask() {
    // 创建默认思考规划主任务
    const res = await recordAgentAction({
      threadId: this.threadId,
      parentTaskId: this.parentTaskId,
      runId: this.runId,
      thought: '思考规划',
      actionName: 'code_file_editor',
      actionNameCn: '规划',
      params: {
        desc: '思考规划',
        language: 'md',
        code: '',
        mode: 'preview',
      },
    });
    if (res?.success) {
      this.mainTaskId = res.result;
      console.log('思考规划开始运行');
    } else {
      console.error('思考规划开始运行创建主任务失败');
    }
  }
  async endMainTask() {
    const tarMessage = this.mainPrinterEffect.formatLogsForDisplay();
    const res = await submitAgentActionObservation({
      taskId: this.mainTaskId,
      threadId: this.threadId,
      runId: this.runId,
      canvasData: {
        desc: '主任务运行结束',
        language: 'md',
        code: formatMessage(tarMessage),
        mode: 'preview',
      },
    });
    if (!res?.success) {
      console.error('主任务完成标记失败');
    }
  }
  async stepStart(stepTitle: string, stepNumber: number): Promise<void> {
    if (this.stopped) return;
    // 标记仅输出当前步骤日志
    this.stepPrinterEffect.reset();
    this.stepLogObj.stepNumber = stepNumber;
    // 记录代理动作
    const res = await recordAgentAction({
      threadId: this.threadId,
      parentTaskId: this.parentTaskId,
      runId: this.runId,
      thought: `步骤${stepNumber + 1}: ${stepTitle}`,
      actionName: 'code_file_editor',
      actionNameCn: `步骤${stepNumber + 1}`,
      params: {
        desc: stepTitle,
        language: 'md',
        code: '',
        mode: 'preview',
      },
    });
    if (res?.success) {
      this.taskId = res.result;
      this.stepNumber = stepNumber;
      this.stepTitle = stepTitle;
      console.log(`步骤 ${stepNumber + 1} 开始运行`);
      // 开始输出
      const tarMessage = `🚩 开始运行步骤 ${stepNumber + 1}: ${stepTitle}\n`;
      this.stepLog(FeedbackType.STEP_START, tarMessage, stepNumber);
    } else {
      console.error(`步骤 ${stepNumber + 1} 开始运行创建子任务失败`);
    }
  }
  async updateMainToMobile(message: string): Promise<void> {
    if (this.stopped) return;
    const res = await updateCanvasData({
      taskId: this.mainTaskId,
      threadId: this.threadId,
      runId: this.runId,
      canvasData: {
        desc: '思考规划: 运行中',
        language: 'md',
        code: formatMessage(message),
        mode: 'preview',
      },
    });
    if (!res?.success) {
      console.error(`步骤 ${this.stepNumber + 1} 运行更新失败`);
    }
  }
  async updateMessageToMobile(message: string): Promise<void> {
    if (this.stopped) return;
    const res = await updateCanvasData({
      taskId: this.taskId,
      threadId: this.threadId,
      runId: this.runId,
      canvasData: {
        desc: `步骤${this.stepNumber + 1}: ${this.stepTitle} 运行中`,
        language: 'md',
        code: formatMessage(message),
        mode: 'preview',
      },
    });
    if (!res?.success) {
      console.error(`步骤 ${this.stepNumber + 1} 运行更新失败`);
    }
  }
  async stepEnd(stepNumber: number): Promise<void> {
    if (this.stopped) return;
    const tarMessage = this.stepPrinterEffect.formatLogsForDisplay();
    // 清空日志
    await wait(500);
    const res = await submitAgentActionObservation({
      taskId: this.taskId,
      threadId: this.threadId,
      runId: this.runId,
      canvasData: {
        desc: `步骤 ${stepNumber + 1}: ${this.stepTitle} 运行成功`,
        language: 'md',
        code: formatMessage(tarMessage),
        mode: 'preview',
      },
    });
    if (!res?.success) {
      console.error(`步骤 ${this.stepNumber + 1} 完成标记失败`);
    }
  }
  async completeStart(): Promise<void> {
    if (this.stopped) return;
    if (this.keyResultData.length) {
      this.stepNumber++;
      const tarData = this.keyResultData[0];
      const htmlParams = {
        desc: tarData.description,
        url: tarData.url,
        title: tarData.title,
        favicon: tarData.icon,
      };
      const res = await recordAgentAction({
        threadId: this.threadId,
        parentTaskId: this.parentTaskId,
        runId: this.runId,
        thought: `步骤${this.stepNumber + 1}：最终应用产物`,
        actionName: 'html',
        actionNameCn: '应用搭建',
        params: htmlParams,
      });
      if (res.success) {
        const res2 = await submitAgentActionObservation({
          taskId: res.result,
          threadId: this.threadId,
          runId: this.runId,
          canvasData: htmlParams,
        });
        if (!res2?.success) {
          console.error('结束生成应用产物子任务失败');
        }
      } else {
        console.error('创建生成应用产物子任务失败');
      }
    }
  }
  coding(message: string, stepNumber?: number, data?: any): void {
    if (!message) return;
    let content = '';
    if (stepNumber === undefined) {
      content = `<CODE>${message || ''}</CODE>\n`;
      this.log(FeedbackType.CODING, content);
    } else {
      content = `\n┣━ <b>${this.getLogTypeIcon(FeedbackType.CODING)} 代码生成:</b>\n\n<CODE>${
        message || ''
      }</CODE>\n\n`;
      this.stepLog(FeedbackType.CODING, content, stepNumber);
    }
  }
  // 不做响应的消息类型
  // complete(message: string): void {}
  // response(message: string): void {}
  // plan(planData: any): void {}
  // stepStatus(): void {}
  // planEnd(message: string, executionData?: any): void {}
}
