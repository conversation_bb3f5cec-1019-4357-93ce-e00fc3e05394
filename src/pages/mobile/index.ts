/* eslint-disable @iceworks/best-practices/recommend-polyfill */
import { initAICanvasWebC } from '@ali/yida-webc';
import { AppQuickChatBotConfig } from '@/agents';
import { Logger } from '@/utils/Logger';
import { UserFeedbackLoggerForMobile } from './feedback';
import { AgentConfig, FactoryAgent } from '@/core/FactoryAgent';
import { BaseAgent } from '@/core/BaseAgent';
import { sendObservationEvent } from './apis';

interface ChatManagerConfig {
  threadId: string;
  parentTaskId: string;
  runId: string;
}
class ChatManager {
  private agentConfig: AgentConfig;

  private logger: Logger;
  private feedbackLogger: UserFeedbackLoggerForMobile;
  private currentAgent: BaseAgent | null = null;

  constructor(agentConfig: AgentConfig) {
    this.agentConfig = agentConfig;
    this.logger = new Logger();
  }

  // 停止执行
  stopExecution() {
    this.currentAgent?.stopExecution();
    this.feedbackLogger.stop();
  }

  // 处理发送消息
  async handleSendMessage(content: string, manusConfig: ChatManagerConfig) {
    this.feedbackLogger = new UserFeedbackLoggerForMobile({
      maxCharsPerBatch: 10,
      threadId: manusConfig.threadId,
      parentTaskId: manusConfig.parentTaskId,
      runId: manusConfig.runId,
    });
    this.currentAgent = FactoryAgent.createAgent({
      ...this.agentConfig,
      memory: this.currentAgent?.memory,
      logger: this.logger,
      feedbackLogger: this.feedbackLogger,
    });
    let result;
    try {
      await this.feedbackLogger.startMainTask();
      result = await this.currentAgent.run(content);
      this.logger.info(`完成用户请求，答复内容: ${result}`);
      await this.feedbackLogger.endMainTask();
    } catch (error) {
      this.logger.error(`处理消息错误: ${error}`);
    }
    // 结束任务
    const res = await sendObservationEvent({
      actionTaskId: manusConfig.parentTaskId,
      canvasData: {
        desc: '已完成任务',
        language: 'html',
        code: result || '已完成任务',
        mode: 'preview',
      },
      cardData: {
        thought: '已经完成任务',
        finalize: true,
        deepThink: false,
      },
    });
    if (!res?.success) {
      console.error('运行完成的提交接口失败');
    }
  }
  // 销毁
  destroy() {
    this.agentConfig = null;
    this.feedbackLogger = null;
  }
}

window.__InitChatBot = () => {
  initAICanvasWebC();
  return new ChatManager(AppQuickChatBotConfig);
  // return new ChatManager(ManusChatBotConfig);
};
