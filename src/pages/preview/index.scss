body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background-color);

  .vc-rootcontent {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
}

.webc-container {
  display: flex;
  height: calc(100vh - 100px);
  position: relative;

  &.resizing {
    cursor: col-resize;
    user-select: none;

    .left-area,
    .right-area {
      pointer-events: none;
    }
  }

  .left-area,
  .right-area {
    height: 100%;
    transition: transform 0.1s ease;
  }

  .right-area {
    margin-left: 20px;
  }

  /* 拖动分隔条样式 */
  .resizer {
    width: var(--resizer-width);
    background-color: transparent;
    position: relative;
    left: 0 !important;
    height: 95vh;
    margin: auto;
    cursor: col-resize;
    z-index: 10;
    touch-action: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '';
      position: absolute;
      left: 13px;
      width: 3px;
      height: 100%;
      background: linear-gradient(
        180deg,
        transparent 0%,
        rgba(0, 0, 0, 0.05) 10%,
        rgba(0, 0, 0, 0.05) 90%,
        transparent 100%
      );
    }

    &::after {
      content: '';
      position: absolute;
      width: 15px;
      height: 30px;
      background-color: #fff;
      border-radius: 3px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      top: 50%;
      transform: translateX(12px);
      opacity: 1;
      transition: all 0.2s ease;
      background-image: url("data:image/svg+xml,%3Csvg width='4' height='24' viewBox='0 0 4 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='2' cy='8' r='1' fill='%23999'/%3E%3Ccircle cx='2' cy='12' r='1' fill='%23999'/%3E%3Ccircle cx='2' cy='16' r='1' fill='%23999'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: 3px 16px;
    }

    &:hover,
    &.resizing {
      &::before {
        transform: all 0.2s ease;
        background: linear-gradient(
          180deg,
          transparent 0%,
          var(--resizer-hover-color) 10%,
          var(--resizer-hover-color) 90%,
          transparent 100%
        );
      }

      &::after {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        background-image: url("data:image/svg+xml,%3Csvg width='4' height='24' viewBox='0 0 4 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='2' cy='8' r='1' fill='%234e6ef2'/%3E%3Ccircle cx='2' cy='12' r='1' fill='%234e6ef2'/%3E%3Ccircle cx='2' cy='16' r='1' fill='%234e6ef2'/%3E%3C/svg%3E");
      }
    }
  }
}
