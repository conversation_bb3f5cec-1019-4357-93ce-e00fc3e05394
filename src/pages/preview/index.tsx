import ReactDOM from 'react-dom';
import React, { useRef, useState } from 'react';
import { Card, Button } from '@alifd/next';
import { WebCTerminal, bundleAICanvasCode } from '@ali/yida-webc';

import { getLocal, setLocal } from '@ali/yc-utils';

import './index.scss';

const { YidaAICanvas, YidaCodeField } = window.LeGao.__ctx__.App.components;

const Preview = () => {
  const [isResizing, setIsResizing] = useState(false);
  const [sourceCode, setSourceCode] = useState('console.log("hello world")');
  const [importedModules, setImportedModules] = useState<string[]>([
    'react',
    'antd',
    'recharts',
    'lucide-react',
    'yida-plugin-markdown',
    'framer-motion',
    '@radix-ui/themes',
    'ahooks',
    'd3',
    'dayjs',
    '@ant-design/icons',
  ]);
  const [loading, setLoading] = useState(true);
  const [runtimeCode, setRuntimeCode] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const resizerRef = useRef<HTMLDivElement>(null);
  const rafRef = useRef<number>();
  const lastWidthRef = useRef(50);
  // const webc = useRef<WebC | null>(null);

  const [leftWidth, setLeftWidth] = useState(() => {
    const savedWidth = getLocal('previewLeftWidth');
    return savedWidth ? parseFloat(savedWidth) : 50;
  });

  const handleOnReady = () => {
    setLoading(false);
  };

  // 处理拖动开始
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    document.body.style.cursor = 'col-resize';
    containerRef.current?.classList.add('resizing');
    e.preventDefault();
  };

  // 处理拖动
  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;

      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }

      rafRef.current = requestAnimationFrame(() => {
        const containerRect = containerRef.current!.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const mouseX = e.clientX - containerRect.left;

        // 计算新宽度（限制在20%到80%之间）
        const newWidth = Math.min(Math.max((mouseX / containerWidth) * 100, 20), 80);

        // 只有当宽度变化超过0.5%时才更新
        if (Math.abs(newWidth - lastWidthRef.current) > 0.5) {
          setLeftWidth(newWidth);
          setLocal('previewLeftWidth', newWidth);
          lastWidthRef.current = newWidth;
        }
      });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      containerRef.current?.classList.remove('resizing');
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('mouseleave', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseleave', handleMouseUp);
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [isResizing]);

  const handlePreview = async () => {
    const { bundleCode, dependencies, sourceCode: newSourceCode, output } = await bundleAICanvasCode(sourceCode, true);
    console.log('++++output++++', output);
    setSourceCode(newSourceCode);
    setRuntimeCode(bundleCode);
    setImportedModules(dependencies);
  };

  return (
    <div className="webc-container" ref={containerRef}>
      <Card
        className="left-area"
        title="代码"
        extra={
          <Button type="primary" onClick={handlePreview}>
            预览
          </Button>
        }
        style={{
          width: `${leftWidth}%`,
          transform: isResizing ? 'none' : undefined,
        }}
        contentHeight="100%"
      >
        {loading && <div style={{ textAlign: 'center', lineHeight: '300px', height: 600 }}>环境初始化化中...</div>}
        {!loading && <YidaCodeField language="javascript" value={sourceCode} onChange={setSourceCode} height={600} />}
        <WebCTerminal style={{ width: '100%', height: 400, marginTop: 20 }} onReady={handleOnReady} />
      </Card>

      <div
        className="resizer"
        ref={resizerRef}
        onMouseDown={handleMouseDown}
        style={{
          left: `${leftWidth}%`,
          transform: 'translateX(-50%)',
        }}
      />

      <Card
        className="right-area"
        title="预览"
        style={{
          width: `${100 - leftWidth - 2}%`,
          transform: isResizing ? 'none' : undefined,
        }}
        contentHeight="100%"
      >
        <YidaAICanvas
          code={sourceCode}
          runtimeCode={runtimeCode}
          importedModules={JSON.stringify(importedModules)}
          isWebCCompiled
        />
      </Card>
    </div>
  );
};

ReactDOM.render(<Preview />, document.querySelector('.yida-manus-container'));
