import { LLMConfig } from '@/types/types';
import { LLMService } from './llmService';

const DEFAULT_MAX_TOKEN = 8000;

export class CodeService extends LLMService {
  constructor(config: Partial<LLMConfig>, modelType: string, apiKey?: string) {
    super(config);
    this.apiKey = apiKey;

    if (modelType === 'ding') {
      if (this.model === 'anthropic/claude-3.7-sonnet') {
        this.config.model = 'dingtalk-openmatrix-claude37-sonnet';
      } else {
        this.config.model = 'dingtalk-qwen3-235b';
      }
      if (this.config.model === 'anthropic/claude-3.7-sonnet') {
        this.config['max_tokens'] = 64000;
        this.config['maxTokens'] = 64000;
      } else {
        this.config['max_tokens'] = 32768;
        this.config['maxTokens'] = 32768;
      }
    } else if (modelType === 'deepseek') {
      this.config['max_tokens'] = DEFAULT_MAX_TOKEN;
    } else if (modelType === 'openrouter') {
      this.config['max_tokens'] = 64000;
      // speed first ranking
      this.config.provider = {
        sort: 'throughput',
      };
    }

    console.log('>>>>', this.apiKey, this.baseUrl, this.model);
  }
}
