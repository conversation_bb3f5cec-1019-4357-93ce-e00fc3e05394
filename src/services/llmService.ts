import axios from 'axios';
import { jsonrepair } from 'jsonrepair';

import { getConfig } from '@/config';
import { AVAILABLE_MODELS, useAISettingStore } from '@/store/aiSettingStore';
import { LLMConfig, LLMResponse, Message, streamType } from '@/types/types';
import { LLMError } from '@/utils/Errors';

const ENABLE_LLM_SERVICE_MSG_LOG = location.search.includes('__ENABLE_LLM_SERVICE_MSG_LOG=true');

export interface IPromptBuilderContext {
  enableRadixUI: boolean;
  enableAntd: boolean;
  scenarioContext: string;
  isDynamicDataSource: boolean;
  isModifyCode: boolean;
  appContext?: string;
  pageContext?: PageContext;
  enableYidaComponents: boolean;
  scenario?: {
    normal?: boolean;
    portal?: boolean;
    application?: boolean;
  };
}

interface PageContext {
  basicInfo: string;
  cubeInfo: string;
  dataSourceAPIInfo: string;
}

/**
 * 简单的重试函数，用于在API请求失败时进行重试
 * @param fn 要执行的异步函数
 * @param retries 最大重试次数
 * @param delay 重试间隔（毫秒）
 * @param backoff 重试间隔的指数增长因子
 */
async function retry<T>(fn: () => Promise<T>, retries = 6, delay = 1000, backoff = 2): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) {
      throw error;
    }

    // 对于速率限制错误，使用更长的延迟
    const isRateLimitError =
      axios.isAxiosError(error) &&
      (error.response?.status === 429 || error.response?.data?.error?.type === 'rate_limit_exceeded');

    const waitTime = isRateLimitError ? delay * 2 : delay;

    // 添加一些随机性，避免多个请求同时重试
    const jitter = Math.random() * 0.3 + 0.85; // 0.85-1.15
    const actualDelay = Math.floor(waitTime * jitter);

    // eslint-disable-next-line no-console
    console.log(`请求失败，${actualDelay}ms后重试，剩余重试次数: ${retries - 1}`);
    await new Promise((resolve) => setTimeout(resolve, actualDelay));

    return retry(fn, retries - 1, delay * backoff, backoff);
  }
}

export class LLMService {
  private abortController: AbortController | null = null;
  private provider: Record<string, any> | null = null;

  protected apiKey: string;
  protected baseUrl: string;
  protected model: keyof typeof AVAILABLE_MODELS;
  // LLM的配置
  protected config: LLMConfig;

  constructor(config?: Partial<LLMConfig>) {
    // 定义用哪个模型作为默认模型
    // this.model = config.model || 'qwen-max';
    const { mainModel } = useAISettingStore.getState();
    this.model = config?.model || mainModel;
    if (
      [
        'deepseek/deepseek-reasoner-v3-0324',
        'anthropic/claude-3.7-sonnet',
        'google/gemini-2.5-pro-exp-03-25:free',
      ].includes(this.model)
    ) {
      this.baseUrl = 'https://openrouter.ai/api/v1';
      this.apiKey = getConfig().openRouterApiKey;
    } else if (this.model === 'deepseek-chat' || this.model === 'deepseek-reasoner') {
      this.baseUrl = 'https://api.deepseek.com';
      this.apiKey = getConfig().deepseekV3ApiKey;
    } else {
      this.baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1';
      this.apiKey = getConfig().openaiApiKey;
    }

    this.config = {
      model: this.model,
      temperature: 0.7,
      maxTokens: AVAILABLE_MODELS[this.model]?.maxTokens || 8000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      ...config,
    };

    if (this.provider) {
      this.config.provider = this.provider;
    }

    if (!this.apiKey) {
      throw new LLMError('OpenAI API key not found in configuration');
    }
  }

  /**
   * 使用LLM来回答问题
   * @param messages 用户的聊天记录
   * @param systemMessages 系统的聊天记录
   * @param tools 可用的工具
   * @param toolChoice 工具选择策略
   * @param jsonFormat 是否将回答结果以JSON对象的形式返回
   * @returns 一个LLMResponse对象，包含了回答的内容和工具调用记录
   */
  async ask(
    messages: Message[],
    systemMessages?: Message[],
    tools?: Array<{ type: string; function: { name: string; description: string; parameters?: any } }>,
    toolChoice: 'none' | 'auto' | 'required' = 'auto',
    jsonFormat?: boolean,
  ): Promise<LLMResponse> {
    // 转换消息格式 - Manus特有的功能
    const chatMessages = [...(systemMessages || []), ...(messages || [])];
    const convertedChatMessages = this.convertMessagesToLLMFormat(chatMessages, {
      model: this.model,
    });
    return retry(async () => {
      try {
        // 使用axios发起请求而不是fetch
        const response = await axios.post(
          `${this.baseUrl}/chat/completions`,
          {
            ...this.config,
            messages: convertedChatMessages,
            tools: tools?.length ? tools : undefined,
            tool_choice: toolChoice,
            stream: false,
            response_format: {
              type: jsonFormat ? 'json_object' : 'text',
            },
          },
          {
            headers: {
              Authorization: `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
          },
        );

        console.log('API响应:', response);
        const result = response.data.choices[0];
        return {
          content: result.message.content,
          toolCalls: result.message.tool_calls,
        };
      } catch (error) {
        if (axios.isAxiosError(error)) {
          throw new LLMError(`API request failed: ${error.response?.data?.error?.message || error.message}`);
        }
        throw new LLMError(`Unexpected error: ${error}`);
      }
    });
  }

  static getUrl(): string {
    if (
      process.env.NODE_ENV === 'development' &&
      new URL(location.href).searchParams.get('ENABLE_LOCAL_SERVER') === 'true'
    ) {
      return `http://localhost:3000/${g_config.appKey}/doc2bot/action`;
    } else {
      return `/${window.g_config.appKey}/query/doc2bot/streamingTxtFromAI?method=action&_csrf_token=${
        window.g_config._csrf_token
      }&_locale_time_zone_offset=${Date.now()}`;
      // return `https://dd-partner-sse.pre-fc.alibaba-inc.com//${g_config.appKey}/doc2bot/action`;
    }
    return `/${window.g_config.appKey}/query/doc2bot/streamingTxtFromAI?method=action&_csrf_token=${
      window.g_config._csrf_token
    }&_locale_time_zone_offset=${Date.now()}`;
  }

  // 浏览器环境中的流式响应实现
  async askStream<Context = any>(
    messages: Message[],
    systemMessages?: Message[],
    tools?: Array<{ type: string; function: { name: string; description: string; parameters?: any } }>,
    toolChoice: 'none' | 'auto' | 'required' = 'auto',
    onStream?: (type: streamType, data: any, abort?: () => void) => void,
    jsonFormat?: boolean,
    options?: {
      context?: Context;
      enableThinking?: boolean;
    },
  ): Promise<LLMResponse> {
    try {
      this.abortController = new AbortController();
      const chatMessages = [...(systemMessages || []), ...(messages || [])];
      const convertedChatMessages = this.convertMessagesToLLMFormat(chatMessages, {
        model: this.model,
      });
      // 使用 fetch API 代替 axios 来处理流式响应
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      // const response = await fetch(`${this.baseUrl}/chat/completions`, {
      // const response = await fetch(`http://localhost:3000/${g_config.appKey}/doc2bot/action`, {
      const response = await fetch(LLMService.getUrl(), {
        method: 'POST',
        headers: {
          Accept: 'text/event-stream',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: getConfig(),
          cfg: this.config,
          messages: convertedChatMessages,
          tools: tools?.length ? tools : undefined,
          tool_choice: toolChoice,
          stream: true,
          response_format: {
            type: jsonFormat ? 'json_object' : 'text',
          },
          // speed first ranking when enable-openrouter for local dev
          provider: {
            sort: 'throughput',
          },
          enableThinking: options?.enableThinking || false,
          enable_thinking: options?.enableThinking || false,
          /**
           * LLM 系统提示词的上下文变量、控制开关
           */
          context: (options?.context || {}) as IPromptBuilderContext,
        }),
        signal: this.abortController.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error ${response.status}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      const reader = response.body.getReader();
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      const decoder = new TextDecoder('utf-8');

      let reasoningContent = '';
      let thinkingContent = '';
      const toolInfo: any[] = [];

      // 处理流式响应
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter((line) => line.trim() !== '');
        let traceId = '';
        for (const line of lines) {
          if (line.startsWith('traceId: ')) {
            traceId = line.substring(9);
          }
          if (line.startsWith('data: ')) {
            const data = line.substring(6);
            if (data === '[DONE]') continue;
            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices[0]?.delta || {};
              if (!delta) continue;
              // 添加调试日志
              if (delta.reasoning_content) {
                reasoningContent += delta.reasoning_content;
                if (onStream) {
                  onStream(streamType.REASONING, reasoningContent, () => {
                    this.interrupt();
                  });
                }
              } else {
                // 处理回复内容
                if (delta.content) {
                  thinkingContent += delta.content;
                  // 如果设置了回调函数，则调用
                  if (onStream) {
                    onStream(streamType.THINKING, thinkingContent, () => {
                      this.interrupt();
                    });
                  }
                }
                // 处理工具调用
                if (delta.tool_calls && delta.tool_calls.length > 0) {
                  for (const toolCall of delta.tool_calls) {
                    const { index } = toolCall;
                    // 确保数组长度足够
                    while (toolInfo.length <= index) {
                      toolInfo.push({});
                    }
                    // 更新工具ID
                    if (toolCall.id) {
                      toolInfo[index].id = (toolInfo[index].id || '') + toolCall.id;
                    }
                    // 更新函数名称
                    if (toolCall.function?.name) {
                      toolInfo[index].name = (toolInfo[index].name || '') + toolCall.function.name;
                    }
                    // 更新参数 - 确保累加参数
                    if (toolCall.function?.arguments) {
                      const currentArguments = (toolInfo[index].arguments || '') + toolCall.function.arguments;
                      toolInfo[index].arguments = currentArguments;
                      const repairedJson = jsonrepair(currentArguments)
                        .replace(/\n/g, '')
                        .replace(/(,(\s)*)?"":null/g, '');
                      if (onStream) {
                        onStream(streamType.TOOL_CALL, {
                          tool: toolInfo[index].name,
                          args: repairedJson,
                        });
                      }
                    }
                  }
                }
              }
            } catch (e) {
              // eslint-disable-next-line no-console
              console.error('Error parsing stream data:', e);
            }
          }
        }
      }
      const newToolCalls = toolInfo.map((tool) => ({
        id: tool.id,
        type: 'function',
        function: {
          name: tool.name,
          arguments: tool.arguments,
        },
      }));
      const result = {
        content: thinkingContent,
        thinking: reasoningContent,
        toolCalls: newToolCalls.length > 0 ? newToolCalls : undefined,
      };
      // 添加调试日志
      console.log('[LLMService] 最终返回结果: ', {
        contentLength: result.content?.length || 0,
        thinkingLength: result.thinking?.length || 0,
        hasToolCalls: !!result.toolCalls,
        toolCallsCount: result.toolCalls?.length || 0,
      });

      // console.log(' Call function: ', traceId, tools, toolInfo);
      return result;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new LLMError('请求被中断');
      }
      if (axios.isAxiosError(error)) {
        throw new LLMError(`API stream request failed: ${error.response?.data?.error?.message || error.message}`);
      }
      throw new LLMError(`Unexpected stream error: ${error}`);
    } finally {
      this.abortController = null;
    }
  }

  // 更新配置
  updateConfig(config: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // 获取当前配置
  getConfig(): LLMConfig {
    return { ...this.config };
  }

  interrupt(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * 将代理内部消息格式转换为LLM所需的格式
   * @param messages 代理内部消息格式
   * @returns 转换后的LLM所需消息格式
   */
  private convertMessagesToLLMFormat(
    messages: Message[],
    options?: {
      model?: string;
      filterUserMsg?: (msg: Message) => boolean;
    },
  ): any[] {
    const transformedMsgList = messages
      .map((msg) => {
        if (msg.role === 'system') {
          return msg;
        }
        if (msg.role === 'user') {
          return msg;
        }
        if (msg.role === 'assistant') {
          const result: any = {
            role: 'assistant',
            content: msg.content,
          };
          if (msg.toolCalls && msg.toolCalls.length > 0) {
            result.thought = msg.content;
            const firstToolCall = msg.toolCalls[0];
            result.tool_calls = [
              {
                type: 'function',
                function: {
                  name: firstToolCall.function.name,
                  arguments: firstToolCall.function.arguments,
                },
                id: firstToolCall.id,
              },
            ];
          }
          return result;
        }

        if (msg.role === 'tool') {
          const result: any = {
            role: 'tool',
            name: msg.name,
            content: msg.content,
          };
          result.tool_call_id = msg.toolCallId;
          return result;
        }
        return msg;
      })
      .filter(Boolean);

    if (ENABLE_LLM_SERVICE_MSG_LOG) {
      console.info('💬 [LLMService] messages: ', transformedMsgList);
    }

    return transformedMsgList;
  }
}
