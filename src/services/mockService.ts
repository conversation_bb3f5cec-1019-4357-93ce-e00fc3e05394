/**
 * @notice debug only for efficiency
 */
export class MockService {
  public isEnabled = location.search.includes('enableLLMServiceMOCK=true');

  mock = async <T>(originalFn: () => Promise<T>, mockFn?: () => Promise<T>): Promise<T> => {
    if (this.isEnabled && mockFn) {
      return mockFn();
    }
    return originalFn();
  };

  /**
   * 模拟生成代码 ToolCall
   */
  mockGenerateCodePage = async (options?: { hasCompileError?: boolean; hasRenderError?: boolean }) => {
    return Promise.resolve(`export const YidaComp = () => {
          ${options?.hasRenderError ? 'throw new Error("rendering error")' : ''}
  return (
    ${options?.hasCompileError ? 'undefined + xxxxx import this shit from win' : ''}
    <div>Hello, Test</div>
  );
      };`);
  };

  mockPageGenPlan = async () => {
    return Promise.resolve(`
Yida 产品首页说明：

YidaHomePage

静态展示宜搭低代码平台的核心价值主张与产品能力，包含以下模块：
1. 头部 Banner - 展示产品 slogan 和核心价值
2. 功能亮点 - 3 - 4 个核心能力卡片展示
3. 产品优势 - 图文混排的优势说明
4. 操作引导区 - 创建表单 / 查看文档等入口

页面组成（组件表）

* HeroSection
  * 功能简述：顶部横幅区域，展示产品主标题和核心价值描述
    * 组件属性：
  | 字段名称 | 类型 | 说明 |
  | ---------------| --------| -----------------------|
  | title | string | 主标题文字内容 |
  | subtitle | string | 副标题文字内容 |
  | backgroundUrl | string | 背景图片的 URL 地址 |

* KeyFeatures
  * 功能简述：核心功能卡片展示区，3 - 4 个等宽卡片
    * 组件属性：
  | 字段名称 | 类型 | 说明 |
  | ----------| -----------------------| --------------------|
  | features | Array<featureitem> | 功能项数组 |

  interface FeatureItem {
  iconUrl: string;
  title: string;
  description: string;
}

* AdvantageSection
  * 功能简述：图文混排的产品优势说明区（左图右文 / 左文右图交替）
* 组件属性：
  | 字段名称 | 类型 | 说明 |
  | -------------| -----------------------| ---------------------|
  | advantages | Array<advantageitem> | 优势说明项数组 |

  interface AdvantageItem {
  imageUrl: string;
  title: string;
  description: string;
  isImageLeft: boolean; // 控制图文排列方向
}

* CallToAction
  * 功能简述：操作引导按钮组（开始使用 + 查看文档）
* 组件属性：
  | 字段名称 | 类型 | 说明 |
  | ----------------| ----------| -------------------------|
  | primaryLabel | string | 主按钮文字 |
  | secondaryLabel | string | 次要按钮文字 |
  | onPrimaryClick | () =& gt; void | 主按钮点击事件回调 |
  | onSecondaryClick | () =& gt; void | 次要按钮点击事件回调 |

  其它要求
1. 所有文字内容通过 props 传入实现可配置化
2. 图片资源使用 CDN URL 传递
3. 响应式布局要求：移动端堆叠排列，PC 端横向排列

其它备忘

  * 页面层组合方式：
<pagelayout>
  <herosection {...heroprops}="">
  <keyfeatures features="{featureList}">
  <advantagesection advantages="{advantageList}">
  <calltoaction {...ctaprops}="">
</calltoaction></advantagesection></keyfeatures></herosection></pagelayout>
  * 所有内容数据通过 page 层传入，保持组件纯净
    * 样式使用 CSS Modules 实现局部作用域

`);
  };

  /**
   * 模拟生成计划 Planning
   */
  mockGeneratePlan = async () => {
    return Promise.resolve(`
猫咪喂养统计 PRD 说明：

应用名称
卡诺猫粮喂养追踪系统

应用描述
用于记录和展示单只猫咪（卡诺）的每日猫粮投喂量，支持最近7天数据的可视化展示，不保留历史数据。

数据表单列表（表单列表）

* 喂食记录表
* 功能简述：记录每日猫粮投喂量核心数据
* 表单字段列表：

| 字段名称 | 类型        | 说明                         |
|----------|-------------|------------------------------|
| 喂食日期 | DateField   | 记录投喂日期（默认当天）     |
| 克数     | NumberField | 必填项，支持小数点精确到1克  |
| 备注说明 | TextareaField | 选填，特殊情况说明         |

应用关键页面列表（功能页面列表）

* 每日投喂表单页
* 功能简述：快速录入当日猫粮投喂量的低代码表单页面

* 周数据看板页
* 功能简述：展示最近7天投喂量变化的可视化看板，包含每日克数折线图、周总量统计和日均值计算
      `);
  };
  /**
   * 模拟生成闪电侠评选系统 PRD
   */
  mockGenerateLightingPlan = async () => {
    return Promise.resolve(`
"""markdown
钉钉闪电侠评选系统 PRD 说明：

应用名称
钉钉闪电侠评选系统

应用描述
用于钉钉内部闪电侠奖项的提名、展示及投票，支持员工提交提名信息，通过炫酷的卡片墙展示候选人并实现投票计数功能。

数据表单列表（表单列表）

1. 闪电侠提名表
2. 功能简述
记录候选人基本信息、成果材料及实时投票数
3. 表单字段列表

| 字段名称      | 类型          | 说明                                                                 |
|---------------|---------------|----------------------------------------------------------------------|
| 提名人        | EmployeeField | 自动关联钉钉账号（含姓名、工号、部门）                               |
| 花名          | TextField     | 候选人在钉钉内部使用的昵称                                           |
| 个人照片      | ImageField    | 正方形证件照（建议尺寸 500x500px）                                   |
| 成果描述      | TextareaField | 200字以内关键贡献说明（支持Markdown格式）                            |
| 成果展示图    | ImageField    | 成果证明材料/效果图（建议上传PNG/JPG格式）                           |
| 当前票数      | NumberField   | 默认值0，通过投票操作自动累加                                        |

应用关键页面列表:

1. 闪电侠理念首页（自定义页面）
功能简述：动态粒子背景+闪电流光动画，展示奖项定义、评选标准及"闪电"与"侠"的视觉化解读，提供「我要提名」和「查看榜单」入口

2. 提名提交页（表单页面）
功能简述：标准化信息录入界面，直接调用「闪电侠提名表」表单组件

3. 闪电侠榜单页（自定义页面）
功能简述：响应式卡片墙布局（每行3-4列），卡片包含候选人照片、花名悬浮动效、成果摘要（截取前50字）、成果图缩略图、实时票数及投票按钮
"""
`);
  };
}

export const mockService = new MockService();
