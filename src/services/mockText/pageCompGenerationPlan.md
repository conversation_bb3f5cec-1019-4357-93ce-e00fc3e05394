Yida 产品首页说明：

YidaHomePage

静态展示宜搭低代码平台的核心价值主张与产品能力，包含以下模块：
1. 头部 Banner - 展示产品 slogan 和核心价值
2. 功能亮点 - 3 - 4 个核心能力卡片展示
3. 产品优势 - 图文混排的优势说明
4. 操作引导区 - 创建表单 / 查看文档等入口

页面组成（组件表）

* HeroSection
  * 功能简述：顶部横幅区域，展示产品主标题和核心价值描述
    * 组件属性：
  | 字段名称 | 类型 | 说明 |
  | ---------------| --------| -----------------------|
  | title | string | 主标题文字内容 |
  | subtitle | string | 副标题文字内容 |
  | backgroundUrl | string | 背景图片的 URL 地址 |

* KeyFeatures
  * 功能简述：核心功能卡片展示区，3 - 4 个等宽卡片
    * 组件属性：
  | 字段名称 | 类型 | 说明 |
  | ----------| -----------------------| --------------------|
  | features | Array<featureitem> | 功能项数组 |

  interface FeatureItem {
  iconUrl: string;
  title: string;
  description: string;
}

* AdvantageSection
  * 功能简述：图文混排的产品优势说明区（左图右文 / 左文右图交替）
* 组件属性：
  | 字段名称 | 类型 | 说明 |
  | -------------| -----------------------| ---------------------|
  | advantages | Array<advantageitem> | 优势说明项数组 |

  interface AdvantageItem {
  imageUrl: string;
  title: string;
  description: string;
  isImageLeft: boolean; // 控制图文排列方向
}

* CallToAction
  * 功能简述：操作引导按钮组（开始使用 + 查看文档）
* 组件属性：
  | 字段名称 | 类型 | 说明 |
  | ----------------| ----------| -------------------------|
  | primaryLabel | string | 主按钮文字 |
  | secondaryLabel | string | 次要按钮文字 |
  | onPrimaryClick | () =& gt; void | 主按钮点击事件回调 |
  | onSecondaryClick | () =& gt; void | 次要按钮点击事件回调 |

  其它要求
1. 所有文字内容通过 props 传入实现可配置化
2. 图片资源使用 CDN URL 传递
3. 响应式布局要求：移动端堆叠排列，PC 端横向排列

其它备忘

  * 页面层组合方式：
<pagelayout>
  <herosection {...heroprops}="">
  <keyfeatures features="{featureList}">
  <advantagesection advantages="{advantageList}">
  <calltoaction {...ctaprops}="">
</calltoaction></advantagesection></keyfeatures></herosection></pagelayout>
  * 所有内容数据通过 page 层传入，保持组件纯净
    * 样式使用 CSS Modules 实现局部作用域
