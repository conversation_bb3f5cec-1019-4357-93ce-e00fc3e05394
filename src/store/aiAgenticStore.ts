import { create } from 'zustand';

import { composedMiddlewares } from './zustand';

import type { StateCreator } from 'zustand';
import type { BaseAgent } from '@/core/BaseAgent';
import type { ComponentArtifactType } from '@/tools';

export type ArtifactMode = 'code' | 'preview' | 'print' | 'logger';

type AIAgentState = {
  generalPRD: string;
  currentExecutingAgent?: BaseAgent;
  currentPageDataInterfaceInfo?: string;
  currentAppCubeInfo?: string;
  componentSrcList?: ComponentArtifactType[];
};

type AIAgentActions = {
  setCurrentExecutingAgent: (agent: BaseAgent) => void;
  setCurrentPageInterfaceInfo: (info: any) => void;
  setComponentSrcList: (srcList: ComponentArtifactType[]) => void;
  setGeneralPRD: (prd: string) => void;
  setCurrentAppCubeInfo: (info: string) => void;
};

const stateCreator: StateCreator<AIAgentState & AIAgentActions> = (set, get) => ({
  currentExecutingAgent: undefined,
  currentPageDataInterfaceInfo: undefined,
  currentAppCubeInfo: undefined,
  setCurrentAppCubeInfo: (info) => set({ currentAppCubeInfo: info }),
  setCurrentPageInterfaceInfo: (info) => set({ currentPageDataInterfaceInfo: info }),
  setCurrentExecutingAgent: (agent) => set({ currentExecutingAgent: agent }),
  setComponentSrcList: (srcList) => set({ componentSrcList: srcList }),
  setGeneralPRD: (prd) => {
    set({ generalPRD: prd });
  },
  generalPRD: '',
  componentSrcList: [],
});

export const useAIAgentStateStore = create<AIAgentState & AIAgentActions>()(
  composedMiddlewares(stateCreator, 'AIAgentStateStore'),
);

window.__AIAgentStateStore = useAIAgentStateStore;
