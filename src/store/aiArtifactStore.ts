import { create } from 'zustand';

import { extractPageCode } from '@/tools/GenerateCodeTool/code';

import { composedMiddlewares } from './zustand';

import type { StateCreator } from 'zustand';
export type ArtifactMode = 'code' | 'preview' | 'print' | 'logger' | 'prd';

type AIState = {
  isCodeGenerated: boolean;
  /**
   * Final Entry Code
   */
  code: string;
  componentList: Array<{
    code: string;
    fileName: string;
  }>;
  logger: string;
  previewIframeRef: HTMLIFrameElement | null;
  artifactMode: ArtifactMode;
  artifactInlineHtml?: string;
  artifactPreviewUrl: string;
  errorMsg: string;
  prdReady?: boolean;
  isPrdInGenerating: boolean;
  prdContent: string;
};

type AIActions = {
  setCode: (
    code: string,
    options?: {
      mode?: 'create' | 'update';
    },
  ) => void;
  setPreviewIframeRef: (iframeRef: HTMLIFrameElement | null) => void;
  /**
   * Set the component list
   * @param componentList - The list of components
   */
  setComponentList: (componentList: Array<{ code: string; fileName: string }>) => void;
  setComponentCode: (fileName: string, code: string) => void;
  setLogger: (logger: string) => void;
  setArtifactMode: (artifactMode: ArtifactMode) => void;
  setArtifactPreviewUrl: (artifactPreviewUrl: string) => void;
  setArtifactInlineHtml: (artifactInlineHtml: string) => void;
  setErrorMsg: (errorMsg: string) => void;
  hasError: () => boolean;
  setIsCodeGenerated: (isCodeGenerated: boolean) => void;
  setPrdContent: (prdContent: string) => void;
  onPRDReady: () => Promise<string>;
  setPRDReady: (prdContent?: string) => void;
  setPRDInGenerating: (isPrdInGenerating: boolean) => void;
};

export const agentConfigList = [
  {
    label: '智能应用(深度思考)',
    value: 'manus',
    icon: 'application',
  },
  {
    label: '智能应用(快速模式)',
    value: 'app_quick',
    icon: 'application',
  },
  {
    label: '智能页面(无深度思考)',
    value: 'page-no-thinking',
    icon: 'page',
  },
  {
    label: '工具调试(开发专用)',
    value: 'tool',
    icon: 'plugin',
  },
  {
    label: 'Polymind(数据分析)',
    value: 'polymind',
    icon: 'chart-line',
  },
  {
    label: 'Polymind(Excel处理)',
    value: 'excel',
    icon: 'excel',
  },
];

export const EVENTS = {
  prdReady: 'yida.codegen.prdReady',
};

const stateCreator: StateCreator<AIState & AIActions> = (set, get) => ({
  code: '',
  logger: '',
  componentList: [],
  isCodeGenerated: false,
  isPrdInGenerating: false,
  prdReady: false,
  artifactMode: 'logger',
  artifactPreviewUrl: '',
  artifactInlineHtml: '',
  errorMsg: '',
  prdContent: '',
  previewIframeRef: null,
  setPreviewIframeRef: (iframeRef) => {
    set({ previewIframeRef: iframeRef });
  },
  setComponentList: (componentList) => {
    set({ componentList });
  },
  setComponentCode: (fileName: string, code) => {
    set((state: any) => {
      const componentList = state.componentList;
      const index = componentList.findIndex((item: any) => item.fileName === fileName);
      if (index !== -1) {
        componentList[index].code = code;
      } else {
        componentList.push({
          code,
          fileName,
        });
      }
    });
  },
  setCode: (code, { mode } = { mode: 'create' }) => {
    if (mode === 'create') {
      const tarCode = extractPageCode(code, '');
      set({ code: tarCode || code });
    } else {
      set({ code });
    }
  },
  setLogger: (logger) => {
    set({ logger });
  },
  setIsCodeGenerated: (isCodeGenerated) => {
    set({ isCodeGenerated });
  },
  setArtifactMode: (artifactMode) => {
    if (artifactMode === get().artifactMode) {
      return;
    }
    set({ artifactMode });
  },
  setArtifactPreviewUrl: (artifactPreviewUrl) => {
    set({ artifactPreviewUrl });
  },
  setArtifactInlineHtml: (artifactInlineHtml) => {
    set({ artifactInlineHtml });
  },
  setErrorMsg: (errorMsg) => {
    set({ errorMsg });
  },
  setPrdContent: (prdContent) => {
    set({ prdContent });
  },
  hasError: () => {
    return get().errorMsg !== '';
  },
  setPRDReady: (prdContent) => {
    const artifactStore = useAIArtifactStateStore.getState();
    artifactStore.setPrdContent(prdContent);
    artifactStore.setArtifactMode('prd');
    const event = new CustomEvent(EVENTS['prdReady'], {
      detail: {
        prdContent,
      },
    });
    set({ prdReady: true });
    document.dispatchEvent(event);
  },
  setPRDInGenerating: (isPrdInGenerating) => {
    set({ isPrdInGenerating });
  },
  onPRDReady() {
    return new Promise((resolve) => {
      document.addEventListener(EVENTS['prdReady'], (e: any) => {
        this.setArtifactMode('prd');
        resolve(get().prdContent);
      });
    });
  },
});

export const useAIArtifactStateStore = create<AIState & AIActions>()(
  composedMiddlewares(stateCreator, 'AIArtifactStateStore'),
);

window.__AIArtifactStateStore = useAIArtifactStateStore;
