import type { StateCreator } from 'zustand';
import { create } from 'zustand';
import { composedMiddlewares } from './zustand';
import { getLocal, parseQuery } from '@ali/yc-utils';
import type { ChatManager, ChatMessage } from '@/utils/ChatManager';
import type { CoderAgent } from '@/core/CoderAgent';

export type YidaAgentType = 'manus' | 'app_quick' | 'page-no-thinking' | 'page-print' | 'tool' | 'polymind' | 'excel';

interface AIState {
  isProcessing: boolean;
  isWaitingForResponse: boolean;
  agentType: YidaAgentType;
  chatInputValue: string;
  chatInputRef: React.RefObject<HTMLInputElement> | null;
  chatManagerRef: React.RefObject<ChatManager> | null;
  msgContainerRef: React.RefObject<HTMLDivElement> | null;
  messages: ChatMessage[];
  coderAgent: CoderAgent | null;
  enableAutoScroll: boolean;
  attachments: any[];
  isRightPanelVisible: boolean;
  countdownInterval: NodeJS.Timeout | null;
  isUserFocusedOnInput: boolean;
  countdown: number;
}

interface AIActions {
  setChatInputRef: (chatInputRef: React.RefObject<HTMLInputElement>) => void;
  setChatManagerRef: (chatManagerRef: React.RefObject<ChatManager>) => void;
  setMsgContainerRef: (msgContainerRef: React.RefObject<HTMLDivElement>) => void;
  setEnableAutoScroll: (enableAutoScroll: boolean) => void;
  setIsProcessing: (isProcessing: boolean) => void;
  setIsWaitingForResponse: (isWaitingForResponse: boolean) => void;
  setChatInputValue: (chatInputValue: string) => void;
  setAgentType: (agentType: YidaAgentType) => void;
  setMessages: (messages: ChatMessage[] | ((prev: ChatMessage[]) => ChatMessage[])) => void;
  addMessage: (message: ChatMessage) => void;
  setAttachments: (attachments: any[]) => void;
  scrollToBottom: () => void;
  setRightPanelVisible: (visible: boolean) => void;
  setCountdownInterval: (interval: NodeJS.Timeout) => void;
  setIsUserFocusedOnInput: (isUserFocusedOnInput: boolean) => void;
  setCountdown: (countDown: number) => void;
  /**
   * 发送消息
   * @param msg 消息内容
   * 如果不传递，从 inputValue 中获取
   */
  sendMessage: (msg?: string) => void;
  setCoderAgent: (coderAgent: CoderAgent) => void;
}

const defaultAgentType = parseQuery().agent || getLocal('yida_manus_currentAgent') || 'manus';
const urlParams = new URLSearchParams(window.location.search);
const defaultIsRightPanelVisible = urlParams.get('hr') === '1';

const stateCreator: StateCreator<AIState & AIActions> = (set, get) => ({
  isProcessing: false,
  isWaitingForResponse: false,
  coderAgent: null,
  agentType: defaultAgentType,
  messages: [],
  chatInputValue: '',
  chatInputRef: null,
  chatManagerRef: null,
  msgContainerRef: null,
  enableAutoScroll: true,
  attachments: [],
  isRightPanelVisible: defaultIsRightPanelVisible,
  countdownInterval: null,
  isUserFocusedOnInput: false,
  countdown: 30,
  setEnableAutoScroll: (enableAutoScroll) => {
    set({ enableAutoScroll });
  },
  setChatInputRef: (chatInputRef) => {
    set({ chatInputRef });
  },
  setChatManagerRef: (chatManagerRef) => {
    set({ chatManagerRef });
  },
  setMsgContainerRef: (msgContainerRef) => {
    set({ msgContainerRef });
  },
  setIsWaitingForResponse: (isWaitingForResponse) => {
    set({ isWaitingForResponse });
  },
  setChatInputValue: (chatInputValue) => {
    set({ chatInputValue });
  },
  setIsProcessing: (isProcessing) => {
    set({ isProcessing });
  },
  setAgentType: (_agentType: any) => {
    if (_agentType === 'page-print') {
      get().coderAgent?.setScenario('print');
    }
    set({ agentType: _agentType });
  },
  setAttachments: (_attachments: any[]) => {
    set({ attachments: _attachments });
  },
  sendMessage: async (newMsg?: string) => {
    const {
      chatInputRef,
      chatManagerRef,
      chatInputValue,
      messages,
      setMessages,
      setIsProcessing,
      agentType,
      setChatInputValue,
      coderAgent,
      setIsWaitingForResponse,
      attachments,
    } = get();
    if (!chatInputValue.trim() && !newMsg) return;
    // 保存当前输入值以备后用
    let currentInput = newMsg || chatInputValue.trim() || '';
    if (typeof currentInput !== 'string') return;
    if (!currentInput) return;

    // 检查是否有图片附件，如果有则在消息前拼接图片信息
    if (attachments.length > 0) {
      const tarAttachments = attachments.map((item) => {
        const newItem = { ...item };
        delete newItem.allData;
        delete newItem.metaData;
        return newItem;
      });
      currentInput = `${currentInput}\n\n<sup>附件列表：${JSON.stringify(tarAttachments)}</sup>`;
    }
    if (agentType === 'page-no-thinking' || agentType === 'page-print') {
      const newMessageList = [
        ...messages,
        { id: `user_${+new Date()}`, content: currentInput, isUser: true, timestamp: new Date() },
      ];
      setMessages(newMessageList);
      setIsProcessing(true);
      setChatInputValue('');
      await coderAgent.handleMessage(currentInput, newMessageList, setMessages);
      setIsProcessing(false);
      return;
    }
    // 检查是否有等待用户回复的问题
    if (globalThis.waitingForUserResponse && typeof globalThis.waitingForUserResponse.resolve === 'function') {
      // 如果有等待的问题，则调用resolve函数传递用户回复
      const userResponse = currentInput;
      // 将回复传递给等待的promise（不添加到聊天消息中）
      globalThis.waitingForUserResponse.resolve(userResponse);
      // 清除等待状态
      globalThis.waitingForUserResponse = undefined;
      setIsWaitingForResponse(false);
      // 清空输入框
      setChatInputValue('');
      // 重新聚焦输入框
      setTimeout(() => {
        if (chatInputRef?.current) {
          chatInputRef.current.focus();
        }
      }, 100);
      return;
    }
    // 先清空输入框，再发送消息，避免消息处理延迟导致的输入框清空延迟
    setChatInputValue('');
    // @ts-ignore
    if (chatInputRef?.current) {
      chatInputRef.current.value = '';
    }
    // 发送消息时滚动到底部
    const { scrollToBottom } = get();
    scrollToBottom();
    // 真实发送消息
    await chatManagerRef.current.handleSendMessage(currentInput);
    // 重新聚焦输入框
    setTimeout(() => {
      if (chatInputRef?.current) {
        chatInputRef.current.focus();
      }
    }, 100);
  },
  setMessages: (messages) => {
    if (typeof messages === 'function') {
      set({ messages: messages(get().messages) });
    } else {
      set({ messages });
    }
  },
  addMessage: (message) => {
    set((state) => {
      state.messages.push(message);
      return state;
    });
  },
  setCoderAgent: (coderAgent) => {
    set({ coderAgent });
  },
  scrollToBottom: () => {
    const { msgContainerRef } = get();
    if (msgContainerRef?.current) {
      msgContainerRef.current.scrollTop = msgContainerRef.current.scrollHeight;
    }
  },
  setRightPanelVisible: (visible: boolean) => {
    const urlParams = new URLSearchParams(window.location.search);
    if (visible) {
      urlParams.append('hr', '1');
    } else {
      urlParams.delete('hr');
    }
    const url = window.location.origin + window.location.pathname + '?' + urlParams.toString();
    window.history.pushState({}, '', url);
    set({ isRightPanelVisible: visible });
  },
  setCountdownInterval: (countdownInterval: NodeJS.Timeout) => {
    set({ countdownInterval });
  },
  setIsUserFocusedOnInput: (isUserFocusedOnInput: boolean) => {
    set({ isUserFocusedOnInput });
  },
  setCountdown: (countdown: number) => {
    set({ countdown });
  },
});

export const useAIChatStore = create<AIState & AIActions>()(composedMiddlewares(stateCreator, 'AIChatStore'));

window.__AIChatStore = useAIChatStore;

export const getUserOriginalFirstChatMsg = () => {
  return useAIChatStore.getState().messages[1]?.content;
};
