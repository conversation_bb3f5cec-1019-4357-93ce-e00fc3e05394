import { create } from 'zustand';

import { getLocal, setLocal } from '@ali/yc-utils';

import { composedMiddlewares } from './zustand';

import type { StateCreator } from 'zustand';
import type { GenerateAppComplexityLevel } from '@/tools/GenerateBizPRDTool/prd-gen.share';

export type UIFrameworkType = 'antd' | 'radix-ui';

export type MODEL_VENDER_TYPE = 'deepseek' | 'aliyun' | 'ding' | 'openrouter';

export const OPEN_ROUTER_BASE_URL = 'https://openrouter.ai/api/v1/chat/completions';
// export const isEnableOpenRouter = location.search.includes('__ENABLE_OPEN_ROUTER=true');
export const OPEN_ROUTER_API_KEY = getLocal('openRouter-sk') || '';
export const OPEN_ROUTER_V3_MODEL = 'deepseek/deepseek-chat-v3-0324';
export const OPEN_ROUTER_REASONER_MODEL = 'deepseek/deepseek-r1';

export const AVAILABLE_MODELS = {
  'anthropic/claude-3.7-sonnet': {
    maxTokens: 100000,
  },
  'anthropic/claude-sonnet-4': {
    maxTokens: 100000,
  },
  [OPEN_ROUTER_V3_MODEL]: {
    maxTokens: 80000,
  },
  qwen3: {
    maxTokens: 30000,
  },
  'dingtalk-qwen3-235b': {
    maxTokens: 30000,
  },
  'google/gemini-2.5-pro-exp-03-25:free': {
    maxTokens: 10000,
  },
  'deepseek-reasoner': {
    maxTokens: 8000,
  },
  'deepseek-chat': {
    maxTokens: 8000,
  },
};

export interface IMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

type AISettingState = {
  modelType: MODEL_VENDER_TYPE;
  model: string;
  mainModel: string;
  temperature: string;
  useTemplate: boolean;
  useDynamicAPI: string | boolean;
  /**
   * 将 PRD Inject 到 CodeGen 工具中
   */
  enableCodeGenPRDContent: boolean;
  appComplexity: GenerateAppComplexityLevel;
  uiFramework: 'antd' | 'radix-ui' | 'none';
  uiAIEditing: boolean;
  bailianSk: string;
  deepseekSk: string;
  openRouterSk: string;
};

type AISettingActions = {
  setModelType: (modelType: MODEL_VENDER_TYPE) => void;
  setModel: (model: string) => void;
  setMainModel: (mainModel: string) => void;
  setTemperature: (temperature: string) => void;
  setUseTemplate: (useTemplate: boolean) => void;
  setUseDynamicAPI: (useDynamicAPI: string | boolean) => void;
  setAppComplexity: (appComplexity: GenerateAppComplexityLevel) => void;
  setUiFramework: (uiFramework: UIFrameworkType) => void;
  setEnableCodeGenPRDContent: (enableCodeGenPRDContent: boolean) => void;
  setUiAIEditing: (uiAIEditing: boolean) => void;
  setBailianSk: (bailianSk: string) => void;
  setDeepseekSk: (deepseekSk: string) => void;
  setOpenRouterSk: (openRouterSk: string) => void;
};

// const enablePRDCodeGen = getLocal('yida_code_gen_enable_prd_content');

// export const isEnableCodeGenPRDContent =
//   enablePRDCodeGen === undefined || enablePRDCodeGen === '' || enablePRDCodeGen === 'true' || enablePRDCodeGen === null
//     ? true
//     : getLocal('yida_code_gen_enable_prd_content');

export const isEnableCodeGenPRDContent = true;

// Initialize with default values from local storage
const getInitialState = (): AISettingState => ({
  modelType: getLocal('yida_manus_modelType') || 'ding',
  model: getLocal('yida_manus_model') || 'dingtalk-qwen3-235b',
  mainModel: getLocal('yida_main_model') || 'dingtalk-qwen3-235b',
  temperature: getLocal('yida_manus_temperature') || '1',
  useTemplate: getLocal('yida_manus_useTemplate') || false,
  useDynamicAPI: getLocal('yida_code_gen_dynamic_data') || 'enabled',
  appComplexity: getLocal('yida_code_gen_app_complexity') || 'medium',
  uiFramework: getLocal('yida_code_gen_ui_framework') || 'antd',
  enableCodeGenPRDContent: true,
  uiAIEditing: getLocal('yida_code_gen_ui_ai_editing') || false,
  bailianSk: getLocal('bailian-sk') || '',
  deepseekSk: getLocal('deepseek-sk') || '',
  openRouterSk: getLocal('openRouter-sk') || '',
});

const stateCreator: StateCreator<AISettingState & AISettingActions> = (set) => ({
  ...getInitialState(),

  setUiAIEditing(uiAIEditing) {
    setLocal('yida_code_gen_ui_ai_editing', uiAIEditing);
    set({ uiAIEditing });
  },

  setEnableCodeGenPRDContent: (enableCodeGenPRDContent) => {
    setLocal('yida_code_gen_enable_prd_content', enableCodeGenPRDContent);
    set({ enableCodeGenPRDContent });
  },

  setModelType: (modelType) => {
    setLocal('yida_manus_modelType', modelType);
    set({ modelType });
  },

  setModel: (model) => {
    setLocal('yida_manus_model', model);
    set({ model });
  },

  setMainModel: (mainModel) => {
    setLocal('yida_main_model', mainModel);
    set({ mainModel });
  },

  setTemperature: (temperature) => {
    setLocal('yida_manus_temperature', temperature);
    set({ temperature });
  },

  setUseTemplate: (useTemplate) => {
    setLocal('yida_manus_useTemplate', useTemplate);
    set({ useTemplate });
  },

  setUseDynamicAPI: (useDynamicAPI) => {
    const normalizedValue =
      typeof useDynamicAPI === 'boolean' ? (useDynamicAPI ? 'enabled' : 'disabled') : useDynamicAPI;

    setLocal('yida_code_gen_dynamic_data', normalizedValue);
    set({ useDynamicAPI: normalizedValue });
  },

  setAppComplexity: (appComplexity) => {
    setLocal('yida_code_gen_app_complexity', appComplexity);
    set({ appComplexity });
  },

  setUiFramework: (uiFramework) => {
    setLocal('yida_code_gen_ui_framework', uiFramework);
    set({ uiFramework });
  },

  setBailianSk: (bailianSk) => {
    setLocal('bailian-sk', bailianSk);
    set({ bailianSk });
  },

  setDeepseekSk: (deepseekSk) => {
    setLocal('deepseek-sk', deepseekSk);
    set({ deepseekSk });
  },

  setOpenRouterSk: (openRouterSk) => {
    setLocal('openRouter-sk', openRouterSk);
    set({ openRouterSk });
  },
});

export const useAISettingStore = create<AISettingState & AISettingActions>()(
  composedMiddlewares(stateCreator, 'AISettingStore'),
);

// Add to window for debugging
window.__AISettingStore = useAISettingStore;
