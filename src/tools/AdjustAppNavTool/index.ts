import { ToolExecutionError } from '@/utils/Errors';
import {
  getFormNavigationListByOrder,
  saveFormNavigation,
  updateFormNavigation,
  updateFormNavigationOrder,
} from '@/apis';
import { BaseTool } from '@/utils/Tool';
import { II18n } from '@ali/yc-utils';
import { log } from '@/utils/decorators/log';

// 修改应用菜单
interface AdjustAppNavArgs {
  // 应用ID
  appId: string;
  // 首页页面ID
  homePageId?: string;
}

/**
 * 调整应用菜单工具
 */
export class AdjustAppNavTool extends BaseTool {
  name = 'adjust_app_nav';
  nameCn = '修改应用导航菜单';
  description = '用于调整应用导航菜单，主要通过调整页面顺序，增加文件夹等方式让页面展示更加清晰合理';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用唯一标识，格式示例:APP_XXX',
      },
      homePageId: {
        type: 'string',
        description: '应用首页的页面ID，格式示例:FORM-XXX',
      },
    },
    required: ['appId'],
  };
  /**
   * 优化应用菜单
   * @param args 优化应用菜单的参数
   * @returns 优化结果
   */
  @log()
  async execute(args: AdjustAppNavArgs): Promise<any> {
    const { appId, homePageId } = args;
    try {
      const title: II18n = {
        type: 'i18n',
        zh_CN: '数据表',
        en_US: 'Data Table',
      };
      // 创建数据表文件夹
      const navId = await saveFormNavigation({
        appType: appId,
        title,
      });
      const pageList = await getFormNavigationListByOrder({ appType: appId });
      const dataFolder = pageList.find((page) => page.navId === navId);
      const homePage = pageList.find((page) => page.id === homePageId);
      const formPageList = pageList.filter((page) => page.type === 'receipt');
      const displayPageList = pageList.filter((page) => page.type === 'display' && page.id !== homePageId);
      const viewPageList = pageList.filter((page) => page.type === 'view');
      const systemPageList = pageList.filter((page) => page.navType === 'SYSTEM');
      const ids = [
        ...systemPageList.map((page) => page.navId),
        homePage?.navId,
        ...displayPageList.map((page) => page.navId),
        ...viewPageList.map((page) => page.navId),
        ...formPageList.map((page) => page.navId),
        navId,
      ].filter((id) => id);
      // 数据表统一放入数据文件夹
      for (const formPage of formPageList) {
        await updateFormNavigationOrder({
          appType: appId,
          parentNavUuid: dataFolder.id,
          currentId: formPage.navId,
          ids,
        });
      }

      await updateFormNavigation({
        appType: appId,
        id: navId,
        title,
        navUuid: dataFolder.id,
        hidden: 'y',
        mobileHidden: 'y',
        formUuid: 'NAV-SYSTEM-FROM-ME-UUID',
      });

      await updateFormNavigationOrder({
        appType: appId,
        currentId: homePage?.navId,
        ids,
      });

      return {
        status: '✅',
        appId,
        content: '应用导航优化成功',
      };
    } catch (error) {
      this.logger.error(`应用导航优化失败: ${error}`);
      throw new ToolExecutionError(this.name, `应用导航优化失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: AdjustAppNavArgs): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.appId) {
      errors.push('应用Id不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
