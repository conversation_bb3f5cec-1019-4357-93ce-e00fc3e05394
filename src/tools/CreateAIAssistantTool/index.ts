import { ToolExecutionError } from '@/utils/Errors';
import { ToolValidator } from '@/utils/Validator';
import { createAIAssistant } from '@/apis';
import { getYidaConfig } from '@ali/yc-utils';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';
/**
 * 创建AI助理工具的输入参数
 */
interface CreateAIAssistantArgs {
  /** 助理名称 */
  name: string;
  /** 助理介绍 */
  instructions: string;
  /** 推荐的提示语 */
  recommendPrompts?: string[];
  /** 应用ID，通常是APP_XXXX格式 */
  appId: string;
  /** 应用名称 */
  appName: string;
}

/**
 * 创建AI助理工具
 * 用于快速创建与应用关联的AI助理
 */
export class CreateAIAssistantTool extends BaseTool {
  name = 'create_ai_assistant';
  nameCn = '创建AI助理';
  description = `创建一个新的AI助理，与应用关联。
助理名称要求1～8个字符，仅限中文汉字、英文字母、数字。
推荐提示词用于引导用户使用AI助理，通常是指引用户如何提交表单、分析数据等操作。`;
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: '助理名称，1～8个字符，仅限中文汉字、英文字母、数字',
      },
      instructions: {
        type: 'string',
        description:
          '助理介绍。基于宜搭低代码应用的AI助理介绍，具备智能填报、数据分析&洞察、数据推送等能力。助理介绍必须在10～100个字符之间',
      },
      recommendPrompts: {
        type: 'array',
        description: '推荐的提示语，用于引导用户使用AI助理，推荐3条',
        items: {
          type: 'string',
        },
      },
      appId: {
        type: 'string',
        description: '应用ID，通常是APP_XXXX格式',
      },
      appName: {
        type: 'string',
        description: '应用名称',
      },
    },
    required: ['name', 'appName', 'appId'],
  };

  getOpenAiAgentUrl(agentCode: string) {
    const agentChatUrl = `https://applink.dingtalk.com/client/aiAgent?assistantId=${agentCode}&corpId=${getYidaConfig(
      'corpId',
    )}&effectCorpId=${getYidaConfig('corpId')}&from=yida`;
    const url = `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(agentChatUrl)}`;
    return url;
  }

  /**
   * 创建AI助理
   * @param args 创建AI助理的参数
   * @returns 创建结果
   */
  @log()
  async execute(args: CreateAIAssistantArgs): Promise<any> {
    const { name, instructions, recommendPrompts, appId, appName } = args;

    try {
      // 调用API创建AI助理
      const response = await createAIAssistant({
        name,
        instructions,
        recommendPrompts,
        appType: appId,
        appName,
      });

      if (!response) {
        throw new Error('创建AI助理失败');
      }

      const defaultRecommendPrompts = ['👀 帮我提交一个表单', '📈 帮我做数据分析', '💼 如何使用这个应用'];
      // 返回创建结果
      return {
        status: '✅',
        assistantName: name,
        assistantAgentCode: response?.aiAssistantId,
        assistantAvatar: `<img src="${response.iconUrl}" width="40px" height="40px" />`,
        assistantChatUrl: `[点击对话](${this.getOpenAiAgentUrl(response?.aiAssistantId)})`,
        assistantRecommendPrompts: (recommendPrompts || defaultRecommendPrompts).join('、'),
      };
    } catch (error) {
      throw new ToolExecutionError(
        this.name,
        `创建AI助理失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 验证参数
   * @param args 参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    // 使用通用验证器进行基础验证
    const baseValidation = ToolValidator.validate(this.parameters, args);
    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];

    // 验证name格式 (1-8个字符，仅限中文、英文、数字)
    if (args.name) {
      if (args.name.length < 1 || args.name.length > 8) {
        errors.push('助理名称长度必须在1到8个字符之间');
      }

      if (!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(args.name)) {
        errors.push('助理名称只能包含中文汉字、英文字母和数字');
      }
    }

    // 验证instructions格式
    if (args.instructions) {
      if (args.instructions.length < 10 || args.instructions.length > 100) {
        errors.push('助理介绍长度必须在10到100个字符之间');
      }
    }

    // 验证appId格式
    if (args.appId && !args.appId.startsWith('APP_')) {
      errors.push('应用ID格式不正确，应以APP_开头');
    }

    // 验证recommendPrompts
    if (args.recommendPrompts) {
      if (!Array.isArray(args.recommendPrompts)) {
        errors.push('推荐提示语必须是数组');
      } else if (!args.recommendPrompts.length) {
        errors.push('推荐提示语不能为空');
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
