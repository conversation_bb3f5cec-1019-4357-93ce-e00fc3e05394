import { ToolExecutionError } from '@/utils/Errors';
import { triggerTxtToImg } from '@/apis/triggerTxtToImg';
import { request, getYidaConfig } from '@ali/yc-utils';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

// 创建应用图标参数
interface CreateAppIconArgs {
  // 应用名称
  appName: string;
  // 图标描述
  description?: string;
  // 图标颜色
  color?: string;
}

/**
 * 创建应用图标工具
 * 用于快速生成应用图标
 */
export class CreateAppIconTool extends BaseTool {
  name = 'create_app_icon';
  nameCn = '创建应用图标';
  description = '为应用生成高质量的应用图标';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appName: {
        type: 'string',
        description: '应用名称，AI会根据名称生成相关的图标',
      },
      description: {
        type: 'string',
        description: '图标的详细描述，例如"简约现代商务风格"、"科技感蓝色图标"等',
      },
      color: {
        type: 'string',
        description: '主题色',
      },
    },
    required: ['appName'],
  };
  /**
   * 创建应用图标
   * @param args 创建图标的参数
   * @returns 创建结果，包含图标URL
   */
  @log()
  async execute(args: CreateAppIconArgs): Promise<any> {
    try {
      this.logger.info('开始生成应用图标');
      // 生成AI提示词
      const prompt = this.generatePrompt(args);
      const size = '1024*1024';
      // 开始生成图标
      const reqData = {
        prompt,
        size,
        batchSize: 1,
      };
      const result: any = { prompt };
      try {
        // 调用API生成图标
        const response = await triggerTxtToImg(reqData);
        const url = response?.[0].replace(/^(.*?)https/, 'https');

        const res = await request({
          method: 'POST',
          url: '/query/attach/uploadNoAuth.json',
          data: {
            appType: getYidaConfig('appType') || 'default_tianshu_app',
            formUuid: getYidaConfig('formUuid'),
            fileName: '应用图标.png',
            imageUrl: url,
          },
        });
        this.logger.info(`🚀 ~ CreateAppIconTool xxxxxxxxxxxxxxxxx~ res: ${JSON.stringify(res)}`);
        if (!res?.url) {
          throw new Error('生成图片失败');
        }
        // 这里直接返回结果
        result.iconImage = `<img src="${res.url}" width="40px" height="40px" />`;
        result.iconUrl = res.url;
      } catch (apiError) {
        this.logger.error(`应用图标生成失败: ${apiError}`);
        throw new ToolExecutionError(this.name, `应用图标生成失败: ${apiError}`);
      }
      this.logger.info('应用图标生成成功');
      return result;
    } catch (error) {
      this.logger.error(`应用图标生成失败: ${error}`);
      throw new ToolExecutionError(this.name, `应用图标生成失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.appName) {
      errors.push('应用名称不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * 转换参数为AI提示词
   */
  private generatePrompt(args: CreateAppIconArgs): string {
    const { appName, description, color } = args;
    let prompt = `为「${appName}」应用生成高质量应用图标`;
    if (description) {
      prompt += `，${description}`;
    }
    if (color) {
      prompt += `，颜色：${color}`;
    }
    prompt += '，风格：平面设计，简洁专业，适合应用图标，无文字，居中构图';

    return prompt;
  }
}
