import { ToolExecutionError } from '@/utils/Errors';
import { registerApp, updateApp } from '@/apis';
import { BaseTool } from '@/utils/Tool';
import { CreateAppIconTool } from '@/tools/CreateAppIconTool';
import { uniqueId } from '@ali/ve-utils';
import { log } from '@/utils/decorators/log';

// 创建应用的输入参数
interface CreateAppArgs {
  // 应用名称
  appName: string;
  // 应用描述
  description?: string;
  // 应用模板，可选择已有模板快速创建
  template?: string;
  // 应用图标
  icon?: string;
  // 应用图标URL
  iconUrl?: string;
  // 应用主题颜色
  color?: string;
}

const ICON_NAMES = [
  '新闻: xian-xinwen',
  '地球: xian-diqiu',
  '政府: xian-zhengfu',
  '汽车: xian-qiche',
  '应用: xian-yingyong',
  '飞机: xian-feiji',
  '学术帽: xian-x<PERSON><PERSON><PERSON>',
  '电脑: xian-diannao',
  '企业: xian-qiye',
  '工作证: xian-gongzuozheng',
  '单据: xian-danju',
  '购物车: xian-gouwuche',
  '市场: xian-shichang',
  '信用卡: xian-xinyongka',
  '经理: xian-jingli',
  '活动: xian-huodong',
  '法律: xian-falv',
  '奖杯: xian-jiangbei',
  '报告: xian-baogao',
  '流程: xian-liucheng',
  '火车: huoche',
  '查询: xian-chaxun',
  '申报: xian-shenbao',
  '打卡: xian-daka',
];

const ICON_BACKGROUND_COLORS = [
  '#0089FF',
  '#00B853',
  '#FFA200',
  '#FF7357',
  '#5C72FF',
  '#85C700',
  '#FFC505',
  '#FF6B7A',
  '#8F66FF',
  '#14A9FF',
];

/**
 * 创建应用工具
 * 用于快速创建不同类型的应用
 */
export class CreateAppTool extends BaseTool {
  name = 'create_app';
  nameCn = '创建应用';
  description = `创建新的应用，支持普通表单和图片智能识别表单这2种类型。
icon的格式为"{iconName}%%{背景颜色16进制码}", 例如: xian-xinwen%%#0089FF。
iconUrl为生成图标的URL，有生成则返回生成图标的URL，否则返回空字符串
iconName的选择范围为${ICON_NAMES.join(', ')}
背景颜色16进制码的选择范围为${ICON_BACKGROUND_COLORS.join(', ')}。`;
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appName: {
        type: 'string',
        description: '应用名称',
      },
      description: {
        type: 'string',
        description: '应用描述，简要说明应用的用途和功能',
      },
      template: {
        type: 'string',
        description: '应用模板的ID，可选择预设模板快速创建',
      },
      icon: {
        type: 'string',
        description: '应用图标，请根据iconName和背景颜色16进制码的选项来推荐，格式为"{iconName}%%{背景颜色16进制码}"',
      },
      color: {
        type: 'string',
        description: '主题色，请根据背景颜色16进制码的选项来推荐',
      },
    },
    required: ['appName', 'description', 'icon'],
  };

  /**
   * 创建应用
   * @param args 创建应用的参数
   * @returns 创建结果
   */
  @log()
  async execute(args: CreateAppArgs): Promise<any> {
    const { appName, description, template, icon, color } = args;
    let appTypeType = '';
    try {
      this.feedbackLogger.toolResult('应用图标生成中...');
      const fileId = 'app';
      this.feedbackLogger.file(`正在生成应用图标...`, {
        id: fileId,
        type: 'app',
      });
      let iconUrl = '';
      if (process.env.NODE_ENV !== 'development') {
        const appIconTool = new CreateAppIconTool();
        // Add 30s timeout for icon generation
        const iconPromise = appIconTool.execute({ appName, description, color });
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Icon generation timed out after 30s')), 30000),
        );
        try {
          const result = await Promise.race([iconPromise, timeoutPromise]);
          iconUrl = result?.iconUrl;
        } catch (error) {
          this.feedbackLogger.toolResult('应用图标生成超时，将使用默认图标');
          iconUrl = '';
        }
      }
      this.feedbackLogger.file(`正在创建应用...`, {
        id: fileId,
        type: 'app',
      });
      // 调用API实际创建应用
      appTypeType = await registerApp({
        appName,
        description: description || `${appName}应用`,
        template,
        // homepageLogo: iconUrl,
        icon: iconUrl || icon,
        iconUrl,
      });
      if (!appTypeType) {
        throw new ToolExecutionError(this.name, '应用创建失败，请稍后重试');
      }

      // 获取返回的应用信息
      const appUrl = `https://${window.location.host}/${appTypeType}/workbench`;

      // 关键产物
      this.feedbackLogger.keyResult({
        type: 'app',
        title: appName,
        url: appUrl,
        desc: description,
        icon: iconUrl,
      });

      // 发送预览地址更新消息
      window.postMessage(
        {
          type: 'appReady',
          appId: appTypeType,
        },
        '*',
      );

      // 生成文件
      this.feedbackLogger.file('已完成应用创建 - 预览', {
        id: fileId,
        type: 'app',
        link: appUrl,
      });

      // 生成应用创建日志
      const result = {
        status: '✅',
        appName,
        appId: appTypeType,
        url: appUrl,
        visit: `[访问应用](${appUrl})`,
        appDesc: description,
        theme: color,
      };
      // 返回应用创建的结果，包含应用唯一标识和访问URL
      return result;
    } catch (error) {
      throw new ToolExecutionError(this.name, `创建应用失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.appName) {
      errors.push('应用名称不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
