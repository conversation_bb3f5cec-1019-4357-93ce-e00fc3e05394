import { createPage, getFormSchema, saveFormSchema } from '@/apis';
import { mockService } from '@/services/mockService';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '@/utils/Tool';

import { checkRenderStatus } from '../GenerateCodeTool';
import { generatePageCode, generatePageSchema } from '../GenerateCodeTool/code';
import { log } from '@/utils/decorators/log';

interface GenerateComplexPageParams {
  appId: string;
  pageName: string;
  pageId?: string;
  description: string;
}

export class CreateComplexPageTool extends BaseTool {
  name = 'generate_complex_page';
  nameCn = '生成复杂页面';
  description = '根据上下文中用户应用需求描述，生成 Yida 应用中的复杂页面。';
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      // TODO: add later for debugging only
      // appId: {
      //   type: 'string',
      //   description: '应用类型 (应用唯一标识)',
      // },
      pageName: {
        type: 'string',
        description: '页面名称，创建必填，修改可选',
      },
      pageId: {
        type: 'string',
        description: '页面ID，如果是新建页面，不要传递该参数，如果是修改页面，请务必传递该参数',
      },
      //       description: {
      //         type: 'string',
      //         description: `页面功能描述，分为两个部分：
      // 1. 将用户要求相关的生成意图原方不动地在这里输入；
      // 2. 将生成的 PRD 中和该页面相关的功能原封不动地传递给代码生成器。
      // 3. 将应用和页面的信息上下文也需要提供在该说明中。
      // 这里必须使用之前的生成的内容，切勿自己篡改、总结或者简述，以影响最终的生成效果。`,
      //       },
    },
    required: ['pageName'],
  };

  @log()
  async execute(args: GenerateComplexPageParams): Promise<any> {
    try {
      this.feedbackLogger.toolCall(this.name, args);
      // 目前的 PRD是从 AI Agent 的状态中获取的，不转述
      const description = useAIAgentStateStore.getState().generalPRD;
      const { appId = 'APP_WN5BIJCYKYB8KQKSXEA7', pageName, pageId: prePageId } = args;
      let pageId = prePageId;
      if (!prePageId) {
        const pageRes = await createPage({
          appType: appId,
          name: pageName,
        });
        pageId = pageRes?.pageId;
      }
      if (!pageId) {
        throw new ToolExecutionError(this.name, '页面不存在');
      }
      const schemaRes = await getFormSchema({
        formUuid: pageId,
        appType: appId,
        schemaVersion: 'V5',
      });
      if (!schemaRes) {
        throw new ToolExecutionError(this.name, '获取页面Schema失败');
      }

      // 获取 AICanvas 的代码
      const currentCode = schemaRes?.pages?.[0]?.componentsTree?.[0]?.children?.[0]?.props?.code || '';

      // const appRes = await queryAppContext(appId);
      // this.feedbackLogger.response('appRes' + JSON.stringify(appRes, null, '\t'));

      const currentRunningAgent = useAIAgentStateStore.getState()?.currentExecutingAgent;

      if (!currentRunningAgent) {
        throw new ToolExecutionError(this.name, '当前没有正在执行的 Agent，请退出后重新执行');
      }

      const componentsList = useAIAgentStateStore.getState().componentSrcList;

      const newCode = await mockService.mock(
        async () => {
          return generatePageCode({
            description,
            isModifyCode: false,
            currentCode: currentCode || '',
            components: componentsList,
            appContext: {
              // ...appRes,
              appId,
            },
            systemPromptKey: 'yida_manus_prompt',
          });
        },
        // async () => {
        //   return ``;
        // }
      );

      if (newCode) {
        const schema = await generatePageSchema(pageId, newCode, {
          components: componentsList,
        }).catch((e) => {
          this.logger.error(`生成页面Schema失败: ${e}`);
          throw new ToolExecutionError(this.name, `生成页面失败: ${e}`);
        });

        const res = await saveFormSchema({
          appType: appId,
          formUuid: pageId,
          content: schema,
          schemaVersion: 'V5',
        });
        if (!res) {
          throw new ToolExecutionError(this.name, '保存页面Schema失败');
        }

        this.logger.info('页面代码生成成功');
        const previewUrl = `/${appId}/preview/${pageId}?navConfig.type=none`;
        // 发送预览地址更新消息
        window.postMessage(
          {
            type: 'previewReady',
            url: previewUrl,
          },
          '*',
        );

        await checkRenderStatus().catch((e) => {
          this.logger.error(`页面渲染失败: ${e}`);
          throw new ToolExecutionError(this.name, `页面渲染失败: ${e}`);
        });

        const result = {
          status: '✅',
          pageName,
          pageId,
        };
        return result;
      }
    } catch (e) {
      return Promise.resolve(`ERROR: 生成组件列表失败${e?.toString()}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.pageName) {
      errors.push('页面名称不能为空');
    }
    // if (!args.description) {
    //   errors.push('页面描述不能为空');
    // }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
