import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '@/utils/Tool';
import { stringifyObjectSafe } from '@/utils/LangUtils';
import { generateComponentCode } from '../GenerateCodeTool/code';
import { mockService } from '@/services/mockService';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { log } from '@/utils/decorators/log';

interface GenerateComponentsParams {
  instruction: string;
  components: Array<{
    fileName: string;
    propInterface: string;
    description: string;
  }>;
}

export type ComponentArtifactType = {
  fileName: string;
  code: string;
  success: boolean;
  error?: string;
};

export class CreatePageComponentsTool extends BaseTool {
  name = 'generate_page_components';
  nameCn = '生成组件列表';
  description = '根据上下文中用户应用需求描述，生成组件列表。';
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      //       instruction: {
      //         type: 'string',
      //         description: `组件列表功能描述，分为两个部分：
      // 1. 将用户要求相关的生成意图原方不动地在这里输入；
      // 2. 将生成的 PRD 中和页面构成组件相关的功能原封不动地传递给代码生成器。
      // 3. 将应用和页面的信息上下文也需要提供在该说明中。
      // 这里必须使用之前的生成的内容，切勿自己篡改、总结或者简述，以影响最终的生成效果。`,
      //       },
      components: {
        type: 'array',
        description:
          '组件列表，由于该工具支持并行生成，!!!**务必将所有要求的组件都放在该列表中。**，一次性生成所有组件!!!',
        items: {
          type: 'object',
          properties: {
            fileName: {
              type: 'string',
              description: '组件文件名, e.g. MyComponent.jsx',
            },
            // propInterface: {
            //   type: 'string',
            //   description:
            //     '组件属性接口, 使用 Typescript 描述的接口，包含对接口各个字段的说明和注释，注意字符的转义，保证代码的正确性',
            // },
            // description: {
            //   type: 'string',
            //   description: '组件描述，整体组件功能要实现的能力描述',
            // },
          },
        },
      },
    },
    required: ['components'],
  };

  @log()
  async execute(args: GenerateComponentsParams): Promise<any> {
    const prd = useAIAgentStateStore.getState().generalPRD;
    const instruction = prd || args.instruction || '';
    const components = args.components || [];

    if (!instruction || !components.length) {
      throw new ToolExecutionError(this.name, '生成意图不能为空，获取的上下文为：' + stringifyObjectSafe(args));
    }

    this.feedbackLogger.toolCall(this.name, args);

    const resultRes = mockService.mock(async () => {
      return Promise.all(
        components.map(async (component) => {
          try {
            const code = await generateComponentCode({
              description: instruction,
              fileName: component.fileName,
              currentCode: '',
            });
            return {
              fileName: component.fileName,
              code,
              success: true,
            };
          } catch (error) {
            return {
              fileName: component.fileName,
              error: error.message,
              success: false,
            };
          }
        }),
      );
    });

    const results = await resultRes;
    useAIAgentStateStore.getState().setComponentSrcList((results as ComponentArtifactType[]) || []);
    useAIArtifactStateStore.getState().setComponentList((results as ComponentArtifactType[]) || []);
    return {
      components: results.map((item, index) => {
        return {
          fileName: item.fileName,
        };
      }),
    };
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.components) {
      errors.push('components is required');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
