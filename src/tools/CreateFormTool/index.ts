/* eslint-disable @typescript-eslint/member-ordering */
import { omit } from 'lodash';
import { ToolExecutionError } from '@/utils/Errors';
import { FIELD_INFO } from '@/utils/YidaMeta';
import { BaseTool } from '@/utils/Tool';
import { FormGenerator, IFieldMeta } from '@/utils/FormGenerator';
import { log } from '@/utils/decorators/log';

// 表单类型
export type FormType = 'normalForm' | 'imageAnalysisForm';

// const DEFAULT_APP_ID = 'APP_C7HMC6CO89MRQEXSHLNN';

// 创建表单参数
interface CreateFormArgs {
  // 应用ID
  appId: string;
  // 表单名称
  title: string;
  // 表单类型
  formType: FormType;
  // 表单字段
  fields: IFieldMeta[];
  generateDataManage?: boolean;
}

const formTypeMap = {
  normalForm: '普通表单',
  imageAnalysisForm: '智能图片识别表单',
};

const fieldInfoMeate = Object.entries(FIELD_INFO)
  .map(([key, value]) => `${key}: ${value.desc}`)
  .join('\n');

/**
 * 创建表单工具
 * 用于快速创建各种类型的表单
 */
export class CreateFormTool extends BaseTool {
  name = 'create_form';
  nameCn = '创建表单';
  description = `一种生成表单页面的工具。
  表单字段列表必须包含用户指定的字段，再根据实际使用场景进行字段的合理扩充，以更好的满足实际场景表单对于数据的收集，发挥表单数据的价值挖掘，同时避免字段冗余。
  支持普通表单和智能图片识别表单两种类型，智能图片识别表单必须至少有一个设置了fromImageRecognition=true的非图片字段，且ImageField作为首个字段。
  表单字段类型必须在以下范围：
    -${fieldInfoMeate}
  `;
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用ID，通常为APP_开头的字符串',
      },
      title: {
        type: 'string',
        description: '表单页面名称，表明数据实体名称即可，例如：员工信息',
      },
      formType: {
        type: 'string',
        description:
          '表单类型，normalForm为普通表单，imageAnalysisForm为智能图片识别表单（须将ImageField作为首个字段）',
        enum: ['normalForm', 'imageAnalysisForm'],
      },
      fields: {
        type: 'array',
        description: `表单字段列表，智能图片识别表单须将ImageField作为首个字段，且至少包含两个字段。请务必根据 PRD 中的字段设计进行创建，**严格遵守字段设计**。
        `,
        items: {
          type: 'object',
          properties: {
            label: {
              type: 'string',
              description: '字段标签',
            },
            fieldType: {
              type: 'string',
              description: '字段类型',
              enum: Object.keys(FIELD_INFO),
            },
            required: {
              type: 'boolean',
              description: '字段是否为必填',
            },
            options: {
              type: 'array',
              description: '字段选项，仅RadioField、SelectField、CheckboxField、MultiSelectField需要该参数',
              items: {
                type: 'string',
              },
            },
            multiple: {
              type: 'boolean',
              description: '字段是否为多选，仅EmployeeField、CountrySelectField、DepartmentSelectField需要该参数',
            },
            fromImageRecognition: {
              type: 'boolean',
              description: '字段是否需要从智能图片识别后填充，仅在imageAnalysisForm表单类型下有效',
            },
            subFields: {
              type: 'array',
              description: '子表字段列表，仅TableField需要该参数',
              items: {
                type: 'object',
                properties: {
                  label: {
                    type: 'string',
                    description: '子字段标签',
                  },
                  fieldType: {
                    type: 'string',
                    description: '子字段类型',
                    enum: Object.keys(omit(FIELD_INFO, 'TableField')),
                  },
                  required: {
                    type: 'boolean',
                    description: '子字段是否为必填',
                  },
                  options: {
                    type: 'array',
                    description: '子字段选项，仅RadioField、SelectField、CheckboxField、MultiSelectField需要该参数',
                    items: {
                      type: 'string',
                    },
                  },
                  multiple: {
                    type: 'boolean',
                    description:
                      '子字段是否为多选，仅EmployeeField、CountrySelectField、DepartmentSelectField需要该参数',
                  },
                  relateFormUuid: {
                    type: 'string',
                    description: '关联表单页面ID, 用于关联当前应用下其他表单数据, 如果是AssociationFormField必须该参数',
                  },
                },
                required: ['label', 'fieldType'],
              },
            },
          },
          required: ['label', 'fieldType'],
        },
      },
      generateDataManage: {
        type: 'boolean',
        description: '是否生成数据管理页，对于需要进行数据管理的表单需要生成数据管理页',
      },
    },
    required: ['title', 'formType', 'fields'],
  };

  /**
   * 创建表单
   * @param args 创建表单的参数
   * @returns 创建结果
   */
  @log()
  async execute(args: CreateFormArgs): Promise<any> {
    const { title, fields, appId, formType, generateDataManage } = args;
    const fileId = title;
    this.feedbackLogger.file('正在生成表单...', {
      id: fileId,
      type: 'form',
    });
    const formTitle = title.replace(/(管理|表|Form)?$/, '');
    const formGenerator = new FormGenerator(appId);
    const formUuid = await formGenerator.create(formTitle);
    try {
      formGenerator.setMeta({
        formType,
        fieldMetaList: fields,
      });

      await formGenerator.generateFormSchema();
      await formGenerator.saveSchema();

      if (generateDataManage) {
        await formGenerator.generateDataManage(formTitle, true);
      }

      const formUrl = `//${window.location.host}/${appId}/workbench/${formUuid}?__disableCache=true`;

      // 发送预览地址更新消息
      window.postMessage(
        {
          type: 'previewReady',
          url: formUrl,
        },
        '*',
      );

      // 生成文件
      this.feedbackLogger.file('已完成表单生成 - 预览', {
        id: fileId,
        type: 'form',
        link: formUrl,
        title: formTitle,
      });

      // 生成表单创建日志
      const result: any = {
        status: '✅',
        formType: formTypeMap[formType],
        formTitle,
        formUrl,
        formUuid,
        visitForm: `[访问表单](${formUrl})`,
      };

      return result;
    } catch (error) {
      await formGenerator.delete();
      this.logger.error(`表单创建失败: ${error}`);
      throw new ToolExecutionError(this.name, `表单创建失败: ${error.message}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    const { title, appId, formType, fields } = args;

    if (!title) {
      errors.push('表单名称不能为空');
    }

    if (!appId) {
      errors.push('应用唯一标识appType不能为空');
    }

    const formErrors = FormGenerator.validateFormMeta({
      formType,
      fieldMetaList: fields,
    });
    errors.push(...formErrors);

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
