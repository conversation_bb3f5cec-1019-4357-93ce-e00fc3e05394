import { createPage, saveFormSchema } from '@/apis';
import { ToolExecutionError } from '@/utils/Errors';
import { queryAppContext } from '@/utils/YidaMeta';

import { BaseTool } from '@/utils/Tool';
import { AdjustAppNavTool } from '@/tools/AdjustAppNavTool';
import { generatePageCode, generatePageSchema } from '../GenerateCodeTool/code';
import { log } from '@/utils/decorators/log';

// 创建首页的输入参数
interface CreateHomePageArgs {
  // 应用唯一标识
  appId: string;
  // 首页标题
  title?: string;
  // 首页描述
  description: string;
  // 表单列表
  formList?: Array<{
    name: string;
    formUuid: string;
  }>;
}

/**
 * 创建应用首页工具
 * 用于根据描述快速生成应用首页
 */
export class CreateHomePageTool extends BaseTool {
  name = 'create_home_page';
  nameCn = '创建首页';
  description = '根据应用上下文信息创建应用的首页，自动设计布局、样式和页面结构';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用唯一标识，首页所属的应用',
      },
      title: {
        type: 'string',
        description: '首页标题，根据应用上下文描述自动生成',
      },
      description: {
        type: 'string',
        description: '从PRD中获取的完整应用首页设计的描述，不要有遗漏',
      },
    },
    required: ['appId', 'title', 'description'],
  };

  /**
   * 创建首页
   * @param args 创建首页的参数
   * @returns 创建结果
   */
  @log()
  async execute(args: CreateHomePageArgs): Promise<string> {
    const { appId, description, title } = args;
    try {
      const data = await createPage({
        appType: appId,
        name: title,
      });
      const pageId = data?.pageId;
      if (!pageId) {
        throw new ToolExecutionError(this.name, '界面创建失败，请稍后重试');
      }
      const fileId = 'homepage';
      this.feedbackLogger.file('正在生成首页...', {
        id: fileId,
        type: 'page',
        icon: 'home',
      });

      const appRes = await queryAppContext(appId);

      const formListStr = (appRes.pageList || [])
        .map((page) => `- 名称：${page.name} 访问链接：/${appId}/workbench/${page.pageId}`)
        .join('\n');

      const code = await generatePageCode({
        description: `
我的要求：
* 该页面为首页，需要足够吸引用户，相对炫酷，有质感，色彩丰富且一定要协调
* 内容要尽可能的丰富，如有必要，可以发挥一些图文，加一些banner和hero，让整个首页更加的丰富和完整
* 页面包含应用名称
* 页面中可以增加一些渐变的背景颜色提升页面的调性
* 页面中快捷入口可以考虑通过 Card + Icon + Text 的模式进行展示，内容要居中，保证入口的美观
* 如果有数据信息，可以考虑展示 一些图表丰富页面的展示

${description}

页面需要包含以下页面入口：

${formListStr}
        `,
        currentCode: '',
        isModifyCode: false,
        appContext: {
          ...appRes,
          appId,
        },
      });
      if (code) {
        const schema = await generatePageSchema(pageId, code);
        await saveFormSchema({
          appType: appId,
          formUuid: pageId,
          content: schema,
          schemaVersion: 'V5',
        });
      }
      // 关键产物
      const previewUrl = `//${window.location.host}/${appId}/preview/${pageId}?navConfig.type=none&__disableCache=true`;
      this.feedbackLogger.keyResult({
        type: 'page',
        title,
        url: previewUrl,
        desc: '',
        icon: '',
      });
      // 发送预览地址更新消息
      window.postMessage(
        {
          type: 'previewReady',
          url: previewUrl,
        },
        '*',
      );

      // 将首页移动到第一个位置
      const adjustNavTool = new AdjustAppNavTool();
      await adjustNavTool.execute({
        appId,
        homePageId: pageId,
      });

      // 生成文件
      this.feedbackLogger.file('已完成首页生成 - 预览', {
        id: fileId,
        type: 'page',
        icon: 'home',
        link: previewUrl,
        title,
      });

      // 生成首页创建日志
      const result: any = {
        应用首页创建状态: '✅',
        首页标题: title || '自动生成的标题',
        访问地址: previewUrl,
        访问首页: `[访问首页](${previewUrl})`,
        页面ID: pageId,
        页面详细描述: description,
      };
      // 返回首页创建结果
      return result;
    } catch (error) {
      throw new ToolExecutionError(this.name, `创建首页失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.appId) {
      errors.push('应用唯一标识不能为空');
    }

    if (!args.description) {
      errors.push('页面设计描述不能为空');
    } else if (args.description.length < 10) {
      errors.push('页面设计描述太短，无法生成有意义的首页，请提供更详细的描述');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
