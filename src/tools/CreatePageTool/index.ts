import { createPage } from '@/apis';
import { DEFAULT_APP_TYPE } from '@/config';
import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

// 创建应用的输入参数
interface CreatePageArgs {
  // 页面名称
  name: string;
  // 应用描述
  description: string;
  // 页面ID
  pageId?: string;
}

/**
 * 创建页面工具
 * 用于快速创建自定义页面
 */
export class CreatePageTool extends BaseTool {
  name = 'create_page';
  nameCn = '创建页面';
  description = '创建一个新的页面，包括页面名称、类型等信息';
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: '页面名称',
      },
    },
    required: ['name'],
  };

  /**
   * 创建组件
   * @param args 创建组件的参数
   * @returns 创建结果
   */
  @log()
  async execute(args: CreatePageArgs): Promise<any> {
    try {
      this.logger.info('开始创建页面');
      const { name } = args;
      // 调用API实际创建应用
      const data = await createPage({
        appType: DEFAULT_APP_TYPE,
        name,
      });
      if (!data?.pageId) {
        throw new ToolExecutionError(this.name, '界面创建失败，请稍后重试');
      }

      // 关键产物
      const previewUrl = `//${window.location.host}/${DEFAULT_APP_TYPE}/preview/${data.pageId}?navConfig.type=none&__disableCache=true`;
      this.feedbackLogger.keyResult({
        type: 'page',
        title: name,
        url: previewUrl,
        desc: '',
        icon: '',
      });

      // 生成页面
      const result = {
        status: '✅',
        pageName: name,
        pageId: data.pageId,
      };
      return result;
    } catch (error) {
      this.logger.error(`页面创建失败: ${error}`);
      throw new ToolExecutionError(this.name, `页面创建失败: ${error}`);
    }
  }
  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.name) {
      errors.push('页面名称不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
