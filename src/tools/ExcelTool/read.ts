import { ToolSchema } from '@/types/types';
import { log } from '@/utils/decorators/log';
import { BaseTool } from '@/utils/Tool';
import { ToolValidator } from '@/utils/Validator';
import * as XLSX from 'xlsx';

export class ExcelReaderTool extends BaseTool {
  name = 'excel_reader';
  nameCn = 'Excel读取工具';
  description =
    'Excel读取工具：通过传入Excel文件的URL地址，自动下载并读取文件内容，将第一个工作表内容转换为CSV格式文本返回。适用于需要AI读取和处理Excel文件内容的场景。参数file为Excel文件的URL地址。';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      fileUrl: {
        type: 'string',
        description: '必填，Excel文件的URL地址，支持http/https协议。',
      },
    },
    required: ['fileUrl'],
  };

  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    return ToolValidator.validate(this.parameters, args);
  }

  @log()
  async execute(args: { fileUrl: string }) {
    const { fileUrl } = args;
    const buffer = await fetch(fileUrl, {
      mode: 'cors',
    }).then((res) => res.arrayBuffer());
    const workbook = XLSX.read(buffer);
    if (workbook.SheetNames.length === 0) {
      throw new Error('没有找到工作表');
    }
    const sheetnames = workbook.SheetNames;
    if (sheetnames.length === 0) {
      return [];
    }
    const arr = sheetnames.map((sheetname: any) => {
      const sheet = workbook.Sheets[sheetname];
      const content = XLSX.utils.sheet_to_csv(sheet);
      return {
        sheetname,
        contentType: 'csv',
        content,
      };
    });
    return arr;
  }
}
