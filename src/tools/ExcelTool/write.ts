import { ToolSchema } from '@/types/types';
import { BaseTool } from '@/utils/Tool';
import { PolyMindUploader } from '@/utils/PolymindUploader';
import { ToolValidator } from '@/utils/Validator';

import * as XLSX from 'xlsx';
import { log } from '@/utils/decorators/log';

export class ExcelWriterTool extends BaseTool {
  name = 'excel_writer';
  nameCn = 'Excel写入工具';
  description =
    'Excel写入工具：通过传入CSV格式文本和文件名，自动生成Excel文件并保存到本地。适用于需要AI生成和保存Excel文件的场景。参数content为CSV格式文本，参数filename为文件名。';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      content: {
        type: 'array',
        description: 'CSV格式文本',
        items: {
          type: 'array',
          description: '行数据',
          items: {
            type: 'string',
            description: '列数据',
          },
        },
      },
      filename: {
        type: 'string',
        description: '文件名',
      },
    },
    required: ['content', 'filename'],
  };

  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    return ToolValidator.validate(this.parameters, args);
  }

  @log()
  async execute(args: { content: string[][]; filename: string }) {
    const { content, filename } = args;

    const sheet = XLSX.utils.aoa_to_sheet(content);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
    const u8 = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([u8], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const file = new File([blob], filename, { type: blob.type });
    const uploader = new PolyMindUploader();
    const result = await uploader.uploadFile(file);
    return result;
  }
}
