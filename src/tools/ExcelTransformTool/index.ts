import { BaseTool } from '../../utils/Tool';
import { useAIChatStore } from '@/store/aiChatStore';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { LLMService } from '@/services/llmService';
import { MessageUtils } from '@/utils/Message';
import { ToolExecutionError } from '@/utils/Errors';
import { streamType } from '@/types/types';
import { systemPrompt } from './prompt';
import { log } from '@/utils/decorators/log';

/**
 * Excel数据转换工具参数
 */
interface ExcelTransformToolArgs {
  /**
   * 待处理的Excel文件ID
   */
  fileId: string[];
}

/**
 * Excel数据过滤和格式转换工具
 * 根据Excel数据结构生成数据转换代码
 */
export class ExcelTransformTool extends BaseTool {
  name = 'excel_transform_tool';
  nameCn = 'Excel数据转换';
  description = '一个根据Excel数据结构，生成数据过滤和格式转换的代码的工具';
  parameters = {
    type: 'object',
    properties: {
      fileId: {
        type: 'string',
        description: '待数据转换的excel文件的id',
      },
    },
    required: ['fileId'],
  };

  /**
   * 执行Excel数据转换
   * @param args 转换参数
   * @returns 生成的数据转换代码
   */
  @log()
  async execute(args: ExcelTransformToolArgs): Promise<string> {
    try {
      this.logger.info('开始执行Excel数据转换');
      const { fileId } = args;

      // 获取附件数据
      const { attachments } = useAIChatStore.getState();
      const excelFile = attachments.find((file: any) => file.type === 'file' && fileId === file.id);
      if (!excelFile) throw new ToolExecutionError(this.name, '未找到指定的Excel文件');
      // 获取用户输入
      const { currentExecutingAgent } = useAIAgentStateStore.getState();
      const userMessages = currentExecutingAgent.memory.getAllMessages();
      const firstUserMessage = userMessages.find((msg: any) => msg.role === 'user');
      const userInput = firstUserMessage?.content || '';

      // 创建LLM服务
      const llm = new LLMService();

      // 为每个Excel文件生成转换代码
      this.logger.info(`处理Excel文件: ${excelFile.name}`);
      // 获取文件的元数据
      const metaFileData = excelFile.metaData || '';
      // 构建提示词内容
      const promptContent = `
用户需求: ${userInput}
Excel文件名称: ${excelFile.name}
Excel数据结构摘要(CSV格式前5行):
${metaFileData}

请根据以上Excel数据结构，生成数据过滤和格式转换的Python代码。
`;
      // 构建消息
      const messages = [{ role: 'user', content: promptContent }] as any;
      const systemMsg = [MessageUtils.systemMessage(systemPrompt)];
      // 调用大模型生成代码
      const result = await llm.askStream(messages, systemMsg, [], 'none', (type: streamType, data: any) => {
        if (type === streamType.REASONING) {
          this.feedbackLogger.thinking(data);
        } else if (type === streamType.THINKING) {
          this.feedbackLogger.toolResult(data);
        }
      });
      // 将生成的代码添加到结果
      const finalResult = `${result.content}\n\n ${excelFile.name} 的数据转换代码生成成功。`;
      this.logger.info('Excel数据转换代码生成完成');
      return finalResult;
    } catch (error) {
      this.logger.error(`Excel数据转换失败: ${error}`);
      throw new ToolExecutionError(this.name, `Excel数据转换失败: ${error}`);
    }
  }
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    const { fileId } = args;
    if (!fileId?.length) {
      errors.push('fileId参数不能为空');
    }
    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
