import { ToolExecutionError } from '@/utils/Errors';
import { ToolValidator } from '@/utils/Validator';

import { BaseTool } from '../../utils/Tool';
import { generatePRD } from './prd-gen.share';
import { log } from '@/utils/decorators/log';

// 生成PRD的参数
interface GeneratePRDArgs {
  // 上下文
  context: string;
}

/**
 * 生成PRD工具
 * 使用 deepseek-r1 模型根据应用描述生成详细的PRD文档
 */
export class GenerateBizPRDTool extends BaseTool {
  name = 'generate_prd';
  nameCn = '生成PRD';
  description = '根据上下文中用户应用需求描述，生成 Yida 应用简单的 PRD 文档。';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      context: {
        type: 'string',
        description:
          '用户需求描述的上下文，请务必逐字逐句的记录用户的需求描述（不要遗漏任何细节，也不要曲解，不要加工，直接原方不动的传递进入）',
      },
    },
    required: ['context'],
  };

  /**
   * 生成PRD文档
   * @param args 生成PRD的参数
   * @returns 生成的PRD文档
   */
  @log()
  async execute(args: GeneratePRDArgs): Promise<string> {
    try {
      this.logger.info('开始生成PRD文档');
      const prd = await generatePRD({
        context: args.context,
        onThinking: (thinking: string) => {
          this.feedbackLogger.thinking(thinking);
        },
        onResult: (answer: string) => {
          this.feedbackLogger.toolResult(answer);
        },
        onError: (error: string) => {
          this.logger.error(`PRD 文档生成失败: ${error}`);
          throw new ToolExecutionError(this.name, `PRD文档生成失败: ${error}`);
        },
      });
      this.logger.info('PRD 文档生成成功');
      return prd.content;
    } catch (error) {
      this.logger.error(`PRD 文档生成失败: ${error}`);
      throw new ToolExecutionError(this.name, `PRD文档生成失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.context) {
      errors.push('需求描述不能为空');
    }
    return ToolValidator.validate(this.parameters, args);
  }
}
