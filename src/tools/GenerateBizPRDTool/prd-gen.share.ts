import { once, throttle } from 'lodash';

import { LLMService } from '@/services/llmService';
import { mockService } from '@/services/mockService';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { LLMResponse, streamType } from '@/types/types';
import { MessageUtils } from '@/utils/Message';
import { FIELD_INFO } from '@/utils/YidaMeta';

const _PRDCache = new Map<string, any>();

export type GenerateAppComplexityLevel = 'easy' | 'medium' | 'hard';

export const generatePRD = async (args: {
  context: string;
  complexity?: GenerateAppComplexityLevel;
  onThinking?: (thinking: string) => void;
  onResult?: (answer: string) => void;
  onError?: (error: string) => void;
}): Promise<LLMResponse> => {
  const { context } = args;

  if (mockService.isEnabled) {
    return {
      thinking: 'mocked',
      content: await mockService.mockGeneratePlan(),
    };
  }

  if (_PRDCache.has(context)) {
    return _PRDCache.get(context);
  }

  const messages = [{ role: 'user', content: context }] as any;

  const fieldDesc = Object.entries(FIELD_INFO)
    .map(([key, value]) => `- ${key}：${value.desc}`)
    .join('\n');

  let formDesignComplexityDesc = '';

  if (args.complexity === 'easy') {
    formDesignComplexityDesc = '**合理的表单（元数据表），最少的关联，最简化的字段实现必要的用户诉求和功能**';
  } else if (args.complexity === 'medium') {
    formDesignComplexityDesc =
      '**合理的表单（元数据表），必要的关联，中等复杂度，贴近钉钉 Yida 低代码平台可以生产的生产级应用**';
  } else if (args.complexity === 'hard') {
    formDesignComplexityDesc =
      '**复杂且深入行业领域的表单（元数据表），必要的关联，复杂的业务逻辑和功能设计 & 实现，接近专业的 Salesforce 或者 SAP 级别的应用**';
  }

  let customPageLimitationDesc = '';

  if (args.complexity === 'easy') {
    customPageLimitationDesc =
      '**不需要创建自定义页面，控制复杂度，优先使用宜搭的低代码表单能力，并配合一个聚合主要页面的链接和基础数据功能的「Portal」页面即可**';
  } else if (args.complexity === 'medium') {
    customPageLimitationDesc = `- *可以创建必要的 Application Page（至多 3 个），但优先使用宜搭的低代码表单能力**
  - **禁止创建纯粹的数据录入型自定义页面**，优先使用宜搭的低代码表单能力，**Application Page** 优先实现必要的业务逻辑和功能，切勿创建纯粹为了提交数据的表单页面
  - **禁止创建数据管理型页面**，宜搭可以根据表单，自动化生成
  `;
  } else if (args.complexity === 'hard') {
    customPageLimitationDesc =
      '**可以创建必要的自定义页面，优先自定义页面来表达多表，和特定重前端的功能复合模块、多数据关联的复杂应用场景**';
  }

  let appComplexityDesc = '';

  if (args.complexity === 'easy') {
    appComplexityDesc = '基础最简单的应用';
  } else if (args.complexity === 'medium') {
    appComplexityDesc = '常规低代码应用';
  } else if (args.complexity === 'hard') {
    appComplexityDesc = '复杂企业级低代码应用';
  }

  // 构建提示词
  const systemPrompt = `{{render "tools.prd-generate"}}`;
  const systemMsg = [MessageUtils.systemMessage(systemPrompt)];
  // 调用 deepseek-r1 模型
  const llm = new LLMService({
    model: 'deepseek-reasoner',
    // model: 'qwen3-235b-a22b',
  });
  const artifactStoreState = useAIArtifactStateStore.getState();
  const throttledSetPRDArtifactContent = throttle((content: string) => {
    artifactStoreState.setPrdContent(content);
  }, 1200);
  const setArtifactModeOnce = once(() => artifactStoreState.setArtifactMode('prd'));
  try {
    artifactStoreState.setPRDInGenerating(true);
    const prd = await llm
      .askStream<{
        appComplexityDesc: string;
        formDesignComplexityDesc: string;
        customPageLimitationDesc: string;
        fieldDesc: string;
      }>(
        messages,
        systemMsg,
        [],
        'none',
        (type: streamType, data: any) => {
          if (type === streamType.REASONING && args.onThinking) {
            args.onThinking(data);
          } else if (type === streamType.THINKING && args.onResult) {
            args.onResult(data);
            setArtifactModeOnce();
            throttledSetPRDArtifactContent(data);
          }
        },
        false,
        {
          context: {
            appComplexityDesc,
            formDesignComplexityDesc,
            customPageLimitationDesc,
            fieldDesc: fieldDesc,
          },
        },
      )
      .finally(() => {
        artifactStoreState.setPRDInGenerating(false);
      });
    await artifactStoreState.onPRDReady();
    // use the latest prd content state
    const userConfirmedPRDContent = useAIArtifactStateStore.getState().prdContent || prd.content;
    args.onResult?.(userConfirmedPRDContent);
    useAIAgentStateStore.getState().setGeneralPRD(userConfirmedPRDContent);
    _PRDCache.set(context, userConfirmedPRDContent);
    return {
      content: userConfirmedPRDContent,
      thinking: prd.thinking,
    };
  } catch (e) {
    if (args.onError) {
      args.onError(e);
    }
  }
  return {
    content: '',
    thinking: '',
  };
};
