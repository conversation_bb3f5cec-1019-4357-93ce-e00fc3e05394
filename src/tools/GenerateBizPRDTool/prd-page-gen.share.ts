import { LLMService } from '@/services/llmService';
import { mockService } from '@/services/mockService';
import { MessageUtils } from '@/utils/Message';

import { streamType, type LLMResponse } from '@/types/types';
import { mockGenerateComplexHelloWorldPage } from '@/mocks/mockComplexPageHelloWorldPRD';
const _PRDCache = new Map<string, any>();

export const generateComplexPagePRD = async (args: {
  context: string;
  onThinking?: (thinking: string) => void;
  onResult?: (answer: string) => void;
  onError?: (error: string) => void;
}): Promise<LLMResponse> => {
  const { context } = args;

  if (_PRDCache.has(context)) {
    return _PRDCache.get(context);
  }

  const messages = [{ role: 'user', content: context }] as any;

  // 构建提示词
  const systemPrompt = `## 角色

  请根据用户的应用需求描述，请生成一份结构清晰、内容精要的页面说明文档，用于让代码生成器生成组件。

  ## 上下文

  - 现在时间是: ${new Date().toLocaleDateString()}

  ## 要求

  - 深度分析需求，识别核心功能，不要发散，简述核心系统的能力要求，分条陈列
  - 根据功能需求将页面拆解为 N 个 **React** 组件构成，组件之间相互独立，**不支持嵌套（不可以以 children 或者 props 的方式嵌入，主要实现平铺，相互独立）**，后续可以被 Page 通过属性，简单组装在一起，给出每一个组件的：功能（要求详细）、属性（支持传递的属性，可以由 Typescript 描述）
  - !!!组件的实现应该保持「独立性」，不要依赖于其他组件的实现细节，也不存在对别的组件的依赖!!!
  - !!!不关注技术细节，不要给任何出技术方案，避免过度设计!!! 只描述功能需求
  - 创建的页面是在 Yida 的 APP 框架之中，因此：
    - 不需要设计导航、路由、权限等平台已有的能力，聚焦在表单和页面的内容本身的设计上
    - 不需要创建和设计后台类的数据管理页面，宜搭可以自动化生成
    - 不需要创建和设计报表类的页面，宜搭可以自动化生成
    - 不需要生成详情页面的设计，数据条目的详情页面宜搭可以自动化生成
  - 创建的**表单页面建议优先作为数据的填写页面**，如果没有额外的要求，可以不创建输入录入页面，让输入录入尽量复用宜搭的低代码表单的能力，只有部分关联能力的页面才需要创建输入录入页面而避免直接录入

  ## 输出结构

  !!!请务必严格按照下面的格式生成 PRD，创建类的和修改类的使用完全不同的格式输出!!!

  创建 PRD：

  XXX 页面说明：

  页面名称

  页面功能描述

  页面组成（组件表）

  > 要求规范化设计 React 组件的熟属性
  > 若需要 page 层传递数据，尤其需要设计好属性。
  > 重点要将组件的功能描述清晰，便于下一步的升级生成。

  * 组件名称
  * 功能简述
  * 组件属性
    * 直接用 typescript interface 描述：名称、类型、说明（默认值、规则和用法等等）
    * 用 Typescript 接口说明，必须 **给出完整的声明**，切记不可漏掉任何一个属性和细节，否则会导致代码生成失败
  * 其它要求

  其它备忘

  * 简述页面和组件的平铺关系，不要嵌套
  `;
  const systemMsg = [MessageUtils.systemMessage(systemPrompt)];
  return mockService.mock(async () => {
    // 调用 deepseek-r1 模型
    const llm = new LLMService({
      model: 'deepseek-reasoner',
      // model: 'qwen3-235b-a22b',
    });
    try {
      const prd = await llm.askStream(messages, systemMsg, [], 'none', (type: streamType, data: any) => {
        if (type === streamType.REASONING && args.onThinking) {
          args.onThinking(data);
        } else if (type === streamType.THINKING && args.onResult) {
          args.onResult(data);
        }
      });
      _PRDCache.set(context, prd);
      return prd;
    } catch (e) {
      if (args.onError) {
        args.onError(e);
      }
    }
    return {
      content: '',
      thinking: '',
      error: '',
    };
  }, mockGenerateComplexHelloWorldPage);
};
