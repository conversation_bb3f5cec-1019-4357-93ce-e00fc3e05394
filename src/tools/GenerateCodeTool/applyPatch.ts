/**
 * Attempts a line-trimmed fallback match for the given search content in the original content.
 * It tries to match `searchContent` lines against a block of lines in `originalContent` starting
 * from `lastProcessedIndex`. Lines are matched by trimming leading/trailing whitespace and ensuring
 * they are identical afterwards.
 *
 * Returns [matchIndexStart, matchIndexEnd] if found, or false if not found.
 */
function lineTrimmedFallbackMatch(
  originalContent: string,
  searchContent: string,
  startIndex: number,
): [number, number] | false {
  // Split both contents into lines
  const originalLines = originalContent.split('\n');
  const searchLines = searchContent.split('\n');

  // Trim trailing empty line if exists (from the trailing \n in searchContent)
  if (searchLines[searchLines.length - 1] === '') {
    searchLines.pop();
  }

  // Find the line number where startIndex falls
  let startLineNum = 0;
  let currentIndex = 0;
  while (currentIndex < startIndex && startLineNum < originalLines.length) {
    currentIndex += originalLines[startLineNum].length + 1; // +1 for \n
    startLineNum++;
  }

  // For each possible starting position in original content
  for (let i = startLineNum; i <= originalLines.length - searchLines.length; i++) {
    let matches = true;

    // Try to match all search lines from this position
    for (let j = 0; j < searchLines.length; j++) {
      const originalTrimmed = originalLines[i + j].trim();
      const searchTrimmed = searchLines[j].trim();

      if (originalTrimmed !== searchTrimmed) {
        matches = false;
        break;
      }
    }

    // If we found a match, calculate the exact character positions
    if (matches) {
      // Find start character index
      let matchStartIndex = 0;
      for (let k = 0; k < i; k++) {
        matchStartIndex += originalLines[k].length + 1; // +1 for \n
      }

      // Find end character index
      let matchEndIndex = matchStartIndex;
      for (let k = 0; k < searchLines.length; k++) {
        matchEndIndex += originalLines[i + k].length + 1; // +1 for \n
      }

      return [matchStartIndex, matchEndIndex];
    }
  }

  return false;
}

/**
 * Attempts to match blocks of code by using the first and last lines as anchors.
 * This is a third-tier fallback strategy that helps match blocks where we can identify
 * the correct location by matching the beginning and end, even if the exact content
 * differs slightly.
 *
 * The matching strategy:
 * 1. Only attempts to match blocks of 3 or more lines to avoid false positives
 * 2. Extracts from the search content:
 *    - First line as the "start anchor"
 *    - Last line as the "end anchor"
 * 3. For each position in the original content:
 *    - Checks if the next line matches the start anchor
 *    - If it does, jumps ahead by the search block size
 *    - Checks if that line matches the end anchor
 *    - All comparisons are done after trimming whitespace
 *
 * This approach is particularly useful for matching blocks of code where:
 * - The exact content might have minor differences
 * - The beginning and end of the block are distinctive enough to serve as anchors
 * - The overall structure (number of lines) remains the same
 *
 * @param originalContent - The full content of the original file
 * @param searchContent - The content we're trying to find in the original file
 * @param startIndex - The character index in originalContent where to start searching
 * @returns A tuple of [startIndex, endIndex] if a match is found, false otherwise
 */
function blockAnchorFallbackMatch(
  originalContent: string,
  searchContent: string,
  startIndex: number,
): [number, number] | false {
  const originalLines = originalContent.split('\n');
  const searchLines = searchContent.split('\n');

  // Only use this approach for blocks of 3+ lines
  if (searchLines.length < 3) {
    return false;
  }

  // Trim trailing empty line if exists
  if (searchLines[searchLines.length - 1] === '') {
    searchLines.pop();
  }

  const firstLineSearch = searchLines[0].trim();
  const lastLineSearch = searchLines[searchLines.length - 1].trim();
  const searchBlockSize = searchLines.length;

  // Find the line number where startIndex falls
  let startLineNum = 0;
  let currentIndex = 0;
  while (currentIndex < startIndex && startLineNum < originalLines.length) {
    currentIndex += originalLines[startLineNum].length + 1;
    startLineNum++;
  }

  // Look for matching start and end anchors
  for (let i = startLineNum; i <= originalLines.length - searchBlockSize; i++) {
    // Check if first line matches
    if (originalLines[i].trim() !== firstLineSearch) {
      continue;
    }

    // Check if last line matches at the expected position
    if (originalLines[i + searchBlockSize - 1].trim() !== lastLineSearch) {
      continue;
    }

    // Calculate exact character positions
    let matchStartIndex = 0;
    for (let k = 0; k < i; k++) {
      matchStartIndex += originalLines[k].length + 1;
    }

    let matchEndIndex = matchStartIndex;
    for (let k = 0; k < searchBlockSize; k++) {
      matchEndIndex += originalLines[i + k].length + 1;
    }

    return [matchStartIndex, matchEndIndex];
  }

  return false;
}

/**
 * This function reconstructs the file content by applying a streamed diff (in a
 * specialized SEARCH/REPLACE block format) to the original file content. It is designed
 * to handle both incremental updates and the final resulting file after all chunks have
 * been processed.
 *
 * The diff format is a custom structure that uses three markers to define changes:
 *
 *   <<<<<<< SEARCH
 *   [Exact content to find in the original file]
 *   =======
 *   [Content to replace with]
 *   >>>>>>> REPLACE
 *
 * Behavior and Assumptions:
 * 1. The file is processed chunk-by-chunk. Each chunk of `diffContent` may contain
 *    partial or complete SEARCH/REPLACE blocks. By calling this function with each
 *    incremental chunk (with `isFinal` indicating the last chunk), the final reconstructed
 *    file content is produced.
 *
 * 2. Matching Strategy (in order of attempt):
 *    a. Exact Match: First attempts to find the exact SEARCH block text in the original file
 *    b. Line-Trimmed Match: Falls back to line-by-line comparison ignoring leading/trailing whitespace
 *    c. Block Anchor Match: For blocks of 3+ lines, tries to match using first/last lines as anchors
 *    If all matching strategies fail, an error is thrown.
 *
 * 3. Empty SEARCH Section:
 *    - If SEARCH is empty and the original file is empty, this indicates creating a new file
 *      (pure insertion).
 *    - If SEARCH is empty and the original file is not empty, this indicates a complete
 *      file replacement (the entire original content is considered matched and replaced).
 *
 * 4. Applying Changes:
 *    - Before encountering the "=======" marker, lines are accumulated as search content.
 *    - After "=======" and before ">>>>>>> REPLACE", lines are accumulated as replacement content.
 *    - Once the block is complete (">>>>>>> REPLACE"), the matched section in the original
 *      file is replaced with the accumulated replacement lines, and the position in the original
 *      file is advanced.
 *
 * 5. Incremental Output:
 *    - As soon as the match location is found and we are in the REPLACE section, each new
 *      replacement line is appended to the result so that partial updates can be viewed
 *      incrementally.
 *
 * 6. Partial Markers:
 *    - If the final line of the chunk looks like it might be part of a marker but is not one
 *      of the known markers, it is removed. This prevents incomplete or partial markers
 *      from corrupting the output.
 *
 * 7. Finalization:
 *    - Once all chunks have been processed (when `isFinal` is true), any remaining original
 *      content after the last replaced section is appended to the result.
 *    - Trailing newlines are not forcibly added. The code tries to output exactly what is specified.
 *
 * Errors:
 * - If the search block cannot be matched using any of the available matching strategies,
 *   an error is thrown.
 */
export function applyDiff(diffContent: string, originalContent: string, isFinal: boolean): string {
  let result = '';
  let lastProcessedIndex = 0;

  let currentSearchContent = '';
  let currentReplaceContent = '';
  let inSearch = false;
  let inReplace = false;

  let searchMatchIndex = -1;
  let searchEndIndex = -1;

  let lines = diffContent.split('\n');

  // If the last line looks like a partial marker but isn't recognized,
  // remove it because it might be incomplete.
  const lastLine = lines[lines.length - 1];
  if (
    lines.length > 0 &&
    (lastLine.startsWith('<') || lastLine.startsWith('=') || lastLine.startsWith('>')) &&
    lastLine !== '<<<<<<< SEARCH' &&
    lastLine !== '=======' &&
    lastLine !== '>>>>>>> REPLACE'
  ) {
    lines.pop();
  }

  for (const line of lines) {
    if (line === '<<<<<<< SEARCH') {
      inSearch = true;
      currentSearchContent = '';
      currentReplaceContent = '';
      continue;
    }

    if (line === '=======') {
      inSearch = false;
      inReplace = true;

      // Remove trailing linebreak for adding the === marker
      // if (currentSearchContent.endsWith("\r\n")) {
      // 	currentSearchContent = currentSearchContent.slice(0, -2)
      // } else if (currentSearchContent.endsWith("\n")) {
      // 	currentSearchContent = currentSearchContent.slice(0, -1)
      // }

      if (!currentSearchContent) {
        // Empty search block
        if (originalContent.length === 0) {
          // New file scenario: nothing to match, just start inserting
          searchMatchIndex = 0;
          searchEndIndex = 0;
        } else {
          // Complete file replacement scenario: treat the entire file as matched
          searchMatchIndex = 0;
          searchEndIndex = originalContent.length;
        }
      } else {
        // Add check for inefficient full-file search
        // if (currentSearchContent.trim() === originalContent.trim()) {
        // 	throw new Error(
        // 		"The SEARCH block contains the entire file content. Please either:\n" +
        // 			"1. Use an empty SEARCH block to replace the entire file, or\n" +
        // 			"2. Make focused changes to specific parts of the file that need modification.",
        // 	)
        // }

        // Exact search match scenario
        const exactIndex = originalContent.indexOf(currentSearchContent, lastProcessedIndex);
        if (exactIndex !== -1) {
          searchMatchIndex = exactIndex;
          searchEndIndex = exactIndex + currentSearchContent.length;
        } else {
          // Attempt fallback line-trimmed matching
          const lineMatch = lineTrimmedFallbackMatch(originalContent, currentSearchContent, lastProcessedIndex);
          if (lineMatch) {
            [searchMatchIndex, searchEndIndex] = lineMatch;
          } else {
            // Try block anchor fallback for larger blocks
            const blockMatch = blockAnchorFallbackMatch(originalContent, currentSearchContent, lastProcessedIndex);
            if (blockMatch) {
              [searchMatchIndex, searchEndIndex] = blockMatch;
            } else {
              throw new Error(
                `The SEARCH block:\n${currentSearchContent.trimEnd()}\n...does not match anything in the file.`,
              );
            }
          }
        }
      }

      // Output everything up to the match location
      result += originalContent.slice(lastProcessedIndex, searchMatchIndex);
      continue;
    }

    if (line === '>>>>>>> REPLACE') {
      // Finished one replace block

      // // Remove the artificially added linebreak in the last line of the REPLACE block
      // if (result.endsWith("\r\n")) {
      // 	result = result.slice(0, -2)
      // } else if (result.endsWith("\n")) {
      // 	result = result.slice(0, -1)
      // }

      // Advance lastProcessedIndex to after the matched section
      lastProcessedIndex = searchEndIndex;

      // Reset for next block
      inSearch = false;
      inReplace = false;
      currentSearchContent = '';
      currentReplaceContent = '';
      searchMatchIndex = -1;
      searchEndIndex = -1;
      continue;
    }

    // Accumulate content for search or replace
    // (currentReplaceContent is not being used for anything right now since we directly append to result.)
    // (We artificially add a linebreak since we split on \n at the beginning. In order to not include a trailing linebreak in the final search/result blocks we need to remove it before using them. This allows for partial line matches to be correctly identified.)
    // NOTE: search/replace blocks must be arranged in the order they appear in the file due to how we build the content using lastProcessedIndex. We also cannot strip the trailing newline since for non-partial lines it would remove the linebreak from the original content. (If we remove end linebreak from search, then we'd also have to remove it from replace but we can't know if it's a partial line or not since the model may be using the line break to indicate the end of the block rather than as part of the search content.) We require the model to output full lines in order for our fallbacks to work as well.
    if (inSearch) {
      currentSearchContent += line + '\n';
    } else if (inReplace) {
      currentReplaceContent += line + '\n';
      // Output replacement lines immediately if we know the insertion point
      if (searchMatchIndex !== -1) {
        result += line + '\n';
      }
    }
  }

  // If this is the final chunk, append any remaining original content
  if (isFinal && lastProcessedIndex < originalContent.length) {
    result += originalContent.slice(lastProcessedIndex);
  }

  return result;
}
