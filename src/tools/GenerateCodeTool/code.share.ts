/**
Portal Pages
Purpose: Entry points that aggregate content from different sources
Examples: Corporate homepages, intranet landing pages, content hubs
Characteristics: Content aggregation, navigation-focused, personalization options

Workbench/Admin Dashboards
Purpose: Operational interfaces for managing data/content
Examples: Content management systems, admin panels, back-office applications
Characteristics: CRUD operations, data tables, form-heavy interfaces

Analytical Dashboards
Purpose: Data visualization and analysis
Examples: Business intelligence tools, performance monitoring, reporting systems
Characteristics: Charts, KPIs, interactive visualizations, data filters

Application Pages
Purpose: Task-focused interfaces with specialized functionality
Examples: Document editors, design tools, project management apps
Characteristics: Rich interactions, specialized toolbars, focused workflows

Landing Pages
Purpose: Conversion-focused standalone pages
Examples: Marketing campaigns, product launches, lead generation pages
Characteristics: Clear CTAs, minimal navigation, focused messaging

Catalog/Directory Pages
Purpose: Organizing and browsing collections of items
Examples: E-commerce product listings, document libraries, media galleries
Characteristics: Filtering, sorting, grid/list views, search functionality

Detail/Entity Pages
Purpose: In-depth information about a specific item
Examples: Product details, user profiles, article pages
Characteristics: Rich media, specifications, related items, actions

Wizard/Flow Pages
Purpose: Guide users through multi-step processes
Examples: Checkout processes, onboarding flows, setup wizards
Characteristics: Progress indicators, sequential navigation, form validation
*/
export type GeneratedPageType = 'portal' | 'dashboard' | 'application' | 'landing' | 'catalog' | 'detail';
