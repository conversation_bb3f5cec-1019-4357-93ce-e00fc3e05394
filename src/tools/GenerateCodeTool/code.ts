import { Parser } from 'commonmark';
import { findLastIndex, once } from 'lodash';

import { CodeService } from '@/services/codeService';
import { mockService } from '@/services/mockService';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { useAIChatStore } from '@/store/aiChatStore';
import { useAISettingStore, type IMessage } from '@/store/aiSettingStore';
import { streamType } from '@/types/types';
import { stringifyObjectSafe } from '@/utils/LangUtils';
import { cacheFetch, getLocal } from '@ali/yc-utils';

import { applyDiff } from './applyPatch';
import { BasicSystemPromptOptions, USER_PROMPT } from './prompt';

import type { ComponentArtifactType } from '../CreateComponentsTool';
import type { GeneratedPageType } from './code.share';
import type { ChatMessage } from '@/utils/ChatManager';
import type { UserFeedbackLogger } from '@/utils/UserFeedbackLogger';
import type { IPromptBuilderContext } from '@/services/llmService';

export * from './schema';

export const AVAILABLE_MODULES = [
  'react',
  'react-dom',
  'react-router-dom',
  'antd',
  'ahooks',
  'd3',
  'lodash',
  'dayjs',
  '@ant-design/icons',
  'recharts',
  'lucide-react',
  'yida-plugin-markdown',
  '@radix-ui/themes',
  'framer-motion',
  '@antv/g2',
  'yida-api',
];

export function extractCode(content: string, type?: 'jsx' | 'tsx' | 'json' | 'js' | 'diff') {
  const parser = new Parser();
  const ast = parser.parse(content);

  const walker = ast.walker();
  const codeBlocks = [];

  let walkEvent = walker.next();
  while (walkEvent) {
    const node = walkEvent.node;
    if (node.type === 'code_block' && (node.info === type || !type)) {
      codeBlocks.push({
        type: node.info,
        content: node.literal,
      });
    }
    walkEvent = walker.next();
  }
  return codeBlocks;
}

export function extractPageCode(result: string, currentCode: string) {
  const codeList = extractCode(result);
  const diffList = codeList.filter((item) => item.type === 'diff');
  const fullJSXCode = codeList.find((item) => item.type === 'jsx');
  const fullHTMLCode = codeList.find((item) => item.type === 'html');
  const agentType = useAIChatStore.getState().agentType;
  let newCode = currentCode;
  if (fullHTMLCode) {
    if (agentType === 'page-print') {
      newCode = fullHTMLCode.content;
    }
  }
  if (fullJSXCode) {
    newCode = fullJSXCode.content;
  } else if (diffList.length > 0 && currentCode) {
    diffList.forEach(({ content }) => {
      if (content) {
        newCode = applyDiff(content, newCode, true);
      }
    });
  }
  // console.log('code result', newCode);
  return newCode || result || currentCode;
}

export interface GenerateCodeContext {
  currentCode: string;
  appContext?: {
    pages?: any[];
    appId?: string;
    /**
     * 跳转页面列表
     */
    pageUrlList?: string;
  };
  onStreamFun?: any;
  feedbackLogger?: UserFeedbackLogger;
  systemPromptKey?: 'yida_manus_prompt' | 'yida_manus_component_prompt';
}

export type GenerateCodeOptions = GeneratePageOptions & GenerateComponentOptions;

interface GeneratePageOptions extends GenerateCodeContext {
  description: string;
  pageType?: GeneratedPageType;
  components?: ComponentArtifactType[];
  isModifyCode?: boolean;
}

interface GenerateComponentOptions extends GenerateCodeContext {
  description: string;
  fileName?: string;
}

function getDataSourceInfo() {
  const currentAgent = useAIAgentStateStore.getState().currentExecutingAgent;
  const agentStore = useAIAgentStateStore.getState();
  if (agentStore.currentAppCubeInfo && agentStore.currentPageDataInterfaceInfo) {
    return {
      allCubesInfo: stringifyObjectSafe(agentStore.currentAppCubeInfo),
      allAPIsInfo: stringifyObjectSafe(agentStore.currentPageDataInterfaceInfo),
    };
  }
  const allCubesInfo =
    currentAgent?.memory
      ?.getAllMessages?.()
      ?.filter((m) => m.name === 'get_cubes_info')
      ?.map((m) => m.content)
      ?.join('\n') || '';
  const allAPIsInfo =
    currentAgent?.memory
      ?.getAllMessages?.()
      ?.filter((m) => m.name === 'generate_data_interface')
      ?.map((m) => m.content)
      ?.join('\n') || '';

  return { allCubesInfo, allAPIsInfo };
}

function checkCode(code: string): boolean {
  const strArr = code.split('\n');
  const importReg = /^import (.*?) from [\'|\"](.*?)[\'|\"];/;
  // 当输出内容超过30行时，判断import内容内容是否合法，如果不合法，则直接abort，避免浪费token
  if (strArr.length === 30) {
    const isValid = strArr.every((item: string) => {
      const moduleName = importReg.exec(item)?.[2];
      if (moduleName && !AVAILABLE_MODULES.includes(moduleName) && !moduleName.startsWith('.')) {
        return false;
      }
      return true;
    });
    return isValid;
  }
  return true;
}

function transformCode(code: string): string {
  // 解决变量名冲突问题
  const newCode = code.replace(/(\W+)YidaComp\s*=\s*YidaComp(\W+|$)/, '$1_YidaComp = YidaComp$2');

  return newCode;
}

async function generateCode(options: GenerateCodeOptions) {
  const { description, currentCode = '', onStreamFun, appContext } = options;
  const { allAPIsInfo, allCubesInfo } = getDataSourceInfo();
  // const scenarioPrompt =
  //   getLocal(options?.systemPromptKey || 'yida_manus_prompt') || getDefaultYidaPageScenarioPrompt();
  // const scenarioPrompt =
  //   options.pageType === 'portal' ? getDefaultPortalScenarioPrompt() : getDefaultApplicationScenarioPrompt();
  // const isGeneratePage = options.systemPromptKey === 'yida_manus_prompt';
  // const fileName = options?.fileName;
  const sharedContext: BasicSystemPromptOptions = {
    scenarioContext: '',
    technologyDependencyContext: '',
    isDynamicDataSource: true,
    appContext: appContext,
    enableYidaComponents: false,
    pageContext: {
      basicInfo: `Custom code generated page.`,
      cubeInfo: allCubesInfo,
      dataSourceAPIInfo: allAPIsInfo,
    },
  };
  console.info('🧾 final sharedContext: ', sharedContext);
  const uiFramework = useAISettingStore.getState().uiFramework;
  const generateSysPromptCtx = {
    ...sharedContext,
    components: options?.components,
    appContext: JSON.stringify(appContext),
    enableAntd: uiFramework === 'antd',
    enableRadixUI: uiFramework === 'radix-ui',
    isModifyCode: options.isModifyCode,
    scenario: {
      normal: false,
      portal: options.pageType === 'portal',
      application: options.pageType === 'application',
    },
  } as IPromptBuilderContext;
  const systemPrompt = '{{render "code-gen.code-gen-entry"}}';
  const finalUserPrompt = USER_PROMPT({ instruction: description, existedCode: currentCode });
  console.info('🦄 final [systemPrompt]: ', systemPrompt);
  console.info('🦄 final [userPrompt]: ', finalUserPrompt);
  useAIArtifactStateStore.getState().setIsCodeGenerated(false);
  const settings = useAISettingStore.getState();
  const selectedContextNodeMsgIdx = findLastIndex(
    useAIChatStore.getState().messages || [],
    (msg: ChatMessage) => msg.isSpecialSelectNodeMessage === 'true',
  );
  const modifiedInfoMsgIdx = findLastIndex(
    useAIChatStore.getState().messages || [],
    (msg: ChatMessage) => msg.isSpecialEditMessage === 'true',
  );
  const selectedContextNodeMsg = useAIChatStore.getState().messages?.[selectedContextNodeMsgIdx];
  const modifiedInfoMsg = useAIChatStore.getState().messages?.[modifiedInfoMsgIdx];
  const messages: IMessage[] = [
    {
      role: 'system',
      content: systemPrompt,
    },
    {
      role: 'user',
      content: finalUserPrompt,
    },
  ];
  if (selectedContextNodeMsg && selectedContextNodeMsgIdx > modifiedInfoMsgIdx) {
    messages.push({
      role: 'user',
      content: (selectedContextNodeMsg?.content as string) || '',
    });
  }
  if (modifiedInfoMsg && modifiedInfoMsgIdx > selectedContextNodeMsgIdx) {
    messages.push({
      role: 'user',
      content: (modifiedInfoMsg?.content as string) || '',
    });
  }
  const setArtifactCodeModeOnce = once(() => useAIArtifactStateStore.getState().setArtifactMode('code'));

  const service = new CodeService(
    {
      model: settings.model,
      temperature: parseFloat(settings.temperature || '1'),
    },
    getLocal('yida_manus_sk') || 'sk-6a2de6a7f72b4da0968ba3f20cf685d4',
  );

  const systemMsg: any[] = [];
  const msgs: any[] = [];
  let systemFlag = false;
  messages.forEach((item) => {
    if (item.role === 'system') {
      systemFlag = true;
      systemMsg.push(item);
    } else {
      msgs.push(item);
    }
  });

  const result = await service.askStream<IPromptBuilderContext>(
    msgs,
    systemMsg,
    [],
    'none',
    (type: streamType, full: any, abort) => {
      // const thinking = type === streamType.THINKING ? full : null;
      const isValid = checkCode(full);
      if (!isValid) {
        abort();
        throw new Error('the code is invalid');
      }
      onStreamFun?.(full);
      // if (thinking && options.feedbackLogger) {
      //   options.feedbackLogger.thinking(thinking);
      // }
      if (!full) {
        return;
      }
      setArtifactCodeModeOnce();
      if (options.systemPromptKey !== 'yida_manus_component_prompt') {
        useAIArtifactStateStore.getState().setCode(full);
      } else {
        useAIArtifactStateStore.getState().setComponentCode((options as GenerateComponentOptions)?.fileName, full);
      }
    },
    false,
    {
      context: generateSysPromptCtx,
      // CodeGen 对于代码生成的复杂任务配置启动 thinking / qwen3
      enableThinking: true,
    },
  );

  console.info('🧾 AI generated result: ', result);
  const extractedCode = extractPageCode(result.content, currentCode);
  console.info('🧾 extractedCode: ', extractedCode);
  return transformCode(extractedCode);
}

export async function generatePageCode(options: GenerateCodeOptions) {
  return mockService.mock(async () =>
    generateCode({
      ...options,
      systemPromptKey: 'yida_manus_prompt',
    }),
  );
}

export async function generateComponentCode(options: GenerateCodeOptions) {
  return generateCode({
    ...options,
    currentCode: '',
    systemPromptKey: 'yida_manus_component_prompt',
  });
}

/**
 * 获取代码片段
 * @param prompt 代码片段
 */
export async function retrieveCodeInfo(
  prompt: string,
  type: 'template' | 'stylePrompt' | 'scenarioPrompt' | 'component',
): Promise<string> {
  const FIELD_MAP = {
    template: 'textareaField_m8z8ux5v',
    stylePrompt: 'textareaField_m8yaqgn0',
    scenarioPrompt: 'textareaField_m8yaxuo6',
    component: 'textareaField_m96owz32',
  };
  try {
    const res = await cacheFetch(
      {
        url: '/query/doc2bot/retrievalCodeInfo.json',
        data: {
          type,
          keyword: prompt,
          limit: 1,
        },
      },
      true,
    );
    const resourcePath = res?.[0]?.dentryId;
    if (!resourcePath) return '';
    // 查询宜搭数据
    const instData = await cacheFetch(
      {
        url: '/query/loginFreeFormData/listFormDataByType.json',
        data: {
          type: `codeAgent_${type}`,
          searchFieldJson: JSON.stringify({
            textField_m8zkx5yb: resourcePath.replace(`/yida/codeAgent/${type}/`, ''),
          }),
        },
      },
      true,
    );
    return instData?.data?.[0]?.[FIELD_MAP[type]] || '';
  } catch (err) {
    return '';
  }
}
