import { createPage, deleteFormSchema, getFormSchema, saveFormSchema } from '@/apis';
import { DEFAULT_APP_TYPE } from '@/config';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { isEnableCodeGenPRDContent, useAISettingStore } from '@/store/aiSettingStore';
import { AdjustAppNavTool } from '@/tools/AdjustAppNavTool';
import { ToolExecutionError } from '@/utils/Errors';
import { stringifyObjectSafe } from '@/utils/LangUtils';
import { BaseTool } from '@/utils/Tool';
import { queryAppContext } from '@/utils/YidaMeta';
/* eslint-disable no-await-in-loop */
import { cacheFetch } from '@ali/yc-utils';

import { generateDataInterfaceStandalone } from '../GenerateInterfaceTool/generateDataQueryInterface.standalone';
import { GetCubesInfoTool } from '../GetCubesInfoTool';
import { generatePageCode, generatePageSchema } from './code';

import type { GeneratedPageType } from './code.share';
import { log } from '@/utils/decorators/log';
// 创建页面参数
interface GenerateCodeArgs {
  // 应用唯一标识
  appId?: string;
  // 页面名称
  pageName: string;
  // 页面ID
  pageId?: string;
  /**
   * 生成页面类型
   */
  pageType: GeneratedPageType;
  // 页面描述
  description?: string;
  /**
   * 是否需要数据接口
   * @default false
   */
  dataInterface?: boolean;
}

export async function checkRenderStatus() {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      window.removeEventListener('message', callback, false);
      console.error(new Error('render error: Canvas render timeout after 10s, please try again'));
      resolve(false);
    }, 10000);

    const callback = function (event: any) {
      if (event.data.type === 'canvasRenderSuccess') {
        clearTimeout(timeoutId);
        resolve(true);
        window.removeEventListener('message', callback, false);
      } else if (event.data.type === 'canvasRenderError') {
        clearTimeout(timeoutId);
        reject(new Error(`render error: ${stringifyObjectSafe(event.data.error) || stringifyObjectSafe(event.data)}`));
        window.removeEventListener('message', callback, false);
      }
    };
    window.addEventListener('message', callback, false);
  });
}

async function logCodeError(type: string, errorMsg: string, code: string) {
  const { model } = useAISettingStore.getState();
  await cacheFetch({
    url: '/APP_DZNV9RF3026B47VCKD1Z/query/formdata/saveFormData.json',
    method: 'POST',
    data: {
      appType: 'APP_DZNV9RF3026B47VCKD1Z',
      formUuid: 'FORM-2E649835CE024929BA24A4B57BCFE090BWL6',
      value: JSON.stringify([
        {
          componentName: 'TextField',
          fieldId: 'textField_ma26wzso',
          label: '开发人员',
          fieldData: { value: window.loginUser.userName },
        },
        {
          componentName: 'TextField',
          fieldId: 'textField_ma26wzsp',
          label: '模型',
          fieldData: { value: model },
        },
        {
          componentName: 'RadioField',
          fieldId: 'radioField_ma26wzsr',
          label: '错误类型',
          fieldData: { value: type },
        },
        {
          componentName: 'TextareaField',
          fieldId: 'textareaField_ma26wzsq',
          label: '错误信息',
          fieldData: { value: errorMsg },
        },
        {
          componentName: 'TextareaField',
          fieldId: 'textareaField_ma26wzss',
          label: '页面代码',
          fieldData: { value: code },
        },
      ]),
    },
  });
}

/**
 * 生成页面代码
 * 用于快速创建各种类型的React组件
 */
export class GenerateCodeTool extends BaseTool {
  name = 'generate_code';
  nameCn = '自定义页面生成';
  description =
    '用于**自定义页面**的创建操作，为页面生成UI界面及处理逻辑代码，**仅能用于页面生成**，如果没有**AppId**参数不要使用该工具，不要生成虚假AppId';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用唯一标识，格式示例:APP_XXX',
      },
      pageId: {
        type: 'string',
        description: '页面ID，如果是新建页面，不要传递该参数，如果是修改页面，请务必传递该参数，格式示例:FORM-XXX',
      },
      pageType: {
        type: 'string',
        description: `页面类型，必填：
Portal Pages
Purpose: Entry points that aggregate content from different sources
Examples: Corporate homepages, intranet landing pages, content hubs
Characteristics: Content aggregation, navigation-focused, personalization options
---
Application Pages
Purpose: Task-focused interfaces with specialized functionality
Examples: Document editors, design tools, project management apps
Characteristics: Rich interactions, specialized toolbars, focused workflows
        `,
        enum: ['application', 'portal'],
      },
      pageName: {
        type: 'string',
        description: '页面名称，创建必填，修改可选',
      },
      // dataInterface: {
      //   type: 'boolean',
      //   description:
      //     '是否需要数据接口，默认不需要，数据接口一般适合于聚合数据展示界面，类似于报表数据消费场景，一般涉及到图表绘制，都需要开启该选项',
      // },
      description: {
        type: 'string',
        description: isEnableCodeGenPRDContent
          ? '当前生成的页面名称和简述，如果是修改页面，该参数信息为要修改的具体内容（!!务必完整地将用户的原始字句，一字不漏地传递给该参数中!!）'
          : `页面功能描述，分为两个部分：
1. 将用户要求相关的生成意图原方不动地在这里输入；
2. 将生成的 PRD 中和该页面相关的功能原封不动地传递给代码生成器。
这里必须使用之前的生成的内容，切勿自己篡改、总结或者简述，以影响最终的生成效果。`,
      },
    },
    required: ['appId', 'pageName', 'description', 'pageType'],
  };

  /**
   * 执行工具
   */
  @log()
  async execute(args: GenerateCodeArgs): Promise<any> {
    try {
      this.logger.info('开始调用工具', this.name, args);
      const { appId = DEFAULT_APP_TYPE, description, pageName, pageId: prePageId, pageType, dataInterface } = args;
      let pageId = prePageId;

      // 处理页面依赖的数据源 cube 信息
      let cubeInfo: any;
      let interfaceInfo: any;
      if (dataInterface) {
        this.feedbackLogger.thinking('获取数据表信息中...');
        cubeInfo = await new GetCubesInfoTool().execute({
          appId,
        });
        if (!cubeInfo?.length) {
          console.warn(this.name, '获取数据表信息失败');
        }
        this.feedbackLogger.thinking('获取数据表 cube 信息完成');
        this.logger.info('🧾 获取数据表信息完成', cubeInfo);
        useAIAgentStateStore.getState().setCurrentAppCubeInfo(cubeInfo);
      }

      // 生成页面依赖的数据源查询接口 interface
      if (cubeInfo && cubeInfo?.length > 0 && dataInterface) {
        this.feedbackLogger.thinking('生成数据接口中...');
        interfaceInfo = await generateDataInterfaceStandalone(`需要生成页面的上下文信息：
* 应用ID：${appId}
* 页面描述：${description}
* 数据表 Cube 信息：${stringifyObjectSafe(cubeInfo)});
请根据页面生成需求，生成数据调用函数接口列表。
`);
        this.feedbackLogger.thinking('生成数据接口完成');
        this.logger.info('🧾 生成数据接口完成', interfaceInfo);
        useAIAgentStateStore.getState().setCurrentPageInterfaceInfo(interfaceInfo);
      }

      if (!prePageId) {
        const pageRes = await createPage({
          appType: appId,
          name: pageName,
          pageType,
        });
        pageId = pageRes?.pageId;
      }
      if (!pageId) {
        throw new ToolExecutionError(this.name, '页面不存在');
      }
      const schemaRes = await getFormSchema({
        formUuid: pageId,
        appType: appId,
        schemaVersion: 'V5',
        pageType,
      });
      if (!schemaRes) {
        throw new ToolExecutionError(this.name, '获取页面Schema失败');
      }

      // 获取 AICanvas 的代码
      const currentCode = schemaRes?.pages?.[0]?.componentsTree?.[0]?.children?.[0]?.props?.code || '';
      const appRes = await queryAppContext(appId);

      let generateSuccess = false;
      let tempCode = currentCode;
      let retryCount = 0;
      let errorMessage = '';
      let previewUrl = '';

      const fileId = 'custompage';
      this.feedbackLogger.file('正在生成页面代码...', {
        id: fileId,
        type: 'page',
      });

      // 获取页面列表
      const formListStr = (appRes.pageList || [])
        .map((page) => `- 名称：${page.name} 访问链接：/${appId}/workbench/${page.pageId}`)
        .join('\n');

      const enableCodeGenPRDContent = useAISettingStore.getState().enableCodeGenPRDContent;
      const currentPRD = useAIAgentStateStore.getState().generalPRD;
      // 最多重试两次，后续 throw 给外部工具完全重新执行
      while (!generateSuccess && retryCount < 2) {
        try {
          const newCode = await generatePageCode({
            description: errorMessage
              ? `${description}，生成出错，错误信息：${errorMessage}，请严格减产错误修复，或者重新生成`
              : `${
                  enableCodeGenPRDContent
                    ? `## 应用 PRD
${currentPRD}
## 当前你需要生成的页面 & 描述
${description}
`
                    : description
                }`,
            currentCode,
            pageType,
            isModifyCode: false,
            feedbackLogger: this.feedbackLogger,
            appContext: {
              ...appRes,
              appId,
              pageUrlList: `以下是已经生成的页面列表：\n\n${formListStr}`,
            } as any,
          });
          if (!newCode) {
            throw new Error('代码生成失败，newCode 生成的代码为空');
          }
          // after compile we store the code in the page
          tempCode = newCode;
          this.feedbackLogger.toolResult('代码生成完成，构建中...');
          const schema = await generatePageSchema(pageId, newCode, {
            pageType,
          });
          const res = await saveFormSchema({
            appType: appId,
            formUuid: pageId,
            content: schema,
            schemaVersion: 'V5',
          });
          if (!res) {
            throw new Error('保存页面Schema失败');
          }

          previewUrl = `/${appId}/preview/${pageId}?navConfig.type=none&__disableCache=true`;
          // 发送预览地址更新消息
          window.postMessage(
            {
              type: 'previewReady',
              url: previewUrl,
            },
            '*',
          );
          this.feedbackLogger.toolResult('检查页面渲染状态...');
          const checkResult = await checkRenderStatus();
          if (!checkResult) {
            this.feedbackLogger.toolResult(
              '页面长时间 10s 内没有响应，可能是页面代码有问题，可以打开 console 查看错误信息',
            );
          }
          generateSuccess = true;
        } catch (err) {
          errorMessage = err.message;
          let type = 'other';
          if (errorMessage.startsWith('bundle')) {
            type = 'bundle';
          } else if (errorMessage.includes('render')) {
            type = 'render';
          }
          await logCodeError(type, errorMessage, tempCode);
          this.logger.error(`代码生成失败: ${err}`);
          if (retryCount < 2) {
            this.feedbackLogger.toolResult('代码生成失败，正在重试...');
          }
          retryCount++;
        }
      }

      if (!generateSuccess) {
        // 创建场景，如果生成失败，删除对应页面
        if (!prePageId && pageId) {
          await deleteFormSchema({
            appType: appId,
            formUuid: pageId,
          });
        }
        throw new ToolExecutionError(this.name, '页面代码生成失败');
      }

      if (pageType === 'portal') {
        const adjustNavTool = new AdjustAppNavTool();
        await adjustNavTool.execute({
          appId,
          homePageId: pageId,
        });
      }

      // 生成文件
      this.feedbackLogger.file('已完成页面生成 - 预览', {
        id: fileId,
        type: 'page',
        link: previewUrl,
      });

      const result = {
        status: '✅',
        pageName,
        pageId,
      };
      return result;
    } catch (error) {
      this.logger.error(`页面代码生成失败: ${error}`);
      throw new ToolExecutionError(this.name, `页面代码生成失败: ${error}`);
    }
  }
  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    const { appId, pageName, pageId, description } = args;
    const appIdReg = /APP_[A-Z0-9]{20}/;
    const pageIdReg = /FORM-[A-Z0-9]{36}/;
    if (!appId) {
      errors.push('应用ID不能为空');
    } else if (!appIdReg.test(appId)) {
      errors.push('应用ID格式不正确，可以通过@指定需要生成页面的应用');
    }
    if (!pageName && !pageId) {
      errors.push('页面名称或页面ID不能为空');
    }
    if (pageId && !pageIdReg.test(pageId)) {
      errors.push('页面ID格式不正确');
    }
    if (!description) {
      errors.push('页面描述不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
