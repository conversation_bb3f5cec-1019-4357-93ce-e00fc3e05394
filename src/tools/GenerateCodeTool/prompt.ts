import type { GenerateCodeContext } from './code';

export type BasicSystemPromptOptions = {
  /**
   * 场景上下文 & 功能上下文
   */
  scenarioContext?: string;
  technologyDependencyContext?: string;
  /**
   * 是否基于 Yida API 的动态数据源
   * 如果是，则需要提供数据源和 API 的相关信息
   * 例如：数据源的 CUBE 信息、API 信息等
   */
  isDynamicDataSource?: boolean;
  /**
   * 应用上下文
   */
  appContext?: GenerateCodeContext['appContext'];
  /**
   * 页面上下文
   */
  pageContext?: {
    basicInfo: string;
    cubeInfo: string;
    dataSourceAPIInfo: string;
  };
  /**
   * 是否启用 Yida 存量组件作为 dependency 上下文
   * 支持引用 Yida 存量组件
   */
  enableYidaComponents?: boolean;
};

export const USER_PROMPT = (options?: { instruction?: string; existedCode?: string }) => `${options?.instruction || ''}
当前代码:
"""jsx
${options?.existedCode || '// no code, create a new page'}
"""
请根据需求和已有的代码（包括组件）等，修改或重新生成页面`;
