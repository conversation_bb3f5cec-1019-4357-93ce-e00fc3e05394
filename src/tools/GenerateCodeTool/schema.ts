import { bundleAICanvasCode, WebC } from '@ali/yida-webc';
import type { ComponentArtifactType } from '../CreateComponentsTool';
import type { GeneratedPageType } from './code.share';

let buildJSXBundle: (jsx: string) => Promise<string> = null;

export async function getBuildJSXBundle() {
  if (buildJSXBundle) return buildJSXBundle;
  const yidaPluginManager: YidaPlugin = await import('yida/plugin').then((m) => m.default);
  await yidaPluginManager.init({
    scenarioCode: [],
    configMode: 'config',
    config: {
      plugins: [],
    },
  });
  const module = await yidaPluginManager.loadComponent({
    moduleName: 'yidaPluginAiCanvas',
    version: '0.0.3',
  });
  buildJSXBundle = module.buildJSXBundle;
  return buildJSXBundle;
}

export async function generatePageSchema(
  pageId: string,
  currentCode: string,
  options?: {
    components?: ComponentArtifactType[];
    pageType?: GeneratedPageType;
  },
) {
  let runtimeCode = '';
  let modules = [];
  let code = currentCode;

  const needBundleComponentList = !!options?.components;

  const webC: WebC = window?.webc;
  if (webC?.isProjectReady) {
    if (needBundleComponentList) {
      const { components } = options;
      const componentMap: Record<string, string> = {};
      components.forEach((item) => {
        componentMap[item.fileName] = item.code;
      });
      await webC.traverseAndWrite(componentMap, '/yidaComp/src');
    }
    const { bundleCode, sourceCode, dependencies, output } = await bundleAICanvasCode(code);
    runtimeCode = bundleCode;
    modules = dependencies;
    code = sourceCode;
    const errorOutput = output.filter((item) => item.type === 'stderr');
    if (errorOutput.length > 0) {
      throw new Error(`bundle error: ${errorOutput.map((item) => item.msg).join('\n')}`);
    }
  } else {
    throw new Error('bundle error: webC未准备就绪.');
  }
  if (!runtimeCode || !modules?.length) {
    // 说明编译失败
    throw new Error('bundle error: 代码编译失败，没有目标代码或导入模块存在，建议重新生成.');
  }
  return {
    schemaType: 'superform',
    schemaVersion: '5.0',
    pages: [
      {
        utils: [
          {
            name: 'legaoBuiltin',
            type: 'npm',
            content: {
              package: '@ali/vu-legao-builtin',
              version: '3.0.0',
              exportName: 'legaoBuiltin',
            },
          },
          {
            name: 'yidaPlugin',
            type: 'npm',
            content: {
              package: '@ali/vu-yida-plugin',
              version: '1.0.13',
              exportName: 'yidaPlugin',
            },
          },
        ],
        componentsMap: [
          {
            package: '@ali/vc-deep-yida',
            version: '1.5.169',
            componentName: 'Page',
          },
          {
            package: '@ali/vc-deep-yida',
            version: '1.5.169',
            componentName: 'RootHeader',
          },
          {
            package: '@ali/vc-deep-yida',
            version: '1.5.169',
            componentName: 'RootContent',
          },
          {
            package: '@ali/vc-deep-yida',
            version: '1.5.169',
            componentName: 'RootFooter',
          },
        ],
        componentsTree: [
          {
            componentName: 'Page',
            id: 'node_ocm8o40xvq1',
            props: {
              templateVersion: '1.0.0',
              pageStyle: {
                backgroundColor: '#f2f3f5',
              },
              contentMargin: '0',
              contentPadding: '0',
              contentBgColor: 'transparent',
              contentMarginMobile: '0',
              contentPaddingMobile: '0',
              contentBgColorMobile: 'white',
              showTitle: false,
              className: 'page_m8o41dj2',
            },
            dataSource: {
              offline: [],
              globalConfig: {
                fit: {
                  compiled:
                    "'use strict';\n\nvar __preParser__ = function fit(response) {\n  var content = response.content !== undefined ? response.content : response;\n  var error = {\n    message: response.errorMsg || response.errors && response.errors[0] && response.errors[0].msg || response.content || '远程数据源请求出错，success is false'\n  };\n  var success = true;\n  if (response.success !== undefined) {\n    success = response.success;\n  } else if (response.hasError !== undefined) {\n    success = !response.hasError;\n  }\n  return {\n    content: content,\n    success: success,\n    error: error\n  };\n};",
                  source:
                    "function fit(response) {\r\n  const content = (response.content !== undefined) ? response.content : response;\r\n  const error = {\r\n    message: response.errorMsg ||\r\n      (response.errors && response.errors[0] && response.errors[0].msg) ||\r\n      response.content || '远程数据源请求出错，success is false',\r\n  };\r\n  let success = true;\r\n  if (response.success !== undefined) {\r\n    success = response.success;\r\n  } else if (response.hasError !== undefined) {\r\n    success = !response.hasError;\r\n  }\r\n  return {\r\n    content,\r\n    success,\r\n    error,\r\n  };\r\n}",
                  type: 'js',
                  error: {},
                },
              },
              online: [],
              list: [],
              sync: true,
            },
            methods: {
              __initMethods__: {
                type: 'js',
                source: 'function (exports, module) { /*set actions code here*/ }',
                compiled: 'function (exports, module) { /*set actions code here*/ }',
              },
            },
            lifeCycles: {
              componentDidMount: {
                id: 'didMount',
                name: 'didMount',
                params: {},
                type: 'actionRef',
              },
              componentWillUnmount: '',
              constructor: {
                type: 'js',
                compiled:
                  "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
                source:
                  "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
              },
            },
            hidden: false,
            title: '',
            isLocked: false,
            condition: true,
            conditionGroup: '',
            children: [
              {
                componentName: 'YidaAICanvas',
                id: 'node_ocm8nxcq7w6',
                props: {
                  code,
                  runtimeCode,
                  importedModules: JSON.stringify(modules),
                  isWebCCompiled: true,
                  componentProps: {},
                  pageType: options?.pageType || 'application',
                },
                hidden: false,
                title: '',
                isLocked: false,
                condition: true,
                conditionGroup: '',
              },
            ],
          },
        ],
        id: pageId,
        connectComponent: [],
      },
    ],
    actions: {
      module: {
        source:
          "/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系宜搭平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能，比如：调用阿里云接口用来做图像识别、上报用户使用数据（如加载完成打点）等等。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nexport function didMount() {\n  console.log(`「页面 JS」：当前页面地址 ${location.href}`);\n  // console.log(`「页面 JS」：当前页面 id 参数为 ${this.state.urlParams.id}`);\n  // 更多 this 相关 API 请参考：https://www.yuque.com/yida/support/ocmxyv#OCEXd\n  // document.title = window.loginUser.userName + ' | 宜搭';\n}",
        compiled:
          '"use strict";\n\nexports.__esModule = true;\nexports.didMount = didMount;\n/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系宜搭平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能，比如：调用阿里云接口用来做图像识别、上报用户使用数据（如加载完成打点）等等。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nfunction didMount() {\n  console.log("\\u300C\\u9875\\u9762 JS\\u300D\\uFF1A\\u5F53\\u524D\\u9875\\u9762\\u5730\\u5740 " + location.href);\n  // console.log(`「页面 JS」：当前页面 id 参数为 ${this.state.urlParams.id}`);\n  // 更多 this 相关 API 请参考：https://www.yuque.com/yida/support/ocmxyv#OCEXd\n  // document.title = window.loginUser.userName + \' | 宜搭\';\n}\n',
      },
      type: 'FUNCTION',
      list: [
        {
          id: 'didMount',
          title: 'didMount',
        },
      ],
    },
    config: {
      connectComponent: [],
    },
  };
}
