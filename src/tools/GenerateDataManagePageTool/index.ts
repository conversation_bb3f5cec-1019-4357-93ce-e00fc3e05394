import { BaseTool } from '@/utils/Tool';
import { saveFormSchemaInfo } from '@/apis/saveFormSchemaInfo';
import { ToolExecutionError } from '@/utils/Errors';
import { log } from '@/utils/decorators/log';

/**
 * 生成数据管理页面工具
 * 基于表单创建一个数据管理页面（视图）
 */
export class GenerateDataManagePageTool extends BaseTool {
  name = 'generate_data_manage_page';
  nameCn = '生成数据管理页面';
  description = '一个基于表单生成数据管理页面（视图）的工具';
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用ID，通常以APP_开头的字符串',
      },
      formUuid: {
        type: 'string',
        description: '关联的表单ID',
      },
      formName: {
        type: 'string',
        description: '数据管理页面名称',
      },
    },
    required: ['appId', 'formUuid', 'formName'],
  };

  /**
   * 执行工具
   * @param params 工具参数
   * @returns 创建结果
   */
  @log()
  async execute(params: { appId: string; formUuid: string; formName: string }): Promise<any> {
    try {
      const { appId, formUuid, formName } = params;
      // 调用创建表单接口，固定formType为view
      const result = await saveFormSchemaInfo({
        appType: appId,
        formType: 'view', // 固定为view类型
        title: formName,
        relateFormUuid: formUuid,
        relateFormType: 'receipt',
      });

      if (!result) {
        return {
          success: false,
          message: '创建数据管理页面失败',
        };
      }

      // 生成数据管理页面访问链接
      const viewUrl = `https://${window.location.host}/${appId}/workbench/${result.formUuid}`;

      return {
        success: true,
        message: '创建数据管理页面成功',
        pageId: result.formUuid,
        viewUrl,
        visitView: `[访问数据管理页面](${viewUrl})`,
      };
    } catch (error) {
      this.logger.error(`生成数据管理页面失败: ${error}`);
      throw new ToolExecutionError(this.name, `创建数据管理页面失败: ${error.message || '未知错误'}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    const { appId, formUuid, formName } = args;

    if (!appId) {
      errors.push('应用唯一标识不能为空');
    }

    if (!formUuid) {
      errors.push('关联的表单ID不能为空');
    }

    if (!formName) {
      errors.push('数据管理页面名称不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
