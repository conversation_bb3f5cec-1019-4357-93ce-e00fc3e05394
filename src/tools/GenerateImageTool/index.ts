import { ToolExecutionError } from '@/utils/Errors';
import { triggerTxtToImg } from '@/apis/triggerTxtToImg';
import { request, getYidaConfig } from '@ali/yc-utils';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

// 生成图片参数
interface GenerateImageArgs {
  // 提示词
  prompt: string;
  // 图片尺寸，默认为 1024*1024
  size?: string;
  // 生成图片数量，默认为 1
  batchSize?: number;
  // 图片样式，如"写实风格"、"卡通风格"等
  style?: string;
  // 图片主题色
  color?: string;
}

/**
 * 通用图片生成工具
 * 用于根据提示词生成各种图片
 */
export class GenerateImageTool extends BaseTool {
  name = 'generate_image';
  nameCn = '生成图片';
  description = '根据提示词生成图片，支持自定义尺寸、风格和颜色等';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      prompt: {
        type: 'string',
        description: '图片生成提示词，描述你想要生成的图片内容',
      },
      size: {
        type: 'string',
        description: '图片尺寸，格式为"宽*高"，例如"1024*1024"、"800*600"等',
        enum: ['1024*1024', '1280*720', '720*1280'],
      },
      batchSize: {
        type: 'number',
        description: '生成图片数量，默认为1, 最多4张',
      },
      style: {
        type: 'string',
        description: '图片风格，例如"写实风格"、"卡通风格"、"水彩画风格"等',
      },
      color: {
        type: 'string',
        description: '图片主题色，例如"蓝色"、"红色"等',
      },
    },
    required: ['prompt'],
  };

  /**
   * 生成图片
   * @param args 生成图片的参数
   * @returns 生成结果，包含图片URL
   */
  @log()
  async execute(args: GenerateImageArgs): Promise<any> {
    this.logger.info('开始生成图片');
    // 生成完整提示词
    const fullPrompt = this.generatePrompt(args);
    const size = args.size || '1024*1024';
    const batchSize = args.batchSize || 1;
    const fileName = '生成图片.png';
    // 开始生成图片
    const reqData = {
      prompt: fullPrompt,
      size,
      batchSize,
    };
    // 调用API生成图片
    const response = await triggerTxtToImg(reqData);
    // 处理返回的图片URL
    if (!response || response.length === 0) {
      throw new Error('生成图片失败');
    }
    // 获取生成的图片URL
    const urls = response.map((url) => url.replace(/^(.*?)https/, 'https'));
    // 上传图片到附件服务
    const result = await Promise.all(
      urls.map(async (url, index) => {
        const fileNameWithIndex = batchSize > 1 ? `${fileName.replace(/\.([^.]+)$/, `_${index + 1}.$1`)}` : fileName;
        const res = await request({
          method: 'POST',
          url: '/query/attach/uploadNoAuth.json',
          data: {
            appType: getYidaConfig('appType') || 'default_tianshu_app',
            formUuid: getYidaConfig('formUuid'),
            fileName: fileNameWithIndex,
            imageUrl: url,
          },
        });
        this.logger.info(`🚀 ~ GenerateImageTool ~ 上传图片结果: ${JSON.stringify(res)}`);
        if (!res?.url) {
          this.logger.error(`上传图片失败: ${fileNameWithIndex}`);
          throw new ToolExecutionError(this.name, `上传图片失败: ${fileNameWithIndex}`);
        }
        return {
          url: res.url,
          name: fileNameWithIndex,
        };
      }),
    );
    return `好的，以为您生成了${result.length}张图片，如下：\n\n${result.map((r) => `![图片](${r.url})`).join('')}`;
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.prompt) {
      errors.push('提示词不能为空');
    }

    if (args.batchSize && (typeof args.batchSize !== 'number' || args.batchSize < 1 || args.batchSize > 10)) {
      errors.push('生成图片数量必须是1-10之间的整数');
    }

    if (args.size && !args.size.match(/^\d+\*\d+$/)) {
      errors.push('图片尺寸格式不正确，应为"宽*高"，例如"1024*1024"');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * 转换参数为完整的AI提示词
   */
  private generatePrompt(args: GenerateImageArgs): string {
    const { prompt, style, color } = args;
    let fullPrompt = prompt;

    // 添加风格
    if (style) {
      fullPrompt += `，风格：${style}`;
    }

    // 添加颜色
    if (color) {
      fullPrompt += `，颜色：${color}`;
    }

    return fullPrompt;
  }
}
