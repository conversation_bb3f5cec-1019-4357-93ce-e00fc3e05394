import { LLMService } from '@/services/llmService';
import type { Message } from '@/types/types';
import { MessageUtils } from '@/utils/Message';
import { GenerateInterfaceTool } from '.';
import { parseJSONObjectSafe, stringifyObjectSafe } from '@/utils/LangUtils';
import { extractCode } from '../GenerateCodeTool/code';

const generateInterfaceToolInstance = new GenerateInterfaceTool();

export const generateDataInterfaceStandalone = async (userInstruction: string) => {
  const llmService = new LLMService({
    model: 'deepseek/deepseek-chat-v3-0324',
    maxTokens: 8000,
    temperature: 1,
  });
  const systemMessage: Message = MessageUtils.systemMessage(`
## Role
你是一个数据接口的生成器
## Description
${generateInterfaceToolInstance.description}
## OUTPUT Parameters
${stringifyObjectSafe(generateInterfaceToolInstance.parameters)}
## Task
Generate the data interface function tool call input parameters and output parameters based on the user instruction. The input parameters should be a JSON object, and the output parameters should be a JSON object as well. The function name should be in camelCase format, and the input and output parameters should be in snake_case format. The function name should be descriptive of the task it performs.
`);
  const userMessage: Message = MessageUtils.userMessage(`创建一个合理的计划来完成以下任务: ${userInstruction}`);
  const llmRes = await llmService.askStream([userMessage], [systemMessage], [], 'auto', (streamType, data) => {}, true);
  const result = extractCode(llmRes.content || '', 'json')?.[0]?.content;
  const inputParams = parseJSONObjectSafe(result);
  const interfaceResult = await generateInterfaceToolInstance.execute(inputParams);
  console.info('🧾 AI generated interface result: ', interfaceResult);
  return interfaceResult;
};
