import { ToolSchema } from '@/types/types';
import { BaseTool } from '../../utils/Tool';
import { searchCubeList } from '@/apis/searchCubeList';
import { getCubeFieldList } from '@/apis/getCubeFieldList';
import { cloneDeep } from 'lodash';
import { uniqueId } from '@ali/ve-utils';
import { defaultDataModuleSchema } from '@/config/defaultDataModuleSchema';
import {
  genArrayFieldExpressionWrap,
  getFieldDefForFieldList,
  getFieldDefForFilter,
  getRealFieldObj,
  getSingleFilter,
  getTimeFilter,
  getTimeGranularityType,
  judgeNumField,
  OtherFilter,
  TIME_CONDITION_TYPE_ARR,
  TIME_FILTER_VARS,
  TimeFilter,
} from './qm';
import { defaultCardSchema } from '@/config/defaultCardSchema';
import { createCard, publishCard, updateCard } from '@/apis/cardCRUD';
import { ToolExecutionError } from '@/utils/Errors';
import { log } from '@/utils/decorators/log';

export interface SelectField {
  fieldId: string;
  comment: string;
  aggregateType?: 'COUNT' | 'COUNT_DISTINCT' | 'SUM' | 'AVG' | 'MAX' | 'MIN' | 'NONE';
  timeGranularityType?: 'YEAR' | 'MONTH' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
  alias?: string;
}

export interface InputParams {
  description?: string;
  cubeCode: string;
  selectFields: SelectField[];
  timeFilters?: TimeFilter[];
  otherFilters?: OtherFilter[];
  orderBy?: [{ field: string; type: 'DESC' | 'ASC' }];
  limit?: number;
  nodeId?: string;
}

export interface OutputParams {
  prdId: string;
  pageId: string;
  cid: string;
  componentClassName: string;
  dataSetKey: string;
  visualType: string;
  cardCode: string;
}

export interface ResponseMeta {
  alias: string;
  comment: string;
  dataType: string;
}

export interface GenerateInterfaceArgs {
  appId: string;
  interfaceList: InputParams[];
  description: string;
}

export interface GenerateInterfaceResponse {
  description: string;
  params: OutputParams;
  responseMeta: ResponseMeta[];
}

export class GenerateInterfaceTool extends BaseTool {
  name = 'generate_data_interface';
  nameCn = '生成数据接口';
  description = `一个为页面组件或模块生成数据接口的工具。在不了解当前应用已经存在哪些数据表以及表中有哪些字段信息前，请不要调用该工具，不要解释原因继续询问用户。
生成数据接口规则如下：
1. 生成的数据表唯一标识 cubeCode 和 查询字段 selectFields 里面的 fieldId 都需要从上下文真实的数据表和字段中获取，不能杜撰
2. 一个数据接口仅支持查询一张表的一个或多个字段，不支持跨表查询
3. 对于 STRING 类型的字段：支持的aggregateType有 NONE | COUNT | COUNT_DISTINCT
4. 对于 INTEGER | DOUBLE 类型的字段：支持的aggregateType有 NONE | SUM | AVG | MAX | MIN
5. 对于 DATE 类型的时间字段：需要指定时间粒度类型 timeGranularityType
6. 过滤条件分为时间过滤条件timeFilter 和 其他过滤条件otherFilter2种类型
7. timeFilter.value为字符串类型，取值范围如下，不支持其他值：
    - THIS_YEAR: 今年
    - THIS_MONTH: 本月
    - LAST_MONTH: 上月
    - NEARLY_3_MONTHS: 近3个月(不含本月)
    - NEARLY_3_MONTHS_INCLUDE_CURRENT_MONTH: 近3个月(含本月)
    - NEARLY_6_MONTHS: 近6个月(不含本月)
    - NEARLY_6_MONTHS_INCLUDE_CURRENT_MONTH: 近6个月(含本月)
    - NEARLY_12_MONTHS: 近12个月(不含本月)
    - NEARLY_12_MONTHS_INCLUDE_CURRENT_MONTH: 近12个月(含本月)
    - TODAY: 今天
    - YESTERDAY: 昨天
    - TOMORROW: 明天
    - LAST_NATURAL_WEEK: 上周
    - NEARLY_A_WEEK: 近1周, 近7天
    - NEARLY_TWO_WEEK: 近2周
    - THIS_WEEK_TO_NOW: 本周
    - NEARLY_30_DAYS: 近30天
    - THIS_MONTH_TO_NOW: 本月至今
    - NEARLY_90_DAYS: 近90天
    - CURRENT_HOUR: 截止目前
    - LAST_HOUR: 上1小时
`;
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      appId: { type: 'string', description: '应用ID，通常为APP_开头的字符串' },
      interfaceList: {
        type: 'array',
        description: '生成的数据接口描述列表',
        items: {
          type: 'object',
          properties: {
            description: { type: 'string', description: '数据接口用途描述' },
            cubeCode: { type: 'string', description: '查询的数据表唯一标识cubeCode' },
            selectFields: {
              type: 'array',
              description: '需要查询的字段列表',
              items: {
                type: 'object',
                properties: {
                  fieldId: { type: 'string', description: '字段ID' },
                  comment: { type: 'string', description: '字段描述' },
                  aggregateType: {
                    type: 'string',
                    description: '聚合类型',
                    enum: ['NONE', 'COUNT', 'COUNT_DISTINCT', 'SUM', 'AVG', 'MAX', 'MIN'],
                  },
                  timeGranularityType: {
                    type: 'string',
                    description: '时间粒度类型',
                    enum: ['YEAR', 'MONTH', 'DAY', 'HOUR', 'MINUTE', 'SECOND'],
                  },
                },
                requiredProperties: ['fieldId'],
              },
            },
            timeFilters: {
              type: 'array',
              description: '时间过滤条件列表，必须是 DATE 类型字段',
              items: {
                type: 'object',
                properties: {
                  fieldId: { type: 'string', description: '字段ID' },
                  conditionType: {
                    type: 'string',
                    description: '条件类型',
                    enum: [
                      'EqualTo',
                      'NotEqualTo',
                      'GreaterThan',
                      'LessThan',
                      'GreaterThanOrEqualTo',
                      'LessThanOrEqualTo',
                      'Between',
                    ],
                  },
                  value: {
                    type: 'string',
                    description: '条件值',
                    enum: [
                      'THIS_YEAR',
                      'THIS_MONTH',
                      'LAST_MONTH',
                      'NEARLY_3_MONTHS',
                      'NEARLY_3_MONTHS_INCLUDE_CURRENT_MONTH',
                      'NEARLY_6_MONTHS',
                      'NEARLY_6_MONTHS_INCLUDE_CURRENT_MONTH',
                      'NEARLY_12_MONTHS',
                      'NEARLY_12_MONTHS_INCLUDE_CURRENT_MONTH',
                      'TODAY',
                      'YESTERDAY',
                      'TOMORROW',
                      'LAST_NATURAL_WEEK',
                      'NEARLY_A_WEEK',
                      'NEARLY_TWO_WEEK',
                      'THIS_WEEK_TO_NOW',
                      'NEARLY_30_DAYS',
                      'THIS_MONTH_TO_NOW',
                      'NEARLY_90_DAYS',
                      'CURRENT_HOUR',
                      'LAST_HOUR',
                    ],
                  },
                },
                requiredProperties: ['fieldId', 'conditionType', 'value'],
              },
            },
            otherFilters: {
              type: 'array',
              description: '其他过滤条件列表，必须是非 DATE 类型字段',
              items: {
                type: 'object',
                properties: {
                  fieldId: { type: 'string', description: '字段ID' },
                  conditionType: {
                    type: 'string',
                    description: '条件类型',
                    enum: [
                      'EqualTo',
                      'NotEqualTo',
                      'In',
                      'NotIn',
                      'Like',
                      'NotLike',
                      'IsNull',
                      'IsNotNull',
                      'GreaterThan',
                      'LessThan',
                      'GreaterThanOrEqualTo',
                      'LessThanOrEqualTo',
                    ],
                  },
                  value: { type: 'string', description: '条件值' },
                },
                requiredProperties: ['fieldId', 'conditionType', 'value'],
              },
            },
            orderBy: {
              type: 'array',
              description: '排序字段列表',
              items: {
                type: 'object',
                properties: {
                  fieldId: { type: 'string', description: '字段ID' },
                  type: { type: 'string', description: '排序方式', enum: ['DESC', 'ASC'] },
                },
                requiredProperties: ['fieldId', 'type'],
              },
            },
            limit: { type: 'number', description: '返回数据条数，最多100条', default: 10 },
          },
          requiredProperties: ['description', 'cubeCode', 'selectFields'],
        },
      },
      description: {
        type: 'string',
        description: '对于生成的数据接口，以及需要生成但未生成接口的模块，给出解释及原因',
      },
    },
    required: ['appId', 'interfaceList', 'description'],
  };

  @log()
  async execute(args: GenerateInterfaceArgs): Promise<GenerateInterfaceResponse[]> {
    const { appId, interfaceList } = args;
    const cubeList = await searchCubeList({ appType: appId, cubeSource: 'YIDA_FORM' });
    if (!cubeList?.length) {
      throw new ToolExecutionError(this.name, `未找到数据表，请检查应用类型${appId}是否正确, 并确认数据表是否已创建`);
    }
    // 获取字段列表
    const cubeCodes = cubeList.map((cube: any) => cube.code);
    const fieldDefListMap = await getCubeFieldList({ appType: appId, cubeCodes });
    // 进行参数校验
    const errors = this.checkValidInputParams(interfaceList, fieldDefListMap);
    if (errors.length > 0) {
      throw new ToolExecutionError(this.name, errors.join(', '));
    }
    // 给selectFields添加nodeId, alias
    interfaceList.forEach((item: any) => {
      item.selectFields.forEach((field: any, index: number) => {
        // eslint-disable-next-line no-param-reassign
        field.alias = `${field.fieldId}_${index + 1}`;
      });
    });
    // 生成单个接口的卡片schema
    const dataModuleSchemas = this.generateCardSchemas(interfaceList, fieldDefListMap);

    // 构建cardSchema
    const cardSchema = cloneDeep(defaultCardSchema);
    const pageChildren = cardSchema.pages[0].componentsTree[0].children;
    const RootContent = pageChildren.find((item: any) => item.componentName === 'RootContent');
    RootContent.children = dataModuleSchemas;
    // 创建数据卡片
    const cardId = await createCard({
      appType: appId,
      title: '数据接口卡片',
    });
    if (!cardId) {
      throw new ToolExecutionError(this.name, '创建数据卡片失败');
    }
    // 保存数据卡片
    const updateCardRes = await updateCard({
      appType: appId,
      cardId,
      cardSchema,
    });
    if (!updateCardRes) {
      throw new ToolExecutionError(this.name, '保存数据卡片失败');
    }
    // 发布卡片
    const publishCardRes = await publishCard({
      appType: appId,
      cardId,
    });
    if (!publishCardRes) {
      throw new ToolExecutionError(this.name, '发布数据卡片失败');
    }
    // 返回接口信息
    return dataModuleSchemas.map((schema: any) => {
      const responseMeta: ResponseMeta[] = [];
      const { fieldDefinitionList, fieldList } = schema.props.dataSetModelMap.table.dataViewQueryModel;
      fieldList.forEach((alias: string) => {
        const fieldDef = fieldDefinitionList.find((f: any) => f.alias === alias);
        const { dataType, aliasName } = fieldDef;
        responseMeta.push({
          alias,
          comment: aliasName.zh_CN,
          dataType,
        });
      });
      return {
        description: schema.props.componentTitle.zh_CN,
        url: `/${appId}/visual/visualizationDataRpc/getDataAsync.json`,
        params: {
          prdId: cardId,
          pageId: cardId,
          cid: schema.id,
          componentClassName: 'YoushuTable',
          dataSetKey: 'table',
          visualType: 'CARD',
          cardCode: cardId,
        },
        responseMeta,
      };
    });
  }
  generateCardSchemas(interfaceList: InputParams[], fieldDefListMap: any) {
    return interfaceList.map((interfaceParams: InputParams) => {
      const fieldDefList = fieldDefListMap[interfaceParams.cubeCode];
      return this.generateOneInterfaceSchema(interfaceParams, fieldDefList);
    });
  }
  generateOneInterfaceSchema(interfaceParams: any, fieldDefList: any) {
    const schema = cloneDeep(defaultDataModuleSchema);
    // 依次替换schema内容
    const nodeId = `node${uniqueId(null, '', '')}`;
    schema.id = nodeId;
    schema.props.cid = nodeId;
    schema.props.componentTitle.zh_CN = interfaceParams.description;
    schema.props.componentTitle.en_US = interfaceParams.description;
    schema.props.fieldId = `YoushuTable${uniqueId(null, '', '')}`;
    schema.props.dataSetModelMap = this.generateDataSetModelMap(interfaceParams, fieldDefList);
    return schema;
  }
  generateDataSetModelMap(interfaceParams: InputParams, fieldDefList: any) {
    const { cubeCode, selectFields, timeFilters, otherFilters, orderBy, limit } = interfaceParams;
    // 解析selectFields字段
    const fieldDefinitionList: any = [];
    const fieldList: any = [];
    const schemaFieldList: any = [];
    const filterList: any = [];
    const schemaFilterList: any = [];
    selectFields.forEach((item: any) => {
      const { alias, comment, aggregateType = 'NONE', fieldId, timeGranularityType } = item;
      const fieldDef = fieldDefList.find((f: any) => f.id === fieldId);
      const realFieldObj = getRealFieldObj({ ...fieldDef, timeGranularityType });
      const newFieldDef = { ...fieldDef, alias, text: comment, timeGranularityType };
      fieldList.push(alias);
      // 判断，如果是ARRAY，且存在聚合类型(COUNT或去重COUNT)，则转换成公式
      if (realFieldObj.dataType === 'ARRAY' && ['COUNT_DISTINCT', 'COUNT'].includes(aggregateType)) {
        const expressionWrap = genArrayFieldExpressionWrap(realFieldObj, aggregateType);
        const tarFieldDef = getFieldDefForFieldList(
          {
            ...newFieldDef,
            expression: expressionWrap.expression,
            expressionWrap,
            dataType: 'INTEGER',
          },
          { aggregateType },
        );
        fieldDefinitionList.push(tarFieldDef);
        schemaFieldList.push({
          title: {
            type: 'i18n',
            zh_CN: comment,
          },
          isDimension: false,
          format: {
            type: 'NONE',
          },
          link: [{ type: 'NONE' }],
          drillList: [],
          aggregateType,
          orderBy: {
            type: 'NONE',
            reference: alias,
          },
          fieldKey: alias,
          visible: true,
          beUsedTimes: 1,
          text: comment,
          expression: expressionWrap.expression,
          expressionWrap,
          dataType: 'INTEGER',
          fieldCode: '',
          cubeCode: '',
          classifiedCode: '',
        });
      } else {
        const tarFieldDef = getFieldDefForFieldList(newFieldDef, { aggregateType });
        fieldDefinitionList.push(tarFieldDef);
        const fieldOrderBy = orderBy?.find((o: any) => o.fieldId === fieldId);
        schemaFieldList.push({
          title: {
            type: 'i18n',
            zh_CN: realFieldObj.text,
          },
          classifiedCode: realFieldObj.classifiedCode,
          cubeCode: realFieldObj.cubeCode,
          fieldCode: realFieldObj.fieldCode,
          isDimension: 'false',
          dataType: realFieldObj.dataType,
          format: { type: 'NONE' },
          link: [{ type: 'NONE' }],
          drillList: [],
          aggregateType,
          orderBy: {
            type: fieldOrderBy ? fieldOrderBy.type : 'NONE',
            reference: alias,
          },
          fieldKey: alias,
          visible: true,
          beUsedTimes: 1,
          isVisible: 'y',
          id: realFieldObj.id,
          text: realFieldObj.text,
          measureType: aggregateType !== 'NONE' ? 'MEASURE_ATTRIBUTE' : undefined,
        });
      }
    });
    // 解析timeFilters
    timeFilters?.forEach((filter: any) => {
      const { fieldId, value } = filter;
      const fieldDef = fieldDefList.find((f: any) => f.id === fieldId);
      const timeGranularityType = getTimeGranularityType(value);
      const newFieldDef = { ...fieldDef, timeGranularityType };
      const tarFieldDef = getFieldDefForFilter(newFieldDef);
      fieldDefinitionList.push(tarFieldDef);
      const filterObj = getTimeFilter(newFieldDef, filter);
      filterList.push(filterObj);
      const realFieldObj = getRealFieldObj(newFieldDef);
      schemaFilterList.push({
        ...filterObj,
        fieldInfo: {
          ...realFieldObj,
          fieldKey: filterObj.alias,
        },
      });
    });
    // 解析otherFields
    otherFilters?.forEach((filter: any) => {
      const { fieldId } = filter;
      const fieldDef = fieldDefList.find((f: any) => f.id === fieldId);
      const newFieldDef = { ...fieldDef };
      const tarFieldDef = getFieldDefForFilter(newFieldDef);
      fieldDefinitionList.push(tarFieldDef);
      const filterObj = getSingleFilter(newFieldDef, filter);
      filterList.push(filterObj);
      const realFieldObj = getRealFieldObj(newFieldDef);
      schemaFilterList.push({
        ...filterObj,
        fieldInfo: {
          ...realFieldObj,
          fieldKey: filterObj.alias,
        },
      });
    });
    // 解析orderBy
    const orderByList = orderBy?.map((o: any) => {
      const fieldDef = fieldDefList.find((f: any) => f.id === o.fieldId);
      const hasInDefinObj = fieldDefinitionList.find((item: any) => item.id === o.fieldId);
      const alias = hasInDefinObj ? hasInDefinObj.alias : o.fieldId;
      if (!hasInDefinObj) {
        const tarFieldDef = getFieldDefForFieldList({ ...fieldDef, alias });
        fieldDefinitionList.push(tarFieldDef);
        const realFieldObj = getRealFieldObj({ ...fieldDef });
        realFieldObj.aggregateType = 'NONE';
        schemaFieldList.push({
          title: {
            type: 'i18n',
            zh_CN: realFieldObj.text,
          },
          classifiedCode: realFieldObj.classifiedCode,
          cubeCode: realFieldObj.cubeCode,
          fieldCode: realFieldObj.fieldCode,
          isDimension: 'false',
          dataType: realFieldObj.dataType,
          format: { type: 'NONE' },
          link: [{ type: 'NONE' }],
          drillList: [],
          aggregateType: 'NONE',
          orderBy: {
            type: o.type || 'DESC',
            reference: alias,
          },
          fieldKey: alias,
          visible: true,
          beUsedTimes: 1,
          isVisible: 'y',
          id: realFieldObj.id,
          text: realFieldObj.text,
        });
      }
      return {
        alias,
        orderType: o.type || 'DESC',
      };
    });
    return {
      table: {
        dataViewQueryModel: {
          cubeCode,
          fieldDefinitionList,
          fieldList,
          filterList,
          orderByList,
        },
        fieldList: schemaFieldList,
        youshuDataType: 'real',
        cubeCodes: [cubeCode],
        columnFields: schemaFieldList,
        filterList: schemaFilterList,
        limit,
        mockData: [] as any,
      },
    };
  }
  checkValidInputParams(interfaceList: InputParams[], fieldListMap: any) {
    const errors: string[] = [];
    interfaceList.forEach((item: any) => {
      const { cubeCode, selectFields, timeFilters, otherFilters } = item;
      // 校验cubeCode是否在cubeList中
      if (!fieldListMap[cubeCode]) {
        errors.push(`数据表${cubeCode}不存在，请检查数据表ID是否正确`);
        return;
      }
      // 校验selectFields是否在cubeList中
      const fieldList = fieldListMap[cubeCode];
      if (!fieldList?.length) {
        errors.push(`数据表${cubeCode}中不存在字段，请检查数据表ID是否正确`);
        return;
      }
      selectFields.forEach((field: any) => {
        const { fieldId, aggregateType } = field;
        let fieldDef = fieldList.find((subF: any) => subF.id === fieldId);
        if (!fieldDef) {
          fieldDef = fieldList.find((subF: any) => subF.id.indexOf(fieldId) > -1);
          if (fieldDef) {
            field.fieldId = fieldDef.id;
          }
        }
        if (!fieldDef) {
          errors.push(`数据表${cubeCode}中不存在字段${fieldId}`);
        } else if (
          !['INTEGER', 'DOUBLE'].includes(fieldDef.dataType) &&
          ['SUM', 'AVG', 'MAX', 'MIN'].includes(aggregateType)
        ) {
          errors.push(`字段${fieldId}的数据类型非数字类型，不支持SUM、AVG、MAX、MIN聚合方式，请修改`);
        }
      });
      // 校验timeFilters是否在cubeList中
      timeFilters?.forEach((filter: any) => {
        const { fieldId, conditionType, value } = filter;
        const fieldDef = fieldList.find((subF: any) => subF.id === fieldId);
        if (!fieldDef) {
          errors.push(`数据表${cubeCode}中不存在字段${fieldId}`);
        }
        if (!TIME_CONDITION_TYPE_ARR.includes(conditionType)) {
          errors.push(`时间过滤器的conditionType必须在${TIME_CONDITION_TYPE_ARR.join(',')}中`);
        }
        if (!TIME_FILTER_VARS.includes(value)) {
          errors.push(`时间过滤器的value必须在${TIME_FILTER_VARS.join(',')}中`);
        }
      });
      // 校验otherFilters是否在cubeList中
      otherFilters?.forEach((filter: any) => {
        const { fieldId, conditionType, value } = filter;
        const fieldDef = fieldList.find((subF: any) => subF.id === fieldId);
        if (!fieldDef) {
          errors.push(`数据表${cubeCode}中不存在过滤条件字段${fieldId}`);
        }
        if (['In', 'NotIn', 'InAny'].includes(conditionType) && !Array.isArray(value)) {
          if (typeof value === 'string' || typeof value === 'number') {
            // eslint-disable-next-line no-param-reassign
            filter.value = [value];
          } else {
            errors.push(`过滤条件字段${fieldId}的条件类型${conditionType}与条件值${value}不匹配`);
          }
        }
        const numType = ['GreaterThan', 'LessThan', 'GreaterThanOrEqualTo', 'LessThanOrEqualTo'];
        if (numType.includes(conditionType) && typeof value !== 'number') {
          errors.push(`过滤条件字段${fieldId}的条件类型${conditionType}与条件值${value}不匹配`);
        }
        if (['Like', 'NotLike'].includes(conditionType) && typeof value !== 'string') {
          errors.push(`过滤条件字段${fieldId}的条件类型${conditionType}与条件值${value}不匹配`);
        }
        if (!judgeNumField(fieldDef) && numType.includes(conditionType)) {
          errors.push(`过滤条件字段${fieldId}的数据类型非数字类型，不支持${conditionType}条件`);
        }
      });
    });
    return errors;
  }

  validateArgs(args: Partial<GenerateInterfaceArgs>): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    if (!args.appId) errors.push('应用ID不能为空，请指定应用ID');
    if (!args.interfaceList || !Array.isArray(args.interfaceList) || args.interfaceList.length === 0) {
      errors.push('需要生成的数据接口描述列表不能为空，请补充需要生成的数据接口描述列表');
    } else {
      args.interfaceList.forEach((item, idx) => {
        if (!item.description) errors.push(`第${idx + 1}个接口描述不能为空`);
        if (!item.cubeCode) errors.push(`第${idx + 1}个数据表ID不能为空`);
        if (!item.selectFields || !Array.isArray(item.selectFields) || item.selectFields.length === 0) {
          errors.push(`第${idx + 1}个接口的字段列表不能为空`);
        }
      });
    }
    return { valid: errors.length === 0, errors };
  }
}
