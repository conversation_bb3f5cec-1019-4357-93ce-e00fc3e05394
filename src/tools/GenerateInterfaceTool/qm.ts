/* eslint-disable @iceworks/best-practices/recommend-polyfill */
/* eslint-disable @typescript-eslint/indent */
import dayjs from 'dayjs';

export const TIME_CONDITION_TYPE_ARR = [
  'EqualTo',
  'NotEqualTo',
  'GreaterThan',
  'LessThan',
  'GreaterThanOrEqualTo',
  'LessThanOrEqualTo',
  'Between',
  'IsNull',
  'IsNotNull',
];

export const TIME_FILTER_VARS = [
  'THIS_YEAR',
  'THIS_MONTH',
  'LAST_MONTH',
  'NEARLY_3_MONTHS',
  'NEARLY_3_MONTHS_INCLUDE_CURRENT_MONTH',
  'NEARLY_6_MONTHS',
  'NEARLY_6_MONTHS_INCLUDE_CURRENT_MONTH',
  'NEARLY_12_MONTHS',
  'NEARLY_12_MONTHS_INCLUDE_CURRENT_MONTH',
  'TODAY',
  'YESTERDAY',
  'TOMORROW',
  'LAST_NATURAL_WEEK',
  'NEARLY_A_WEEK',
  'NEARLY_TWO_WEEK',
  'THIS_WEEK_TO_NOW',
  'NEARLY_30_DAYS',
  'THIS_MONTH_TO_NOW',
  'NEARLY_90_DAYS',
  'CURRENT_HOUR',
  'LAST_HOUR',
] as any;

export const CONDITION_TYPE_BETWEEN_ARR = [
  'NEARLY_3_MONTHS',
  'NEARLY_3_MONTHS_INCLUDE_CURRENT_MONTH',
  'NEARLY_6_MONTHS',
  'NEARLY_6_MONTHS_INCLUDE_CURRENT_MONTH',
  'NEARLY_12_MONTHS',
  'NEARLY_12_MONTHS_INCLUDE_CURRENT_MONTH',
  'LAST_NATURAL_WEEK',
  'NEARLY_A_WEEK',
  'NEARLY_TWO_WEEK',
  'THIS_WEEK_TO_NOW',
  'THIS_MONTH_TO_NOW',
  'NEARLY_30_DAYS',
  'NEARLY_90_DAYS',
  'CURRENT_HOUR',
  'LAST_HOUR',
] as any;

export interface OtherFilter {
  fieldId: string;
  conditionType:
    | 'EqualTo'
    | 'NotEqualTo'
    | 'In'
    | 'NotIn'
    | 'Like'
    | 'NotLike'
    | 'IsNull'
    | 'IsNotNull'
    | 'GreaterThan'
    | 'LessThan'
    | 'GreaterThanOrEqualTo'
    | 'LessThanOrEqualTo';
  value: any;
}
export interface TimeFilter {
  fieldId: string;
  conditionType:
    | 'EqualTo'
    | 'NotEqualTo'
    | 'GreaterThan'
    | 'LessThan'
    | 'GreaterThanOrEqualTo'
    | 'LessThanOrEqualTo'
    | 'Between';
  value:
    | 'THIS_YEAR'
    | 'THIS_MONTH'
    | 'LAST_MONTH'
    | 'NEARLY_3_MONTHS'
    | 'NEARLY_3_MONTHS_INCLUDE_CURRENT_MONTH'
    | 'NEARLY_6_MONTHS'
    | 'NEARLY_6_MONTHS_INCLUDE_CURRENT_MONTH'
    | 'NEARLY_12_MONTHS'
    | 'NEARLY_12_MONTHS_INCLUDE_CURRENT_MONTH'
    | 'TODAY'
    | 'YESTERDAY'
    | 'TOMORROW'
    | 'LAST_NATURAL_WEEK'
    | 'NEARLY_A_WEEK'
    | 'NEARLY_TWO_WEEK'
    | 'THIS_WEEK_TO_NOW'
    | 'NEARLY_30_DAYS'
    | 'THIS_MONTH_TO_NOW'
    | 'NEARLY_90_DAYS'
    | 'CURRENT_HOUR'
    | 'LAST_HOUR';
}

export function getFieldDefForFieldList(fieldObj: any, config = {} as any) {
  const tarItem = getRealFieldObj(fieldObj);
  const tarAliasName =
    tarItem.aggrText ||
    (judgeDateField(fieldObj) && tarItem !== fieldObj ? `${fieldObj.text}(${tarItem.text})` : tarItem.text);
  if (fieldObj.expression) {
    return {
      cubeCode: '',
      isDim: false,
      alias: fieldObj.alias || fieldObj.id,
      aliasName: { zh_CN: tarAliasName },
      classifiedCode: '',
      fieldCode: '',
      dataType: fieldObj.dataType,
      expression: fieldObj.expression,
      expressionWrap: fieldObj.expressionWrap,
      ...config,
      aggregateType: 'NONE',
      timeGranularityType: null as any,
    };
  }
  // 根据聚合类型和值类型做动态调整
  const aggregateType = config.aggregateType || 'NONE';
  const statisticsPattern = config.statisticsPattern || null;
  return {
    cubeCode: tarItem.cubeCode,
    isDim: tarItem.isDimension === 'true',
    alias: fieldObj.alias || fieldObj.id,
    aliasName: { zh_CN: tarAliasName },
    classifiedCode: tarItem.cubeCode,
    fieldCode: tarItem.fieldCode,
    dataType: tarItem.dataType,
    timeGranularityType: tarItem.timeGranularityType,
    ...config,
    aggregateType,
    statisticsPattern,
  };
}

export function judgeDateField(fieldObj: any) {
  if (!fieldObj) return false;
  if (fieldObj.dataType === 'DATE') return true;
  const { children } = fieldObj;
  if (!Array.isArray(children)) return false;
  return children.length === 6 && children[0].dataType === 'DATE' && children[0].timeGranularityType === 'YEAR';
}

export function getRealFieldObj(fieldObj: any) {
  // 找到真实目标字段配置
  const isDateField = judgeDateField(fieldObj);
  if (isDateField) {
    let tarItem = null as any;
    if (fieldObj?.children?.[0].children?.length) {
      tarItem = fieldObj.children[0].children;
    } else if (fieldObj?.children?.length) {
      tarItem = fieldObj.children;
    } else {
      tarItem = [fieldObj];
    }
    return tarItem?.find((item: any) => item.timeGranularityType === (fieldObj.timeGranularityType || 'DAY'));
  }
  return fieldObj;
}

// 判断是否数字类型
export function judgeNumField(field: any) {
  if (!field) return false;
  return ['INTEGER', 'DOUBLE'].includes(field.dataType);
}

export function genArrayFieldExpressionWrap(realFieldObj: any, aggregateType: string) {
  const expressFormulaName = aggregateType === 'COUNT_DISTINCT' ? 'COUNTDISTINCT' : 'COUNT';
  const expression = `${expressFormulaName}(ArrayToString(${realFieldObj.cubeCode}.${realFieldObj.id},','))`;
  return {
    source: `${expressFormulaName}(ArrayToString(#{${realFieldObj.cubeCode}.${realFieldObj.id}},','))`,
    display: `${expressFormulaName}(ArrayToString(${realFieldObj.text},','))`,
    expression,
  };
}

export function getTimeGranularityType(value: any) {
  if (!value) return null;
  switch (value) {
    case 'THIS_YEAR':
      return 'YEAR';
    case 'THIS_MONTH':
    case 'LAST_MONTH':
    case 'NEARLY_3_MONTHS':
    case 'NEARLY_3_MONTHS_INCLUDE_CURRENT_MONTH':
    case 'NEARLY_6_MONTHS':
    case 'NEARLY_6_MONTHS_INCLUDE_CURRENT_MONTH':
    case 'NEARLY_12_MONTHS':
    case 'NEARLY_12_MONTHS_INCLUDE_CURRENT_MONTH':
      return 'MONTH';
    case 'TODAY':
    case 'YESTERDAY':
    case 'TOMORROW':
    case 'LAST_NATURAL_WEEK':
    case 'NEARLY_A_WEEK':
    case 'NEARLY_TWO_WEEK':
    case 'THIS_WEEK_TO_NOW':
    case 'NEARLY_30_DAYS':
    case 'THIS_MONTH_TO_NOW':
    case 'NEARLY_90_DAYS':
      return 'DAY';
    case 'CURRENT_HOUR':
    case 'LAST_HOUR':
      return 'HOUR';
    default:
      if (Array.isArray(value) && value.length === 2) {
        const [start, end] = value;
        if (dayjs(end).diff(dayjs(start), 'minute') === 0) return 'MINUTE';
        if (dayjs(end).diff(dayjs(start), 'hour') === 0) return 'HOUR';
        if (dayjs(end).diff(dayjs(start), 'day') === 0) return 'DAY';
        if (dayjs(end).diff(dayjs(start), 'month') === 0) return 'MONTH';
        if (dayjs(end).diff(dayjs(start), 'year') >= 0) return 'YEAR';
      }
      return 'DAY';
  }
}

export function generateFilterAlias(tarItem: any) {
  return `${tarItem.cubeCode}_${tarItem.id}_${tarItem.isDimension === 'true'}`;
}

export function getFieldDefForFilter(fieldObj: any) {
  const tarItem = getRealFieldObj(fieldObj);
  const tarAliasName =
    tarItem.aggrText ||
    (judgeDateField(fieldObj) && tarItem !== fieldObj ? `${fieldObj.text}(${tarItem.text})` : tarItem.text);
  return {
    cubeCode: tarItem.cubeCode,
    isDim: tarItem.isDimension === 'true',
    alias: generateFilterAlias(tarItem),
    aliasName: tarAliasName,
    classifiedCode: tarItem.cubeCode,
    fieldCode: tarItem.fieldCode,
    dataType: fieldObj.id === 'yida_departments' ? 'ARRAY' : tarItem.dataType,
    timeGranularityType: tarItem.timeGranularityType,
    isFilterField: true,
  };
}

export function getTimeFilter(fieldObj: any, timeFilter: TimeFilter) {
  const tarItem = getRealFieldObj(fieldObj);
  let value = {} as any;
  // 矫正valueType
  const valueType = TIME_FILTER_VARS.includes(timeFilter.value) ? 'USER_VARIABLE' : 'ASSIGN_VALUE';
  const { conditionType } = timeFilter;
  if (['IsNull', 'IsNotNull'].includes(conditionType)) {
    value = null;
  } else if (valueType === 'USER_VARIABLE') {
    value = {
      timeType: conditionType === 'Between' ? 'TIMEINTERVAL' : 'TIME',
      timeSelectValue: timeFilter.value,
      value: [] as any,
      valueType,
    };
  } else {
    const tarValue =
      conditionType === 'Between'
        ? [dayjs(timeFilter.value[0]).valueOf(), dayjs(timeFilter.value[1]).valueOf()]
        : [dayjs(timeFilter.value).valueOf()];
    value = {
      timeType: 'TIMEINTERVAL',
      value: tarValue,
      valueType,
    };
  }
  return {
    filterKey: 'filter_time_1',
    filterType: 'inside',
    alias: generateFilterAlias(tarItem),
    value,
    conditionType,
  };
}

export function getSingleFilter(fieldObj: any, singleFilter: any) {
  const tarItem = getRealFieldObj(fieldObj);
  const valueType = 'ASSIGN_VALUE';
  let { conditionType } = singleFilter;
  let tarValue = '' as any;
  if (judgeNumField(fieldObj) && typeof singleFilter.value === 'number') {
    tarValue = [`${singleFilter.value}`];
  } else if (
    Array.isArray(singleFilter.value) &&
    tarItem.dataType === 'STRING' &&
    !['In', 'InAny', 'NotIn'].includes(conditionType)
  ) {
    tarValue = [JSON.stringify(singleFilter.value)];
  } else if (Array.isArray(singleFilter.value)) {
    tarValue = singleFilter.value;
    if (tarItem.dataType === 'ARRAY') {
      conditionType = 'InAny';
    }
  } else {
    tarValue = [singleFilter.value];
  }

  const tarValueObj = ['IsNull', 'IsNotNull'].includes(conditionType)
    ? null
    : {
        value: tarValue,
        valueType,
      };
  return {
    filterKey: 'filter_single_1',
    filterType: 'inside',
    alias: generateFilterAlias(tarItem),
    value: tarValueObj,
    conditionType,
  };
}
