import { pick } from 'lodash';

import { createFormInstance, searchDepartmentList, searchEmployeeList } from '@/apis';
import { LLMService } from '@/services/llmService';
import { extractCode } from '@/tools/GenerateCodeTool/code';
import { streamType } from '@/types/types';
import { log } from '@/utils/decorators/log';
import { ToolExecutionError } from '@/utils/Errors';
import { MessageUtils } from '@/utils/Message';
import { ToolValidator } from '@/utils/Validator';
import { FIELD_INFO, IAppContext, IFieldInfo, queryAppContext } from '@/utils/YidaMeta';

import { BaseTool } from '../../utils/Tool';

// 构建应用的mock数据
interface GenerateMockDataArgs {
  // 应用ID
  appId: string;
  //
  desc?: string;
}

const mockPrompt = `{{render "tools.generate-mockdata"}}`;

const address_mock_data = [
  {
    regionIds: [110000, 110100, 110105, 110105009],
    address: '亚运村',
    regionText: [
      { zh_CN: '北京', en_US: 'bei jing' },
      { zh_CN: '北京市', en_US: 'bei jing shi' },
      { zh_CN: '朝阳区', en_US: 'chao yang qu' },
      { zh_CN: '亚运村街道', en_US: 'ya yun cun jie dao' },
    ],
  },
  {
    regionIds: [330000, 330100, 330110, 330110005],
    address: '未来Park园区',
    regionText: [
      { zh_CN: '浙江省', en_US: 'zhe jiang sheng' },
      { zh_CN: '杭州市', en_US: 'hang zhou shi' },
      { zh_CN: '余杭区', en_US: 'yu hang qu' },
      { zh_CN: '五常街道', en_US: 'wu chang jie dao' },
    ],
  },
  {
    regionIds: [810000, 810200, 810203],
    address: '维多利亚港',
    regionText: [
      { zh_CN: '香港特别行政区', en_US: 'xiang gang te bie xing zheng qu' },
      { zh_CN: '九龙', en_US: 'jiu long' },
      { zh_CN: '深水埗区', en_US: 'shen shui bu qu' },
    ],
  },
  {
    regionIds: [420000, 420100, 420106, 420106012],
    address: '武汉大学',
    regionText: [
      { zh_CN: '湖北省', en_US: 'hu bei sheng' },
      { zh_CN: '武汉市', en_US: 'wu han shi' },
      { zh_CN: '武昌区', en_US: 'wu chang qu' },
      { zh_CN: '水果湖街街道', en_US: 'shui guo hu jie jie dao' },
    ],
  },
  {
    regionIds: [310000, 310100, 310115, 310115103],
    address: '迪斯尼游乐园',
    regionText: [
      { zh_CN: '上海', en_US: 'shang hai' },
      { zh_CN: '上海市', en_US: 'shang hai shi' },
      { zh_CN: '浦东新区', en_US: 'pu dong xin qu' },
      { zh_CN: '川沙新镇', en_US: 'chuan sha xin zhen' },
    ],
  },
];
/**
 * 生成应用的Mock数据工具
 */
export class GenerateMockDataTool extends BaseTool {
  name = 'generate_mock_data';
  nameCn = '生成Mock数据';
  description = '生成应用的Mock数据， 当所有应用中的表单完成创建后使用，同一个应用只能生成一次Mock数据。';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用唯一标识',
      },
      desc: {
        type: 'string',
        description: '应用描述，为了更好的生成Mock数据，请尽量详细描述应用的功能和使用场景',
      },
    },
    required: ['appId'],
  };

  generateMockData = async (appContext: IAppContext, desc: string) => {
    const llmService = new LLMService();
    const userPrompt = `
        应用描述：${desc}
        数据模型结构：
        ${JSON.stringify(appContext)}

        请为应用生成Mock数据
      `;
    const mockRes = await llmService.askStream<{
      fieldsInfo: string;
    }>(
      [MessageUtils.userMessage(userPrompt)],
      [MessageUtils.systemMessage(mockPrompt)],
      [],
      'none',
      (type: streamType, data: any) => {
        if (type === streamType.REASONING) {
          this.feedbackLogger.thinking(data);
        } else if (type === streamType.THINKING) {
          this.feedbackLogger.toolResult(data);
        }
      },
      true,
      {
        context: {
          fieldsInfo: `${Object.entries(FIELD_INFO)
            .map(([key, value]) => `- ${key}：${value.desc}(示例：${value.valueExample})`)
            .join('\n')}`,
        },
      },
    );
    const jsonCode = extractCode(mockRes.content, 'json');
    const mockData = JSON.parse(jsonCode?.[0]?.content || '[]');
    return mockData;
  };

  generateFormData = async (mockData: any, fieldList: IFieldInfo[], allData: any) => {
    const formData: any = {};
    for (const field of fieldList) {
      const { fieldId, type, relatePageInfo, subFields } = field;
      const fieldValue = mockData[fieldId];
      if (type === 'EmployeeField') {
        const { list = [] } = await searchEmployeeList();
        const startIndex: number = Math.floor(Math.random() * list.length);
        formData[fieldId] = (fieldValue || []).map(
          (val: any, idx: number) => list[(startIndex + idx) % list.length].emplId,
        );
        // 如果有bind的字段，则将bind字段的值设置为对应的name
        const bindField = fieldList.find((item) => item.bind && item.bind === `${fieldId}.name`);
        if (bindField) {
          const employeeName = list[startIndex].name;
          mockData[bindField.fieldId] = employeeName;
          formData[bindField.fieldId] = employeeName;
        }
      } else if (type === 'DepartmentSelectField') {
        const { list = [] } = await searchDepartmentList();
        const startIndex: number = Math.floor(Math.random() * list.length);
        formData[fieldId] = (fieldValue || []).map(
          (val: any, idx: number) => list[(startIndex + idx) % list.length].emplId,
        );
      } else if (type === 'AssociationFormField') {
        const targetData = allData[relatePageInfo.formUuid].filter((item: any) => item.formInstanceId);
        const targetIndex = Math.floor(Math.random() * targetData.length);
        formData[fieldId] = [
          {
            ...pick(relatePageInfo, ['appType', 'formUuid', 'formType', 'formTitle']),
            instanceId: targetData[targetIndex].formInstanceId,
            title: targetData?.[targetIndex]?.[relatePageInfo.mainFieldId] || '',
          },
        ];
      } else if (type === 'CountrySelectField') {
        formData[fieldId] = [
          {
            value: 'CN',
            text: {
              en_US: 'China',
              zh_CN: '中国',
              type: 'i18n',
            },
          },
        ];
      } else if (type === 'AddressField') {
        formData[fieldId] = address_mock_data[Math.floor(Math.random() * 5)];
      } else if (type === 'TableField') {
        formData[fieldId] = [];
        for (const subField of fieldValue) {
          const subFieldValue = await this.generateFormData(subField, subFields, allData);
          formData[fieldId].push(subFieldValue);
        }
      } else if (type === 'SerialNumberField') {
        delete formData[fieldId];
      } else if (fieldValue) {
        formData[fieldId] = fieldValue;
      }
    }
    return formData;
  };

  saveMockData = async (appId: string, mockData: any, appContext: IAppContext) => {
    const { pageList } = appContext;
    for (const page of pageList) {
      const { pageId, fieldList } = page;
      const mockList = mockData[pageId];
      for (const mockItem of mockList) {
        const formData = await this.generateFormData(mockItem, fieldList, mockData);
        const { formInstanceId } = await createFormInstance({
          appType: appId,
          formUuid: pageId,
          formData,
        });
        mockItem.formInstanceId = formInstanceId;
      }
    }
  };

  /**
   * 修改表单
   */
  @log()
  async execute(args: GenerateMockDataArgs): Promise<any> {
    try {
      const { appId, desc } = args;
      const appContext = await queryAppContext(appId, ['receipt', 'process']);
      const mockData = await this.generateMockData(appContext, desc);
      await this.saveMockData(appId, mockData, appContext);
      console.info('[💽 Mock data generated]', mockData);
      return {
        status: '✅',
        appId,
        message: 'Mock数据生成成功',
        data: JSON.stringify(mockData, null, 2),
      };
    } catch (error) {
      this.logger.error(`更新表单失败: ${error}`);
      throw new ToolExecutionError(this.name, `更新表单失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.appId) {
      errors.push('应用唯一标识不能为空');
    }

    return ToolValidator.validate(this.parameters, args);
  }
}
