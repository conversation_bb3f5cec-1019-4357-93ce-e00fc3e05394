import { ToolExecutionError } from '@/utils/Errors';
import { ToolValidator } from '@/utils/Validator';
import { BaseTool } from '../../utils/Tool';
import { LLMService } from '@/services/llmService';
import { MessageUtils } from '@/utils/Message';
import { streamType } from '@/types/types';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { log } from '@/utils/decorators/log';

// 生成PRD的参数
interface GeneratePRDArgs {
  // 上下文
  context: string;
}

/**
 * 生成PRD工具
 * 使用 deepseek-r1 模型根据应用描述生成详细的PRD文档
 */
export class GeneratePRDTool extends BaseTool {
  name = 'generate_prd';
  nameCn = '生成PRD';
  description = '根据用户需求生成产品需求文档(PRD)';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appName: {
        type: 'string',
        description: '应用名称，如果用户没有明确指定，请询问用户',
      },
      context: {
        type: 'string',
        description: '用户需求描述的上下文',
      },
    },
    required: ['context', 'appName'],
  };

  /**
   * 生成PRD文档
   * @param args 生成PRD的参数
   * @returns 生成的PRD文档
   */
  @log()
  async execute(args: GeneratePRDArgs): Promise<string> {
    try {
      this.logger.info('开始生成PRD文档');
      const { context } = args;

      const messages = [{ role: 'user', content: context }] as any;

      // 构建提示词
      const systemPrompt = `
## 角色
请根据用户的应用需求描述，请生成一份结构清晰、内容完整的PRD文档。

## 上下文
- 现在时间是: ${new Date().toLocaleDateString()}
- 当前助理仅支持生成以下2种类型的智能应用，请根据需求选择合适的类型。
  - 普通表单类智能应用：用于收集信息、数据或意见，可设计多种字段类型，支持数据存储、分析、播报
  - 图片智能识别类智能应用：用于智能识别上传的图片，并提取信息填入到表单，支持数据存储、分析、播报
- 目前支持的应用为宜搭低代码应用而非源码开发的应用，总体应用支持的页面类型和功能，以及应用生成的顺序如下：
- 支持对需求进行分析，产出不少于300字的应用设计PRD（根据PRD指导完成后续步骤）
- 支持设计应用图标
- 支持创建应用
- 支持表单页面设计&创建
- 支持首页设计&创建
- 支持应用助理创建
  - 支持为应用AI助理的表单开启/关闭技能(chatBI和chatForm)
  - 支持生成数据洞察的推送

## 要求
- 深度分析需求，并且识别核心功能 和 隐含期望，体现应用的潜在价值
- 应用PRD文档需要与上述步骤紧密相关，按照步骤产出详细功能说明、包括不限于生成后续表单设计、首页设计、AI助理配置等完整PRD
- 表单字段设计需要优先满足用户指定需要的字段，再根据实际使用场景进行字段的合理扩充，以更好的满足实际场景表单对于数据的收集，发挥表单数据的价值挖掘，同时避免字段冗余
- 对于图片识别类需求，应使用图片智能识别类型的表单，并确保图片上传作为首个字段
- 首页设计应根据当前应用场景和功能描述来设计，首页要有对应的入口链接到表单页面
- PRD不要包含测试、发布、推广等其他内容，不要包含PRD文件头、文件尾等，直接输出核心内容
`;
      const systemMsg = [MessageUtils.systemMessage(systemPrompt)];
      // 调用 deepseek-r1 模型
      const llm = new LLMService({
        model: 'deepseek-reasoner',
      });
      const prd = await llm.askStream(messages, systemMsg, [], 'none', (type: streamType, data: any) => {
        if (type === streamType.REASONING) {
          this.feedbackLogger.thinking(data);
        } else if (type === streamType.THINKING) {
          this.feedbackLogger.toolResult(data);
        }
      });
      this.logger.info('PRD文档生成成功');
      useAIAgentStateStore.getState().setGeneralPRD(prd.content);
      return prd.content;
    } catch (error) {
      this.logger.error(`PRD文档生成失败: ${error}`);
      throw new ToolExecutionError(this.name, `PRD文档生成失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    return ToolValidator.validate(this.parameters, args);
  }
}
