import { searchCubeList } from '@/apis/searchCubeList';
import { BaseTool } from '../../utils/Tool';
import { ToolSchema } from '@/types/types';
import { getCubeFieldList, judgeDateField } from '@/apis/getCubeFieldList';
import { log } from '@/utils/decorators/log';

export interface GetCubesInfoArgs {
  appId: string;
}

export interface GetCubesInfoResponse {
  cubeCode: string;
  cubeName: string;
  fieldList: Array<{
    fieldId: string;
    comment: string;
    dataType: 'STRING' | 'INTEGER' | 'DOUBLE' | 'DATE' | 'ARRAY' | 'NONE';
  }>;
}

export class GetCubesInfoTool extends BaseTool {
  name = 'get_cubes_info';
  nameCn = '获取数据表信息';
  description = '一个获取当前应用数据表及字段信息的工具。';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      appId: { type: 'string', description: '应用ID, 通常为APP_开头的字符串' },
    },
    required: ['appId'],
  };

  @log()
  async execute(args: GetCubesInfoArgs): Promise<GetCubesInfoResponse[]> {
    const { appId } = args;
    const cubeList = await searchCubeList({ appType: appId, cubeSource: 'YIDA_FORM' });
    if (!cubeList?.length) {
      // throw new Error(`未找到数据表，请检查应用ID${appId}是否正确, 并确认数据表是否已创建`);
      console.warn(`未找到数据表，请检查应用ID${appId}是否正确, 并确认数据表是否已创建`);
      return [];
    }
    // 获取字段列表
    const cubeCodes = cubeList.map((cube: any) => cube.code);
    const fieldDefListMap = await getCubeFieldList({ appType: appId, cubeCodes });
    // 构建数据库表、字段信息
    return Object.keys(fieldDefListMap).map((cubeCode: string) => {
      const cube = cubeList.find((c: any) => c.code === cubeCode);
      const fieldList = fieldDefListMap[cubeCode].map((fieldDef: any) => ({
        fieldId: fieldDef.id,
        comment: fieldDef.text,
        dataType: judgeDateField(fieldDef) ? 'DATE' : fieldDef.dataType,
      }));
      return {
        cubeCode,
        cubeName: cube.name,
        fieldList,
      };
    });
  }

  validateArgs(args: Partial<GetCubesInfoArgs>): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    if (!args.appId) errors.push('应用ID不能为空，请指定应用ID');
    return { valid: errors.length === 0, errors };
  }
}
