import { ToolSchema } from '@/types/types';
import { ToolValidator } from '@/utils/Validator';
import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

interface MessageNotifyArgs {
  text: string;
  attachments?: string | string[];
}

interface MessageAskArgs {
  text: string;
  attachments?: string | string[];
  suggest_user_takeover?: 'none' | 'browser';
}

/**
 * 向用户发送通知消息，不需要用户回应
 */
export class MessageNotifyTool extends BaseTool {
  name = 'message_notify';
  nameCn = '发送通知消息';
  description = '发送通知消息给用户';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      text: {
        type: 'string',
        description: '要显示给用户的消息文本',
      },
      attachments: {
        type: 'string',
        description: '（可选）要显示给用户的附件列表，可以是文件路径或URL。可以是字符串或字符串数组。',
      },
    },
    required: ['text'],
  };
  @log()
  async execute(args: MessageNotifyArgs): Promise<string> {
    try {
      this.logger.info('开始发送通知消息');
      const validation = this.validateArgs(args);
      if (!validation.valid) {
        throw new ToolExecutionError(this.name, `参数验证失败: ${validation.errors?.join(', ')}`);
      }

      const { text, attachments } = args;

      this.logger.info(`[通知消息] ${text}`);

      if (attachments) {
        const attachmentList = Array.isArray(attachments) ? attachments : [attachments];
        this.logger.info(`[附件] ${attachmentList.join(', ')}`);
      }

      const result = `通知已发送: ${text}`;
      this.logger.info('通知消息发送成功');
      return result;
    } catch (error) {
      this.logger.error(`通知消息发送失败: ${error}`);
      throw new ToolExecutionError(this.name, `通知消息发送失败: ${error}`);
    }
  }
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    if (args.attachments && !this.isValidAttachments(args.attachments)) {
      return {
        valid: false,
        errors: ['attachments必须是字符串或字符串数组'],
      };
    }
    return ToolValidator.validate(this.parameters, args);
  }
  private isValidAttachments(attachments: any): boolean {
    if (typeof attachments === 'string') return true;
    if (Array.isArray(attachments)) {
      return attachments.every((item) => typeof item === 'string');
    }
    return false;
  }
}

/**
 * 向用户提问并等待回应
 */
export class MessageAskUserTool extends BaseTool {
  name = 'message_ask_user';
  nameCn = '询问用户';
  description = '向用户发送问题并等待回答';

  parameters: ToolSchema = {
    type: 'object',
    properties: {
      text: {
        type: 'string',
        description: '要呈现给用户的问题文本',
      },
      attachments: {
        type: 'string',
        description: '（可选）与问题相关的文件或参考材料。可以是字符串或字符串数组。',
      },
    },
    required: ['text'],
  };
  @log()
  async execute(args: MessageAskArgs): Promise<string> {
    try {
      this.logger.info('开始向用户发送问题');
      const validation = this.validateArgs(args);
      if (!validation.valid) {
        throw new ToolExecutionError(this.name, `参数验证失败: ${validation.errors?.join(', ')}`);
      }

      const { text, attachments, suggest_user_takeover } = args;

      this.logger.info(`[提问] ${text}`);

      if (attachments) {
        const attachmentList = Array.isArray(attachments) ? attachments : [attachments];
        this.logger.info(`[附件] ${attachmentList.join(', ')}`);
      }

      if (suggest_user_takeover) {
        this.logger.info(`[建议用户接管] ${suggest_user_takeover}`);
      }

      // 创建一个新的Promise来处理用户回复
      const result = new Promise<string>((resolve) => {
        if (typeof globalThis !== 'undefined') {
          // 设置30秒的倒计时
          const timeoutId = setTimeout(() => {
            this.logger.info('用户30秒内未响应，由助理代为决策');
            resolve('用户30秒内未响应，由助理代为决策');
          }, 30000);

          // 存储回调函数到全局上下文
          globalThis.waitingForUserResponse = {
            resolve: (userResponse: string) => {
              // 清除倒计时
              clearTimeout(timeoutId);
              // 当用户回复时，继续流程
              this.logger.info(`[用户回应] ${userResponse}`);
              resolve(`用户回应: ${userResponse}`);
            },
            onInputStart: () => {
              // 当用户开始输入时，清除倒计时
              clearTimeout(timeoutId);
              this.logger.info('用户开始输入，取消倒计时');
            },
            question: text,
            isDirectResponse: true,
            timeout: 30000, // 添加超时时间信息
          };
        } else {
          // 如果没有全局上下文，使用默认回复
          this.logger.warning('无法找到全局上下文来存储回调，使用默认回复');
          setTimeout(() => {
            resolve('用户回应: 默认回复，未能等待真实用户输入');
          }, 1000);
        }
      });

      this.logger.info('用户问题发送成功');
      return result;
    } catch (error) {
      this.logger.error(`用户问题发送失败: ${error}`);
      throw new ToolExecutionError(this.name, `用户问题发送失败: ${error}`);
    }
  }
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    if (args.attachments && !this.isValidAttachments(args.attachments)) {
      return {
        valid: false,
        errors: ['attachments必须是字符串或字符串数组'],
      };
    }
    return ToolValidator.validate(this.parameters, args);
  }
  private isValidAttachments(attachments: any): boolean {
    if (typeof attachments === 'string') return true;
    if (Array.isArray(attachments)) {
      return attachments.every((item) => typeof item === 'string');
    }
    return false;
  }
}
