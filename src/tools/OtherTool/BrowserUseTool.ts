import { Tool, ToolSchema } from '@/types/types';
import axios from 'axios';
import { ToolValidator } from '@/utils/Validator';
import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

interface BrowserUseArgs {
  url: string;
  action: 'get' | 'extract';
  selector?: string;
}

export class BrowserUseTool extends BaseTool {
  name = 'browser_use';
  nameCn = '浏览器操作';
  description = '与网页交互并提取信息，支持获取页面内容和提取页面元素';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      url: {
        type: 'string',
        description: '(必填) 要访问的网页URL',
      },
      action: {
        type: 'string',
        description: '(必填) 执行的操作类型: "get" 获取整个页面内容, "extract" 使用选择器提取特定内容',
        enum: ['get', 'extract'],
      },
      selector: {
        type: 'string',
        description: '(extract操作必填) 用于提取页面内容的CSS选择器',
      },
    },
    required: ['url', 'action'],
  };

  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    return ToolValidator.validate(this.parameters, args);
  }

  @log()
  async execute(args: BrowserUseArgs): Promise<string> {
    try {
      this.logger.info('开始执行浏览器操作');
      // 验证参数
      const validation = this.validateArgs(args);
      if (!validation.valid) {
        throw new Error(`参数验证失败: ${validation.errors?.join(', ')}`);
      }

      const { url, action, selector } = args;

      let result: string;
      switch (action) {
        case 'get': {
          // 使用 CORS 代理来避免跨域问题
          const corsProxy = 'https://corsproxy.io/?';
          const response = await axios.get(`${corsProxy}${encodeURIComponent(url)}`);
          result = response.data;
          break;
        }
        case 'extract': {
          if (!selector) {
            throw new Error('提取操作需要提供选择器参数');
          }

          // 使用 CORS 代理来避免跨域问题
          const corsProxy = 'https://corsproxy.io/?';
          const response = await axios.get(`${corsProxy}${encodeURIComponent(url)}`);

          // 在浏览器环境中使用 DOMParser 解析 HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.data, 'text/html');

          // 使用选择器提取内容
          const elements = doc.querySelectorAll(selector);
          if (elements.length === 0) {
            result = '没有找到匹配的元素';
          } else {
            // 提取文本内容
            const results = Array.from(elements)
              .map((el) => el.textContent?.trim())
              .filter(Boolean);
            result = results.join('\n');
          }
          break;
        }
        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      this.logger.info('浏览器操作执行成功');
      return result;
    } catch (error) {
      this.logger.error(`浏览器操作失败: ${error}`);
      throw new ToolExecutionError(this.name, `浏览器操作失败: ${error}`);
    }
  }
}
