import { Tool } from '@/types/types';
import { log } from '@/utils/decorators/log';

/**
 * 创建结构化回复参数
 * 可以指定响应内容或其他自定义字段
 */
export interface ChatCompletionArgs {
  /**
   * 要返回给用户的回复内容
   * 可以是字符串或复杂的结构化数据
   */
  response: string | Record<string, any>;
  [key: string]: any;
}

/**
 * 清理JSON字符串中的非法控制字符
 * @param jsonString JSON字符串
 * @returns 清理后的JSON字符串
 */
function sanitizeJsonString(jsonString: string): string {
  if (typeof jsonString !== 'string') {
    return JSON.stringify(jsonString);
  }

  // 移除JSON中不允许的控制字符
  // ASCII控制字符(0-31)中，只有\t, \n, \r是允许的
  return jsonString.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, '');
}

/**
 * 创建回复工具
 * 用于代理直接向用户提供结构化回复，而不是执行其他工具
 */
export class CreateChatCompletion implements Tool {
  name = 'create_chat_completion';
  nameCn = '创建结构化回复';
  description = '创建结构化回复，用于向用户提供直接的回答或响应';

  /**
   * 创建回复的参数定义
   */
  parameters = {
    type: 'object',
    properties: {
      response: {
        type: 'string',
        description: '要返回给用户的回复内容',
      },
    },
    required: ['response'],
  };

  /**
   * 执行工具，返回提供的响应
   * @param args 回复参数
   * @returns 响应内容
   */
  @log()
  async execute(args: ChatCompletionArgs): Promise<string | Record<string, any>> {
    try {
      // 如果参数为空或无效，返回默认消息
      if (!args || typeof args !== 'object') {
        return '提供的参数无效，无法创建回复';
      }

      // 如果response不存在
      if (args.response === undefined) {
        return '未提供回复内容';
      }

      // 如果是字符串，直接返回
      if (typeof args.response === 'string') {
        return args.response;
      }

      // 如果是复杂对象，尝试安全地序列化为JSON
      if (typeof args.response === 'object') {
        try {
          // 先转为JSON字符串，再清理，然后进行解析验证
          const jsonStr = JSON.stringify(args.response);
          const cleanedJson = sanitizeJsonString(jsonStr);

          // 验证清理后的JSON是否有效
          JSON.parse(cleanedJson);

          return args.response;
        } catch (e) {
          console.error('序列化复杂对象失败:', e);
          return `无法处理复杂对象: ${e.message}`;
        }
      }

      return String(args.response);
    } catch (e) {
      console.error('CreateChatCompletion执行出错:', e);
      return `执行create_chat_completion时出错: ${e.message}`;
    }
  }
}
