import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import * as fs from 'fs/promises';
import * as path from 'path';
import { log } from '@/utils/decorators/log';

interface FileSaverArgs {
  content: string;
  filename: string;
  directory?: string;
  mode?: number;
}

export class FileSaver extends BaseTool {
  name = 'file_saver';
  nameCn = '文件保存';
  description = 'Save content to a file';

  @log()
  async execute(args: FileSaverArgs): Promise<string> {
    try {
      this.logger.info('开始保存文件');
      const { content, filename, directory = '.', mode = 0o644 } = args;
      // 确保目录存在
      await fs.mkdir(directory, { recursive: true });
      const filePath = path.join(directory, filename);
      // 写入文件
      await fs.writeFile(filePath, content, { mode });
      this.logger.info('文件保存成功');
      return `File saved successfully: ${filePath}`;
    } catch (error) {
      this.logger.error(`文件保存失败: ${error}`);
      throw new ToolExecutionError(this.name, `Failed to save file: ${error}`);
    }
  }

  // 检查文件是否存在
  async fileExists(filepath: string): Promise<boolean> {
    try {
      await fs.access(filepath);
      return true;
    } catch {
      return false;
    }
  }

  // 读取文件内容
  async readFile(filepath: string): Promise<string> {
    try {
      return await fs.readFile(filepath, 'utf8');
    } catch (error) {
      throw new ToolExecutionError(this.name, `Failed to read file: ${error}`);
    }
  }

  // 删除文件
  async deleteFile(filepath: string): Promise<void> {
    try {
      await fs.unlink(filepath);
    } catch (error) {
      throw new ToolExecutionError(this.name, `Failed to delete file: ${error}`);
    }
  }
}
