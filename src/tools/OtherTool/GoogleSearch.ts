import { ToolSchema } from '@/types/types';
import { ToolValidator } from '@/utils/Validator';
import axios from 'axios';
import { getConfig } from '@/config';
import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool, BaseToolConfig } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

interface GoogleSearchArgs {
  query: string;
  numResults?: number;
}

interface SearchResult {
  title: string;
  link: string;
  snippet: string;
}

export class GoogleSearch extends BaseTool {
  name = 'google_search';
  nameCn = 'Google搜索';
  description = 'Search Google and return results';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      query: {
        type: 'string',
        description: '(required) The search query to submit to Google.',
      },
      numResults: {
        type: 'integer',
        description: '(optional) The number of search results to return. Default is 10.',
        default: 10,
      },
    },
    required: ['query'],
  };

  private apiKey: string;
  private searchEngineId: string;

  constructor(config?: BaseToolConfig) {
    super(config);
    const sysConfig = getConfig();
    this.apiKey = sysConfig.googleApiKey || '';
    this.searchEngineId = sysConfig.googleSearchEngineId || '';

    if (!this.apiKey || !this.searchEngineId) {
      this.logger.warning('Google Search API credentials not found in environment variables');
    }
  }

  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    return ToolValidator.validate(this.parameters, args);
  }

  @log()
  async execute(args: GoogleSearchArgs): Promise<SearchResult[]> {
    try {
      this.logger.info('开始执行 Google 搜索');
      // 验证参数
      const validation = this.validateArgs(args);
      if (!validation.valid) {
        throw new Error(`参数验证失败: ${validation.errors?.join(', ')}`);
      }

      if (!this.apiKey || !this.searchEngineId) {
        throw new Error('Google Search API credentials not configured');
      }

      const { query, numResults = 10 } = args;

      const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
        params: {
          key: this.apiKey,
          cx: this.searchEngineId,
          q: query,
          num: numResults,
        },
      });

      if (!response.data.items || !Array.isArray(response.data.items)) {
        return [];
      }

      const results = response.data.items.map((item: any) => ({
        title: item.title || '',
        link: item.link || '',
        snippet: item.snippet || '',
      }));

      this.logger.info('Google 搜索执行成功');
      return results;
    } catch (error) {
      this.logger.error(`Google 搜索失败: ${error}`);
      throw new ToolExecutionError(this.name, `Google search failed: ${error}`);
    }
  }
}
