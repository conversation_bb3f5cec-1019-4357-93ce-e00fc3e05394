import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { exec } from 'child_process';
import { promisify } from 'util';
import { log } from '@/utils/decorators/log';

const execAsync = promisify(exec);

interface PythonExecuteArgs {
  code: string;
  timeout?: number;
}

export class PythonExecute extends BaseTool {
  name = 'python_execute';
  nameCn = 'Python执行';
  description = 'Execute Python code and return the result';

  @log()
  async execute(args: PythonExecuteArgs): Promise<string> {
    try {
      this.logger.info('开始执行 Python 代码');
      const { code, timeout = 30000 } = args;
      const { stdout, stderr } = await execAsync(`python -c "${code}"`, {
        timeout,
        encoding: 'utf8',
      });

      if (stderr) {
        throw new Error(stderr);
      }

      this.logger.info('Python 代码执行成功');
      return stdout;
    } catch (error) {
      this.logger.error(`Python 代码执行失败: ${error}`);
      throw new ToolExecutionError(this.name, `Python execution failed: ${error}`);
    }
  }
}
