import { ToolSchema, PlanStepStatus, PlanData, PlanStorage } from '@/types/types';
import { log } from '@/utils/decorators/log';

/**
 * 内存计划存储实现
 */
class MemoryPlanStorage implements PlanStorage {
  private plans: Map<string, PlanData> = new Map();

  async savePlan(planId: string, planData: PlanData): Promise<void> {
    this.plans.set(planId, { ...planData });
  }

  async loadPlan(planId: string): Promise<PlanData | null> {
    const plan = this.plans.get(planId);
    return plan ? { ...plan } : null;
  }

  async listPlans(): Promise<string[]> {
    return Array.from(this.plans.keys());
  }

  async deletePlan(planId: string): Promise<void> {
    this.plans.delete(planId);
  }
}

export class PlanningTool {
  name = 'planning';
  nameCn = '计划管理';
  description = '一个用于管理和执行计划的工具';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      command: {
        type: 'string',
        enum: ['create', 'update', 'list', 'get', 'set_active', 'mark_step', 'delete'],
        description: '要执行的命令，必须要有这个参数，否则会报错',
      },
      planId: {
        type: 'string',
        description: '计划的唯一标识符',
      },
      title: {
        type: 'string',
        description: '计划的标题',
      },
      steps: {
        type: 'array',
        items: {
          type: 'string',
        },
        description: '计划的步骤列表',
      },
      stepIndex: {
        type: 'number',
        description: '步骤的索引',
      },
      stepStatus: {
        type: 'string',
        enum: ['pending', 'in_progress', 'completed', 'failed'],
        description: '步骤的状态',
      },
      stepNotes: {
        type: 'string',
        description: '步骤的备注',
      },
    },
    required: ['command'],
  };
  private currentPlanId: string | null = null;
  private storage: PlanStorage;

  constructor() {
    this.storage = new MemoryPlanStorage();
  }

  toParam(): ToolSchema {
    return {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          enum: ['create', 'update', 'list', 'get', 'set_active', 'mark_step', 'delete'],
          description: '要执行的命令，必须要有这个参数，否则会报错',
        },
        planId: {
          type: 'string',
          description: '计划的唯一标识符',
        },
        title: {
          type: 'string',
          description: '计划标题（用于 create/update 命令）',
        },
        steps: {
          type: 'array',
          items: {
            type: 'string',
          },
          description: '计划中的步骤列表（用于 create/update 命令）',
        },
        stepIndex: {
          type: 'number',
          description: '要标记的步骤索引（用于 mark_step 命令）',
        },
        stepStatus: {
          type: 'string',
          enum: [
            PlanStepStatus.NOT_STARTED,
            PlanStepStatus.IN_PROGRESS,
            PlanStepStatus.COMPLETED,
            PlanStepStatus.BLOCKED,
          ],
          description: '步骤的状态（用于 mark_step 命令）',
        },
        stepNotes: {
          type: 'string',
          description: '步骤的附加说明（用于 mark_step 命令）',
        },
      },
      required: ['command'],
    };
  }

  @log()
  async execute(args: {
    command: string;
    planId?: string;
    title?: string;
    steps?: string[];
    stepIndex?: number;
    stepStatus?: PlanStepStatus;
    stepNotes?: string;
  }): Promise<string> {
    try {
      switch (args.command) {
        case 'create':
          return this.createPlan(args);
        case 'update':
          return this.updatePlan(args);
        case 'list':
          return this.listPlans();
        case 'get':
          return this.getPlan(args.planId);
        case 'set_active':
          return this.setActivePlan(args.planId);
        case 'mark_step':
          return this.markStep(args);
        case 'delete':
          return this.deletePlan(args.planId);
        default:
          throw new Error(`未知的命令: ${args.command}`);
      }
    } catch (error) {
      this.logger.error(`执行计划工具时出错: ${error}`);
      throw error;
    }
  }

  @log()
  private async createPlan(args: { planId?: string; title?: string; steps?: string[] }): Promise<string> {
    if (!args.planId) {
      throw new Error('创建计划需要 planId 参数');
    }

    const existingPlan = await this.storage.loadPlan(args.planId);
    if (existingPlan) {
      throw new Error(`计划 ID '${args.planId}' 已存在。请使用 update 命令修改现有计划。`);
    }

    if (!args.title) {
      throw new Error('创建计划需要 title 参数');
    }

    if (!args.steps || !Array.isArray(args.steps) || args.steps.length === 0) {
      throw new Error('创建计划需要非空的 steps 数组');
    }

    const planData: PlanData = {
      title: args.title,
      steps: args.steps,
      stepStatuses: Array(args.steps.length).fill(PlanStepStatus.NOT_STARTED),
      stepNotes: Array(args.steps.length).fill(''),
    };
    await this.storage.savePlan(args.planId, planData);
    this.currentPlanId = args.planId;

    return `计划创建成功，ID: ${args.planId}\n\n${await this.formatPlan(planData, args.planId)}`;
  }

  @log()
  private async updatePlan(args: { planId?: string; title?: string; steps?: string[] }): Promise<string> {
    if (!args.planId) {
      throw new Error('更新计划需要 planId 参数');
    }

    const plan = await this.storage.loadPlan(args.planId);
    if (!plan) {
      throw new Error(`未找到 ID 为 '${args.planId}' 的计划`);
    }

    if (args.title) {
      plan.title = args.title;
    }

    if (args.steps) {
      plan.steps = args.steps;
      plan.stepStatuses = Array(args.steps.length).fill(PlanStepStatus.NOT_STARTED);
      plan.stepNotes = Array(args.steps.length).fill('');
    }

    await this.storage.savePlan(args.planId, plan);
    return `计划更新成功\n\n${await this.formatPlan(plan, args.planId)}`;
  }

  @log()
  private async listPlans(): Promise<string> {
    const planIds = await this.storage.listPlans();

    if (planIds.length === 0) {
      return '没有可用的计划。使用 create 命令创建新计划。';
    }

    let output = '可用的计划:\n';
    for (const planId of planIds) {
      const plan = await this.storage.loadPlan(planId);
      if (plan) {
        const currentMarker = planId === this.currentPlanId ? ' (当前)' : '';
        const completed = plan.stepStatuses.filter((s) => s === PlanStepStatus.COMPLETED).length;
        const total = plan.steps.length;
        const progress = `${completed}/${total} 步骤已完成`;
        output += `• ${planId}${currentMarker}: ${plan.title} - ${progress}\n`;
      }
    }

    return output;
  }

  @log()
  private async getPlan(planId?: string): Promise<string> {
    const targetPlanId = planId || this.currentPlanId;
    if (!targetPlanId) {
      throw new Error('没有活动计划。请指定 planId 或设置活动计划。');
    }

    const plan = await this.storage.loadPlan(targetPlanId);
    if (!plan) {
      throw new Error(`未找到 ID 为 '${targetPlanId}' 的计划`);
    }

    return this.formatPlan(plan, targetPlanId);
  }

  @log()
  private async setActivePlan(planId?: string): Promise<string> {
    if (!planId) {
      throw new Error('设置活动计划需要 planId 参数');
    }

    const plan = await this.storage.loadPlan(planId);
    if (!plan) {
      throw new Error(`未找到 ID 为 '${planId}' 的计划`);
    }

    this.currentPlanId = planId;
    return `计划 '${planId}' 现在是活动计划。\n\n${await this.formatPlan(plan, planId)}`;
  }

  @log()
  private async markStep(args: {
    planId?: string;
    stepIndex?: number;
    stepStatus?: PlanStepStatus;
    stepNotes?: string;
  }): Promise<string> {
    const targetPlanId = args.planId || this.currentPlanId;
    if (!targetPlanId) {
      throw new Error('没有活动计划。请指定 planId 或设置活动计划。');
    }

    const plan = await this.storage.loadPlan(targetPlanId);
    if (!plan) {
      throw new Error(`未找到 ID 为 '${targetPlanId}' 的计划`);
    }

    if (typeof args.stepIndex !== 'number') {
      throw new Error('标记步骤需要 stepIndex 参数');
    }

    if (args.stepIndex < 0 || args.stepIndex >= plan.steps.length) {
      throw new Error(`无效的 stepIndex: ${args.stepIndex}。有效范围是 0 到 ${plan.steps.length - 1}`);
    }

    if (args.stepStatus && !Object.values(PlanStepStatus).includes(args.stepStatus as PlanStepStatus)) {
      throw new Error(`无效的 stepStatus: ${args.stepStatus}。有效状态是: ${Object.values(PlanStepStatus).join(', ')}`);
    }

    if (args.stepStatus) {
      plan.stepStatuses[args.stepIndex] = args.stepStatus;
    }

    if (args.stepNotes) {
      plan.stepNotes[args.stepIndex] = args.stepNotes;
    }

    await this.storage.savePlan(targetPlanId, plan);
    return `步骤 ${args.stepIndex} 已更新\n\n${await this.formatPlan(plan, targetPlanId)}`;
  }

  @log()
  private async deletePlan(planId?: string): Promise<string> {
    if (!planId) {
      throw new Error('删除计划需要 planId 参数');
    }

    const plan = await this.storage.loadPlan(planId);
    if (!plan) {
      throw new Error(`未找到 ID 为 '${planId}' 的计划`);
    }

    await this.storage.deletePlan(planId);

    if (this.currentPlanId === planId) {
      this.currentPlanId = null;
    }

    return `计划 '${planId}' 已删除`;
  }

  private async formatPlan(plan: PlanData, planId: string): Promise<string> {
    let output = `计划: ${plan.title} (ID: ${planId})\n`;
    output += `${'='.repeat(output.length)}\n\n`;

    // 计算进度统计
    const total = plan.steps.length;
    const completed = plan.stepStatuses.filter((s) => s === PlanStepStatus.COMPLETED).length;
    const inProgress = plan.stepStatuses.filter((s) => s === PlanStepStatus.IN_PROGRESS).length;
    const blocked = plan.stepStatuses.filter((s) => s === PlanStepStatus.BLOCKED).length;
    const notStarted = plan.stepStatuses.filter((s) => s === PlanStepStatus.NOT_STARTED).length;

    const progress = total > 0 ? (completed / total) * 100 : 0;
    output += `进度: ${completed}/${total} 步骤已完成 (${progress.toFixed(1)}%)\n`;
    output += `状态: ${completed} 已完成, ${inProgress} 进行中, ${blocked} 已阻塞, ${notStarted} 未开始\n\n`;
    output += '步骤:\n';

    const statusMarks = {
      [PlanStepStatus.COMPLETED]: '[✓]',
      [PlanStepStatus.IN_PROGRESS]: '[→]',
      [PlanStepStatus.BLOCKED]: '[!]',
      [PlanStepStatus.NOT_STARTED]: '[ ]',
    };

    plan.steps.forEach((step, i) => {
      const status = plan.stepStatuses[i];
      const statusMark = statusMarks[status as PlanStepStatus] || statusMarks[PlanStepStatus.NOT_STARTED];
      output += `${i}. ${statusMark} ${step}\n`;
      if (plan.stepNotes[i]) {
        output += `   备注: ${plan.stepNotes[i]}\n`;
      }
    });

    return output;
  }
}
