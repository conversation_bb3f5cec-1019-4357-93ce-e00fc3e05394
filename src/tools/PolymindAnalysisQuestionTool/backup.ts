import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { LLMService } from '@/services/llmService';
import { MessageUtils } from '@/utils/Message';
import { streamType } from '@/types/types';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { log } from '@/utils/decorators/log';

// 问题分析工具的参数
interface PolymindAnalysisQuestionArgs {
  // 用户的原始问题描述
  question: string;
}

/**
 * 问题分析工具
 * 使用 deepseek-reasoner 模型对用户的问题进行深度理解和分析
 */
export class PolymindAnalysisQuestionTool extends BaseTool {
  name = 'polymind_analysis_question';
  nameCn = '分析问题';
  description = '深度分析用户的问题，提供结构化的理解结果';
  /**
   * 分析用户问题
   * @param args 问题分析的参数
   * @returns 结构化的问题分析结果
   */
  @log()
  async execute(args: PolymindAnalysisQuestionArgs): Promise<string> {
    try {
      this.logger.info('开始分析用户问题');

      // 获取用户输入
      const { currentExecutingAgent } = useAIAgentStateStore.getState();
      const userMessages = currentExecutingAgent.memory.getAllMessages();
      const lastUserMessage = userMessages.find((msg: any) => msg.role === 'user');
      const inputText = lastUserMessage?.content || '';

      const messages = [{ role: 'user', content: inputText }] as any;

      // 构建提示词
      const systemPrompt = `
## 角色
你是一位精代码、数据科学和分析的专家，擅长将模糊的问题转化为结构化的分析框架。你的任务是深入理解用户的需求，并提供全面的问题解析。

## 要求
对用户提出的问题进行系统化分析，输出结构化的理解结果，必须包含以下所有部分：

### 1. 问题概述
- **问题类型**：明确识别这是什么类型的数据分析问题（如描述性分析、预测分析、诊断分析、关联分析等）
- **核心问题**：一句话精确概括用户需要解决的核心问题
- **关键信息提取**：从用户描述中提取所有关键信息点，不遗漏任何细节

### 2. 深度问题解析
- **分析目标**：明确用户希望通过数据分析达成的具体目标
- **具体需求**：以清晰的条目列出所有明确和隐含的需求
- **关键概念与指标**：识别并解释问题中涉及的专业概念和需要关注的关键指标
- **约束条件**：识别任何时间、资源或方法论上的限制条件

### 3. 数据需求分析
- **必要数据**：列出解决问题所需的所有数据类型和来源
- **数据特征**：预估数据的规模、结构、时间跨度和粒度
- **数据缺口**：指出用户描述中缺失但对分析必要的数据
- **数据质量考量**：预判可能存在的数据质量问题（如缺失值、异常值、偏差等）

### 4. 预期输出
- **可视化图表分析**：提出适合问题的数据可视化图标类型与分析目标
- **交付物**：明确用户期望的最终分析结果形式（如报告页面、输出文件等）

### 5. 其他
- **成功标准**：提出评估分析成功与否的标准
- **潜在挑战**：预判分析过程中可能遇到的技术或解释性挑战
- **决策支持**：说明分析结果将如何支持用户的决策或行动

## 输出格式
- 使用清晰的标题和子标题组织你的分析
- 每个部分使用简洁的要点列表呈现
- 保持客观、专业的语言风格
- 确保分析逻辑严密，推理合理
- 如有不确定之处，明确指出并提供多种可能的解释
`;
      const systemMsg = [MessageUtils.systemMessage(systemPrompt)];

      // 调用 deepseek-reasoner 模型
      const llm = new LLMService();

      const analysis = await llm.askStream(messages, systemMsg, [], 'none', (type: streamType, data: any) => {
        if (type === streamType.REASONING) {
          this.feedbackLogger.thinking(data);
        } else if (type === streamType.THINKING) {
          this.feedbackLogger.toolResult(data);
        }
      });

      this.logger.info('问题分析完成');
      return analysis.content;
    } catch (error) {
      this.logger.error(`问题分析失败: ${error}`);
      throw new ToolExecutionError(this.name, `问题分析失败: ${error}`);
    }
  }
}
