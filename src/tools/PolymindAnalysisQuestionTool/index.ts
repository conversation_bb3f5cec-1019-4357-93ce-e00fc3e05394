import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { LLMService } from '@/services/llmService';
import { MessageUtils } from '@/utils/Message';
import { streamType } from '@/types/types';
import { useAIAgentStateStore } from '@/store/aiAgenticStore';
import { pageContextPrompt, systemPrompt } from './prompt';
import { useAIChatStore } from '@/store/aiChatStore';
import { log } from '@/utils/decorators/log';

// 问题分析工具的参数
interface PolymindAnalysisQuestionArgs {
  // 用户的原始问题描述
  question: string;
}

/**
 * 问题分析工具
 * 使用 deepseek-reasoner 模型对用户的问题进行深度理解和分析
 */
export class PolymindAnalysisQuestionTool extends BaseTool {
  name = 'polymind_analysis_question';
  nameCn = '分析问题';
  description = '深度分析用户的问题，提供结构化的理解结果，在分析问题前，如果存在附件，先读取附件内容，再进行深度理解';
  /**
   * 分析用户问题
   * @param args 问题分析的参数
   * @returns 结构化的问题分析结果
   */
  @log()
  async execute(args: PolymindAnalysisQuestionArgs): Promise<string> {
    try {
      this.logger.info('开始分析用户问题');

      const { attachments } = useAIChatStore.getState();
      const excelFiles = attachments.filter((file: any) => file.type === 'file');
      const shortExcelFiles = excelFiles.map((item) => {
        const newItem = { ...item };
        delete newItem.allData;
        return newItem;
      });

      // 获取用户输入
      const { currentExecutingAgent } = useAIAgentStateStore.getState();
      const userMessages = currentExecutingAgent.memory.getAllMessages();
      const firstUserMessage = userMessages.find((msg: any) => msg.role === 'user');
      const inputText = firstUserMessage?.content || '';
      const tarContent = `${inputText}\n\n 附件各Excel的数据摘要如下：\n ${JSON.stringify(shortExcelFiles)}`;
      const messages = [{ role: 'user', content: tarContent }] as any;
      // 构建提示词
      const systemMsg = [MessageUtils.systemMessage(systemPrompt)];

      // 调用 deepseek-reasoner 模型
      const llm = new LLMService();

      const analysis = await llm.askStream(messages, systemMsg, [], 'none', (type: streamType, data: any) => {
        if (type === streamType.REASONING) {
          this.feedbackLogger.thinking(data);
        } else if (type === streamType.THINKING) {
          this.feedbackLogger.toolResult(data);
        }
      });

      // 设置模拟页面生成PRD内容
      const PRDContent = `${analysis.content} \n\n ${pageContextPrompt}`;
      useAIAgentStateStore.getState().setGeneralPRD(PRDContent);

      this.logger.info('问题分析完成');
      return analysis.content;
    } catch (error) {
      this.logger.error(`问题分析失败: ${error}`);
      throw new ToolExecutionError(this.name, `问题分析失败: ${error}`);
    }
  }
}
