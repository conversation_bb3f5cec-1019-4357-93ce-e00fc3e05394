import { DEFAULT_APP_TYPE } from '@/config';
import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

// 创建应用的输入参数
interface PreviewPageArgs {
  // 页面ID
  pageId: string;
}

/**
 * 预览组件工具
 */
export class PreviewPageTool extends BaseTool {
  name = 'preview_page';
  nameCn = '预览页面';
  description = '预览页面的真实效果';
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      pageId: {
        type: 'string',
        description: '页面ID 或 表单formUuid',
      },
    },
    required: ['pageId'],
  };
  /**
   * 预览组件
   */
  @log()
  async execute(args: PreviewPageArgs): Promise<any> {
    try {
      this.logger.info('开始预览页面');
      const { pageId } = args;
      const previewUrl = `/${DEFAULT_APP_TYPE}/preview/${pageId}?navConfig.type=none&__disableCache=true`;
      // 发送预览地址更新消息
      window.postMessage(
        {
          type: 'previewReady',
          url: previewUrl,
        },
        '*',
      );

      const result = {
        status: '✅',
        pageId,
        previewUrl,
      };
      this.logger.info('页面预览成功');
      return result;
    } catch (error) {
      this.logger.error(`页面预览失败: ${error}`);
      throw new ToolExecutionError(this.name, `页面预览失败: ${error}`);
    }
  }
  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.pageId) {
      errors.push('页面ID或表单formUuid不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
