import { ToolSchema } from '@/types/types';
import { BaseTool } from '../../utils/Tool';
import { ToolExecutionError } from '@/utils/Errors';
import { streamTxtFromAI } from '@ali/yc-utils';
import { log } from '@/utils/decorators/log';

export interface ReadImageArgs {
  /**
   * 图片URL，公网可访问的图片地址
   */
  imageUrl: string;
  /**
   * 读取图片的提示词
   */
  prompt?: string;
}

/**
 * 读取图片工具
 */
export class ReadImageTool extends BaseTool {
  name = 'read_image';
  nameCn = '阅读图片';
  description = '一个通过AI模型读取图片内容的工具。';

  parameters: ToolSchema = {
    type: 'object',
    properties: {
      imageUrl: {
        type: 'string',
        description: '图片URL，公网可访问的图片地址',
      },
      prompt: {
        type: 'string',
        description: '读取图片的提示词',
      },
    },
    required: ['imageUrl'],
  };

  /**
   * 执行图片读取
   * @param args 读取图片参数
   * @returns 读取结果
   */
  @log()
  async execute(args: ReadImageArgs): Promise<string> {
    try {
      const fileNameStr = args.imageUrl.split('&').find((item) => item.startsWith('fileName='));
      const fileName = fileNameStr ? fileNameStr.split('=')[1] : '';
      const response = await streamTxtFromAI(
        {
          imageUrl: fileName,
          prompt: args.prompt,
          maxTokens: 2000,
          skill: 'ReadImage',
        } as any,
        (chunk: string, full: string) => {
          this.feedbackLogger.toolResult(full);
        },
      );
      if (!response.success) {
        throw new ToolExecutionError(this.name, '读取图片失败，请稍后重试');
      }
      return response.result;
    } catch (error) {
      this.logger.error(`读取图片失败: ${error}`);
      throw new ToolExecutionError(this.name, `读取图片失败: ${error}`);
    }
  }

  /**
   * 验证参数
   * @param args 参数
   * @returns 验证结果
   */
  validateArgs(args: ReadImageArgs): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.imageUrl) {
      errors.push('图片路径不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
