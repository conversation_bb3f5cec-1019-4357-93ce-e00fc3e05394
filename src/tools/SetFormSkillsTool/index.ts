/* eslint-disable no-await-in-loop */
/* eslint-disable @typescript-eslint/member-ordering */
import { ToolExecutionError } from '@/utils/Errors';
import { ToolValidator } from '@/utils/Validator';
import { createOrUpdateEntitySkill, getFormSchema } from '@/apis';
import { getConfig, getCsrfToken } from '@ali/yc-utils';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

const fieldTypeList = [
  'TextField',
  'TextareaField',
  'RadioField',
  'SelectField',
  'CheckboxField',
  'MultiSelectField',
  'NumberField',
  'RateField',
  'DateField',
  // 未来再增加
  // 'CascadeDateField',
  'EditorField',
  'EmployeeField',
  'CountrySelectField',
  'DepartmentSelectField',
  'AddressField',
  'ImageField',
  'TableField',
  'AssociationFormField',
] as any;

/**
 * 设置表单AI技能参数
 */
export interface SetFormSkillsArgs {
  /** 助理ID */
  agentCode: string;
  /** 应用ID */
  appId: string;
  /** 表单ID */
  formUuid: string;
  /** 表单名称  */
  formName: string;
  /** 要启用的技能列表 */
  skills?: Array<'chatBI' | 'chatForm'>;
  /** 是否开启技能，默认为开启 */
  status?: 'on' | 'off';
}

/**
 * 设置表单AI技能
 * 用于快速为AI助理表单开启或关闭chatBI和chatForm技能
 */
export class SetFormSkillsTool extends BaseTool {
  name = 'create_form_skills';
  nameCn = '设置表单技能';
  description =
    '为AI助理技能开启或关闭某一个表单的2个技能，包含智能数据分析(chatBI)和智能表单填报(chatForm)技能，一般情况下会同时开启表单的2个技能。';
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      agentCode: {
        type: 'string',
        description: 'AI助理的ID',
      },
      appId: {
        type: 'string',
        description: '应用ID，APP_开头的应用唯一标识',
      },
      formUuid: {
        type: 'string',
        description: '表单ID，FORM-开头',
      },
      formName: {
        type: 'string',
        description: '表单名称',
      },
      skills: {
        type: 'array',
        description: '要启用的技能列表，默认同时启用chatBI和chatForm',
        items: {
          type: 'string',
          enum: ['chatBI', 'chatForm'],
        },
      },
      status: {
        type: 'string',
        description: '是否开启技能，默认为开启',
        enum: ['on', 'off'],
      },
    },
    required: ['agentCode', 'appId', 'formUuid', 'formName', 'skills', 'status'],
  };

  parseFieldListBySchema(children: any) {
    return children
      .map((item: any) => {
        if (!fieldTypeList.includes(item.componentName)) return null;
        const field: any = {
          componentName: item.componentName,
          fieldName: item.props.fieldId,
          fieldZhName: item.props.label?.zh_CN,
          fieldType: 'string',
          isRequired: item.props.validation?.find((v: any) => v.type === 'required') ? 1 : 0,
        };
        if (['CascadeDateField', 'DateField'].includes(item.componentName)) {
          field.fieldFormat = item.props.format;
          field.fieldProp = item.componentName === 'CascadeDateField' ? 'timeRange' : 'time';
        } else if (item.componentName === 'NumberField') {
          field.fieldType = 'number';
        } else if (['RadioField', 'CheckboxField', 'SelectField', 'MultiSelectField'].includes(item.componentName)) {
          field.fieldType = ['CheckboxField', 'MultiSelectField'].includes(item.componentName) ? 'array' : 'string';
          field.fieldEnum = item.props.dataSource.map((d: any) => ({
            label: d.text.zh_CN,
            value: d.value,
          }));
          if (['CheckboxField', 'MultiSelectField'].includes(item.componentName)) {
            field.children = [
              {
                fieldZhName: '数组字段',
                fieldName: `${item.props.fieldId}_item`,
                fieldType: 'string',
              },
            ];
          }
        } else if (item.componentName === 'ImageField') {
          field.fieldType = item.props.multiple ? 'array' : 'string';
          field.fieldProp = 'image';
          field.children = [
            {
              fieldZhName: '数组字段',
              fieldName: `${item.props.fieldId}_item`,
              fieldType: 'object',
              children: [
                {
                  fieldName: 'mediaId',
                  fieldZhName: '图片ID',
                  fieldType: 'string',
                  isRequired: 1,
                },
                {
                  fieldName: 'name',
                  fieldZhName: '图片名字',
                  fieldType: 'string',
                  isRequired: 1,
                },
              ],
            },
          ];
        } else if (item.componentName === 'TableField') {
          field.fieldType = 'array';
          field.children = [
            {
              fieldZhName: '子表字段列表',
              fieldName: `${item.props.fieldId}_object`,
              fieldType: 'object',
              isRequired: 0,
              children: this.parseFieldListBySchema(item.children),
            },
          ];
        } else if (
          ['EmployeeField', 'CountrySelectField', 'DepartmentSelectField', 'AssociationFormField'].includes(
            item.componentName,
          )
        ) {
          field.fieldProp = item.componentName === 'EmployeeField' ? 'contact' : '';
          field.fieldType = item.props.multiple ? 'array' : 'string';
          if (item.props.multiple) {
            field.children = [
              {
                fieldZhName: '数组字段',
                fieldName: `${item.props.fieldId}_item`,
                fieldType: 'string',
              },
            ];
          }
        }
        return field;
      })
      .filter((f: any) => !!f);
  }

  /**
   * 更新AI助理技能表单
   * @param args 更新表单参数
   * @returns 更新结果
   */
  @log()
  async execute(args: SetFormSkillsArgs): Promise<any> {
    try {
      this.logger.info(`开始执行${this.name}工具，参数: ${JSON.stringify(args)}`);

      // 设置默认值
      const skills = args.skills || ['chatBI', 'chatForm'];
      const status = args.status || 'on';

      const results: Record<string, any> = {};
      const enabledSkills: string[] = [];

      // 处理每个技能
      for (const skill of skills) {
        try {
          this.logger.info(`正在${status === 'on' ? '开启' : '关闭'} ${skill} 技能`);
          // 根据技能类型设置不同的配置
          const chatBISkillConfig = {
            cubeSource: 'YIDA_FORM',
            cubeName: args.formName,
          };
          let chatFormSkillConfig: any = {
            sheetName: args.formName,
            formUuid: args.formUuid,
            appKey: args.appId,
          };

          const entityId = skill === 'chatBI' ? args.formUuid.replace(/^FORM-/, 'FORM_') : args.formUuid;
          const entityType = skill === 'chatBI' ? 'cube' : 'form';
          if (skill === 'chatForm') {
            // 构造skillConfig
            // 获取表单schema
            const formSchema = await getFormSchema({
              appType: args.appId,
              formUuid: args.formUuid,
              schemaVersion: 'V5',
            });
            const componentsTreeChildren = formSchema?.pages?.[0]?.componentsTree?.[0]?.children;
            const RootContent = componentsTreeChildren?.find((item: any) => item?.componentName === 'RootContent');
            const FormContainer = RootContent.children?.find((item: any) => item?.componentName === 'FormContainer');
            const fieldList = this.parseFieldListBySchema(FormContainer.children);
            chatFormSkillConfig = {
              ...chatFormSkillConfig,
              csrfToken: getCsrfToken(),
              fieldList,
              linkage: false,
              corpId: getConfig('corpId'),
              userId: window.loginUser?.userId,
              privateCardData: {},
            };
          }
          const skillConfig = skill === 'chatBI' ? chatBISkillConfig : chatFormSkillConfig;
          const response = await createOrUpdateEntitySkill({
            appType: args.appId,
            agentCode: args.agentCode,
            skill,
            skillConfig,
            entityType,
            entityId,
            status,
          });
          if (response === false) {
            throw new Error(`${status === 'on' ? '开启' : '关闭'} ${skill} 技能失败`);
          }
          enabledSkills.push(skill);
          this.logger.info(`${skill} 技能${status === 'on' ? '开启' : '关闭'}成功`);
        } catch (skillError) {
          // 单个技能出错不影响其他技能
          this.logger.error(`${skill} 技能操作失败: ${skillError}`);
          results[`${skill}Status`] = '⚠️ 失败';
          results[`${skill}ErrorReason`] = skillError instanceof Error ? skillError.message : String(skillError);
        }
      }

      // 构建结果对象
      let statusText = '';
      if (enabledSkills.length === 2) {
        statusText = '✅ 全部成功';
      } else if (enabledSkills.length === 1) {
        statusText = '⚠️ 部分失败';
      } else {
        statusText = '⚠️ 全部失败';
      }
      results.status = statusText;
      results.skills = enabledSkills.length > 0 ? enabledSkills : '无';
      results.time = new Date().toLocaleString();

      this.logger.info(`${this.name}工具执行完成，结果: ${JSON.stringify(results)}`);
      return results;
    } catch (error) {
      this.logger.error(`${this.name}工具执行失败: ${error}`);
      throw new ToolExecutionError(
        this.name,
        `更新AI助理技能状态失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * 验证参数
   * @param args 参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    // 使用通用验证器进行基础验证
    const baseValidation = ToolValidator.validate(this.parameters, args);
    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];

    // 验证appId格式
    if (args.appId && !args.appId.startsWith('APP_')) {
      errors.push('应用ID格式不正确，应以APP_开头');
    }

    // 验证formId格式
    if (args.formId && !args.formId.startsWith('FORM-')) {
      errors.push('表单ID格式不正确，应以FORM-开头');
    }

    // 验证skills数组
    if (args.skills) {
      if (!Array.isArray(args.skills)) {
        errors.push('skills参数必须是数组');
      } else if (args.skills.length === 0) {
        errors.push('skills参数不能为空数组');
      } else {
        const validSkills = ['chatBI', 'chatForm'];
        for (const skill of args.skills) {
          if (!validSkills.includes(skill)) {
            errors.push(`技能名称 "${skill}" 不合法，有效值为: ${validSkills.join(', ')}`);
          }
        }
      }
    }

    // 验证status参数
    if (args.status && !['on', 'off'].includes(args.status)) {
      errors.push('status参数必须为 "on" 或 "off"');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
