import { ToolSchema } from '@/types/types';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

interface StepCompleteArgs {
  message?: string;
}

export class StepCompleteTool extends BaseTool {
  name = 'step_complete';
  nameCn = '步骤完成';
  description = '标记当前步骤完成并准备进入下一个步骤。此工具不会终止整个计划。';

  parameters: ToolSchema = {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        description: '关于步骤完成的消息或备注',
      },
    },
    required: [],
  };

  @log()
  async execute(args: StepCompleteArgs): Promise<string> {
    const message = args.message || '步骤已成功完成';
    return `步骤完成: ${message}`;
  }
}
