import { ToolExecutionError } from '@/utils/Errors';
import { BaseTool } from '@/utils/Tool';
import { queryPageList, queryFieldList } from '@/utils/YidaMeta';
import { LLMService } from '@/services/llmService';
import { MessageUtils } from '@/utils/Message';
import { log } from '@/utils/decorators/log';

interface TargetPageArgs {
  // 应用ID
  appId: string;
  // 描述
  description?: string;
}

export class TargetPageTool extends BaseTool {
  name = 'target_page';
  nameCn = '查找目标页面';
  description = '根据描述快速定位到目标页面，并返回页面相关信息';
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用ID，APP_开头的应用唯一标识',
      },
      description: {
        type: 'string',
        description: '页面描述',
      },
    },

    required: ['appId', 'description'],
  };

  @log()
  async execute(args: TargetPageArgs): Promise<any> {
    try {
      this.logger.info('开始根据描述快速定位到目标页面');
      if (!args.appId) {
        return [];
      }
      this.logger.info('开始调用工具', this.name, args);
      const pageList = await queryPageList(args.appId);

      const messages = [{ role: 'user', content: args.description }] as any;

      // 构建提示词
      const systemPrompt = `
## 角色
你是一个页面搜索专家，根据页面描述，能够快速从页面列表中选出目标页面。

## 上下文
-  页面列表：
\`\`\`json
${JSON.stringify(pageList)}
\`\`\`

## 要求
- 必须从页面列表中返回页面信息，不要杜撰；
- 如果没有匹配到合适的页面，可以返回null；
- 返回结果必须以JSON格式
`;
      const systemMsg = [MessageUtils.systemMessage(systemPrompt)];
      // 调用 deepseek-r1 模型
      const llm = new LLMService({
        model: 'deepseek-chat',
      });
      const res = await llm.ask(messages, systemMsg, [], 'none', true);
      const info = JSON.parse(res.content || '{}');
      if (info?.pageId) {
        if (['receipt', 'process'].includes(info.type)) {
          info.fieldList = await queryFieldList(args.appId, info.pageId);
        }
        return {
          status: '✅',
          content: info,
        };
      }
      return {
        status: '⚠️',
        content: null,
      };
    } catch (error) {
      this.logger.error(`根据描述快速定位到目标页面失败: ${error}`);
      throw new ToolExecutionError(this.name, `根据描述快速定位到目标页面失败: ${error}`);
    }
  }
  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    if (!args.appId) {
      errors.push('应用ID不能为空');
    }

    if (!args.description) {
      errors.push('页面描述不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
