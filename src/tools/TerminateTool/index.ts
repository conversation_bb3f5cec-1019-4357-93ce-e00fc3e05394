import { ToolSchema } from '@/types/types';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

interface TerminateArgs {
  reason?: string;
}

export class TerminateTool extends BaseTool {
  name = 'terminate';
  nameCn = '终止';
  description = '终止当前任务的执行';

  parameters: ToolSchema = {
    type: 'object',
    properties: {
      reason: {
        type: 'string',
        description: '终止计划的原因',
      },
    },
    required: [],
  };

  @log()
  async execute(args: TerminateArgs): Promise<string> {
    const reason = args.reason || '任务计划已成功完成';
    return `任务计划终止: ${reason}`;
  }
}
