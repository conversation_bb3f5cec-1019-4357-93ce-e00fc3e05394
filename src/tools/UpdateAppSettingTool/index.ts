import { ratiocinateByAI } from '@ali/yc-utils';
import { getAppIncludingAecpInfo, UpdateAppParams, updateApp } from '@/apis';
import { BaseTool } from '@/utils/Tool';
import { ToolExecutionError } from '@/utils/Errors';
import { log } from '@/utils/decorators/log';

/**
 * 应用配置更新工具参数
 */
export interface UpdateAppSettingToolParams {
  /** 应用ID */
  appId: string;
  /** 修改描述 */
  modifyDescription: string;
}

const UpdateAppParamsStr = `
interface UpdateAppParams {
  /** 应用名称 */
  appName: {
    en_US: string;
    key: string;
    pureEn_US: string;
    type: string;
    value: string;
    zh_CN: string;
  };
  /** 应用描述 */
  description: {
    en_US: string;
    key: string;
    pureEn_US: string;
    type: string;
    value: string;
    zh_CN: string;
  };
  /** 应用图标 */
  icon: string;
  /** 是否在应用中心显示 */
  showAppCenter: 'n';
  /** 颜色主题 */
  colour: 'blue' | 'orange' | 'green';
  /** 首页Logo */
  homepageLogo: string;
  /** Logo链接 */
  logoLink?: string;
  /** 系统链接 */
  systemLink: string;
  /** 是否添加水印 */
  addWaterMark: 'n' | 'y';
  /** 短链接 */
  shortUrl?: string;
  /** 是否允许外部通讯录 */
  allowExternalAddressBook?: 'n' | 'y';
  /** 详情布局 */
  detailLayout: 'vertical' | 'horizontal';
  /** 流程操作记录布局 */
  procOperateRecordLayout: 'standard' | 'merge';
  /** 是否显示应用标题 */
  showAppTitle: boolean;
}
`;

/**
 * 应用配置更新工具
 * 通过AI辅助更新应用配置
 */
export class UpdateAppSettingTool extends BaseTool {
  name = 'update_app_setting';
  nameCn = '更新应用配置';
  description = `一个通过自然语言描述更新应用配置的工具，可以根据用户的描述智能修改应用的各项配置。可修改的应用配置参数定义如下：\n${UpdateAppParamsStr}`;
  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用ID，通常为APP_开头的字符串',
      },
      modifyDescription: {
        type: 'string',
        description: '修改描述，用自然语言描述需要修改的内容，例如：将应用名称修改为"智能工单系统"，将颜色主题改为绿色',
      },
    },
    required: ['appId', 'modifyDescription'],
  };
  /**
   * 执行应用配置更新
   * @param args 工具参数
   * @returns 更新结果
   */
  @log()
  async execute(args: UpdateAppSettingToolParams): Promise<any> {
    try {
      this.logger.info('开始调用工具', this.name, args);
      const { appId, modifyDescription } = args;
      // 1. 通过appId调用getAppIncludingAecpInfo获取应用信息
      this.feedbackLogger.toolResult(`正在获取应用 ${appId} 的配置信息...`);
      const appInfo = await getAppIncludingAecpInfo({
        appType: appId,
      });
      if (!appInfo) {
        this.logger.error('获取应用信息失败');
        throw new ToolExecutionError(this.name, '获取应用信息失败，请检查应用ID是否正确');
      }

      // 2. 从应用信息中提取updateApp所需的参数
      const currentAppParams: UpdateAppParams = {
        appKey: appId,
        mode: 'normal',
        appName: appInfo.appName || {
          en_US: '',
          key: '',
          pureEn_US: '',
          type: '',
          value: '',
          zh_CN: '',
        },
        description: appInfo.description || {
          en_US: '',
          key: '',
          pureEn_US: '',
          type: '',
          value: '',
          zh_CN: '',
        },
        icon: appInfo.icon || '',
        navigation: appInfo.navigation || 'TODO,DONE,SUBMIT',
        showAppCenter: appInfo.showAppCenter || 'n',
        reVersion: appInfo.reVersion || '5.9.16',
        type: appInfo.type || 'single',
        navLayout: appInfo.navLayout || 'auto',
        colour: appInfo.colour || 'blue',
        homepageLogo: appInfo.homepageLogo || '',
        logoLink: appInfo.logoLink || '',
        systemLink: appInfo.systemLink || '',
        addWaterMark: appInfo.addWaterMark || 'n',
        shortUrl: appInfo.shortUrl || '',
        allowExternalAddressBook: appInfo.allowExternalAddressBook || 'n',
        detailLayout: appInfo.detailLayout || 'vertical',
        procOperateRecordLayout: appInfo.procOperateRecordLayout || 'standard',
        categoryId: appInfo.categoryId || 'DEFAULT',
        defaultConsultant: appInfo.defaultConsultant || 'n',
        appDeptId: appInfo.appDeptId || -1,
        showAppTitle: appInfo.showAppTitle || false,
      };
      // 3. 调用大模型接口修改应用参数
      this.feedbackLogger.toolResult('正在修改应用配置中...');
      const prompt = `
# 根据用户的修改描述，修改应用配置参数。

## 当前应用配置参数:
${JSON.stringify(currentAppParams, null, 2)}

## 允许修改的应用配置参数TS如下:
${UpdateAppParamsStr}

## 用户修改描述:
${modifyDescription}

## 要求:
1. 返回回复给用户的修改内容及结果描述 modifyDescription 
2. 请根据用户的修改描述，仅返回有变更的应用配置参数的JSON对象 modifiedParams, 参数值没有修改的不要返回。
3. 只需返回JSON对象，不要添加其他说明，返回格式如下：
{
  "modifyDescription": "",
  "modifiedParams": {}
}
`;
      const updatedParams = await ratiocinateByAI(
        {
          prompt,
          maxTokens: 2000,
          skill: 'ToData',
        },
        () => {},
      );
      if (!Object.keys(updatedParams?.modifiedParams || {}).length) {
        this.logger.error('AI修改应用配置参数失败');
        throw new ToolExecutionError(this.name, 'AI修改应用配置参数失败，请稍后重试');
      }
      // 6. 调用updateApp API更新应用配置
      this.logger.info('AI模型修改应用配置结果：', updatedParams);
      const updateResult = await updateApp({
        appKey: appId,
        ...currentAppParams,
        ...updatedParams.modifiedParams,
      });
      if (!updateResult) {
        this.logger.error('应用配置更新失败');
        throw new ToolExecutionError(this.name, '应用配置更新失败，请检查参数是否正确');
      }
      // 7. 返回更新结果
      this.logger.info('应用配置更新成功', updateResult);
      const appSettingUrl = `https://${window.location.host}/${appId}/admin/appSetting/basicSetting`;
      const result_info = {
        success: true,
        message: updatedParams?.modifyDescription || '应用配置更新成功',
        modifiedParams: updatedParams?.modifiedParams,
        actions: [
          {
            type: 'link',
            url: appSettingUrl,
            text: '查看应用配置',
          },
        ],
      };
      this.logger.info('应用配置更新成功', result_info);
      return result_info;
    } catch (error) {
      this.logger.error(`更新应用配置失败: ${error}`);
      throw new ToolExecutionError(this.name, `更新应用配置失败: ${error}`);
    }
  }
  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    const { appId, modifyDescription } = args;

    if (!appId) {
      errors.push('应用ID不能为空');
    }

    if (!modifyDescription) {
      errors.push('修改描述不能为空');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
