import { ToolExecutionError } from '@/utils/Errors';
import { ToolValidator } from '@/utils/Validator';
import { BaseTool } from '@/utils/Tool';
import { FormGenerator } from '@/utils/FormGenerator';
import { log } from '@/utils/decorators/log';

// 修改表单工具
interface UpdateFormArgs {
  // 应用ID
  appId: string;
  // 表单ID
  formUuid: string;
  // 修改描述
  description?: string;
}

/**
 * 更新表单工具
 */
export class UpdateFormTool extends BaseTool {
  name = 'update_form';
  nameCn = '更新表单';
  description = '用于**表单页面**更新，支持对表单中的内容进行修改，例如字段的增删改等操作';

  // 参数定义
  parameters = {
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        description: '应用唯一标识，格式示例:APP_XXX',
      },
      formUuid: {
        type: 'string',
        description: '表单页面的唯一标识，格式示例:FORM-XXX',
      },
      description: {
        type: 'string',
        description: '表单修改描述，请严格根据用户的需求进行修改，不要发散或者杜撰用户需求',
      },
    },
    required: ['appId', 'formUuid', 'description'],
  };

  /**
   * 修改表单
   */
  @log()
  async execute(args: UpdateFormArgs): Promise<any> {
    try {
      const { appId, formUuid, description } = args;

      const formGenerator = new FormGenerator(appId, formUuid);
      await formGenerator.init();

      await formGenerator.transformFormMetaInfo(description);
      await formGenerator.generateFormSchema();
      const res = await formGenerator.saveSchema();
      if (!res) {
        throw new ToolExecutionError(this.name, '表单不存在');
      }
      const formUrl = `/${appId}/workbench/${formUuid}?__disableCache=true`;
      // 发送预览地址更新消息
      window.postMessage(
        {
          type: 'previewReady',
          url: formUrl,
        },
        '*',
      );
      return {
        status: '✅',
        message: '表单更新成功',
        appId,
        formUuid,
      };
    } catch (error) {
      this.logger.error(`更新表单失败: ${error}`);
      throw new ToolExecutionError(this.name, `更新表单失败: ${error}`);
    }
  }

  /**
   * 验证参数
   */
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    const { appId, formUuid, description } = args;

    if (!appId) {
      errors.push('应用ID不能为空');
    }

    if (!formUuid) {
      errors.push('表单ID不能为空');
    }

    if (!description) {
      errors.push('修改描述不能为空');
    }
    return ToolValidator.validate(this.parameters, args);
  }
}
