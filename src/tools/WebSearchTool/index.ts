import { streamingSearchWeb } from '@ali/yc-utils';
import { ToolSchema } from '@/types/types';
import { ToolValidator } from '@/utils/Validator';
import { BaseTool } from '../../utils/Tool';
import { log } from '@/utils/decorators/log';

interface WebSearchArgs {
  query: string;
}

export class WebSearchTool extends BaseTool {
  name = 'web_search';
  nameCn = '网络搜索';
  description = '使用搜索引擎搜索互联网上的信息';
  parameters: ToolSchema = {
    type: 'object',
    properties: {
      query: {
        type: 'string',
        description: '(必填）提交给网站的搜索查询。',
      },
    },
    required: ['query'],
  };

  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    return ToolValidator.validate(this.parameters, args);
  }

  @log()
  async execute(args: WebSearchArgs): Promise<string> {
    // 验证参数
    const validation = this.validateArgs(args);
    if (!validation.valid) {
      throw new Error(`参数验证失败: ${validation.errors?.join(', ')}`);
    }
    const { query } = args;
    try {
      const response = await streamingSearchWeb(
        {
          prompt: query,
          maxTokens: 2000,
        } as any,
        (str: string, full: string) => {
          this.feedbackLogger.toolResult(full);
        },
      );
      if (!response.success) {
        return '联网搜索失败';
      }
      console.log('+++++search result+++++', response.result);
      return response.result;
    } catch (error) {
      throw new Error(`联网搜索失败: ${error?.message || '未知异常'}`);
    }
  }
}
