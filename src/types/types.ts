// 增加window的定义
export interface WaitingForUserResponse {
  resolve: (userResponse: string) => void;
  onInputStart: () => void;
  question: string;
  isDirectResponse: boolean;
  timeout: number;
}
declare global {
  var waitingForUserResponse: WaitingForUserResponse | undefined;
  interface Window {
    [key: string]: any;
  }
}

export type LogLevel = 'debug' | 'info' | 'warning' | 'error';

export enum FlowType {
  PLANNING = 'planning',
  EXECUTE = 'execute',
}

export interface PlanStep {
  description: string;
  tool?: string;
  args?: Record<string, any>;
}

export interface Plan {
  steps: PlanStep[];
}

export enum AgentStatus {
  IDLE = 'idle',
  THINKING = 'thinking',
  ACTING = 'acting',
  FINISHED = 'finished',
  ERROR = 'error',
  NEXTSTEP = 'nextstep', // 标记当前步骤完成，准备进行下一步
}

export enum PlanStepStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  BLOCKED = 'blocked',
}

export interface Flow {
  name: string;
  description: string;
  steps: FlowStep[];
}

export interface FlowStep {
  name: string;
  description?: string;
  action: string;
  next?: string;
}

export interface Message {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content?: string;
  toolCallId?: string;
  name?: string;
  toolCalls?: ToolCall[];
  isPlanMsg?: boolean;
}

export enum streamType {
  /**
   * 深入思考
   */
  REASONING = 'reasoning',
  /**
   * 直接回答
   */
  THINKING = 'thinking',
  /**
   * FunctionCall Result
   */
  TOOL_CALL = 'toolCall',
}

export interface ToolCall {
  id: string;
  function: {
    name: string;
    arguments: string | Record<string, unknown>;
  };
}

export interface LLMResponse {
  content?: string;
  toolCalls?: ToolCall[];
  thinking?: string; // 代理思考过程
}

export interface PlanningToolParam {
  type: string;
  function: {
    name: string;
    description: string;
    parameters: {
      type: string;
      properties: Record<
        string,
        {
          type: string;
          enum?: string[];
          optional?: boolean;
          items?: {
            type: string;
          };
        }
      >;
    };
  };
}

export interface PlanData {
  title: string;
  steps: string[];
  stepStatuses: PlanStepStatus[];
  stepNotes: string[];
}

export interface PlanStatusData {
  title: string;
  completed: number;
  total: number;
  steps: Array<{
    description: string;
    status: PlanStepStatus;
  }>;
  icon?: string;
  iconUrl?: string;
  url?: string;
  desc?: string;
}

export interface ToolParameter {
  type: string;
  description: string;
  default?: any;
  enum?: string[];
  items?: {
    type: string;
    enum?: string[];
    properties?: Record<string, ToolParameter>;
    requiredProperties?: string[];
  };
  properties?: Record<string, ToolParameter>;
  requiredProperties?: string[];
}

export interface ToolSchema {
  type: string;
  properties: Record<string, ToolParameter>;
  required: string[];
}

export interface Tool {
  name: string;
  nameCn?: string;
  description: string;
  parameters?: ToolSchema;
  execute: (args: any) => Promise<any>;
  validateArgs?: (args: any) => { valid: boolean; errors?: string[] };
  agent?: any;
}

export interface Config {
  openaiApiKey: string;
  googleApiKey?: string;
  deepseekV3ApiKey?: string;
  openRouterApiKey?: string;
  googleSearchEngineId?: string;
  logLevel: string;
  maxSteps: number;
  defaultTimeout: number;
}

export interface ToolParams {
  [key: string]: any;
}

export interface LLMConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  [key: string]: any;
}

/**
 * 计划存储接口
 */
export interface PlanStorage {
  /**
   * 保存计划
   * @param planId 计划ID
   * @param planData 计划数据
   */
  savePlan: (planId: string, planData: PlanData) => Promise<void>;

  /**
   * 加载计划
   * @param planId 计划ID
   * @returns 计划数据，如果不存在则返回 null
   */
  loadPlan: (planId: string) => Promise<PlanData | null>;

  /**
   * 列出所有计划
   * @returns 计划ID列表
   */
  listPlans: () => Promise<string[]>;

  /**
   * 删除计划
   * @param planId 计划ID
   */
  deletePlan: (planId: string) => Promise<void>;
}

export interface StepInfo {
  text: string;
  type?: string;
}

export interface ExecutionResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface agentRunOptions {
  isPlanMsg?: boolean;
}
