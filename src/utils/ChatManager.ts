import { uniqueId } from '@ali/ve-utils';
import { Logger } from './Logger';
import { UserFeedbackLogger } from './UserFeedbackLogger';
import { AgentConfig, FactoryAgent } from '@/core/FactoryAgent';
import { BaseAgent } from '../core/BaseAgent';
import { FeedbackLogItem } from './UpdatePrinterEffect';
import { UserFeedbackCard } from './UserFeedbackCard';
import { MessageUtils } from './Message';
import { useAIChatStore } from '@/store/aiChatStore';

export interface ChatMessage {
  id: string;
  isUser: boolean;
  timestamp: Date;
  isSystemLog?: boolean;
  content: FeedbackLogItem[] | string;
  attachments?: any[];
  /**
   * 标记是否为特殊编辑消息
   */
  isSpecialEditMessage?: string;
  /**
   * 标记是否为特殊选择节点消息
   */
  isSpecialSelectNodeMessage?: string;
}

export interface ChatState {
  messages: ChatMessage[];
  isProcessing: boolean;
  systemLogs: string[];
  currentLogIndex: number;
  isTyping: boolean;
}

export interface ChatCallbacks {
  setMessages: (messages: ChatMessage[] | ((prev: ChatMessage[]) => ChatMessage[])) => void;
  setIsProcessing: (isProcessing: boolean) => void;
  setIsSummarizing?: (isSummarizing: boolean) => void;
}

export class ChatManager {
  private agentConfig: AgentConfig;
  private state: ChatState;
  private callbacks: ChatCallbacks;
  private logger: Logger;
  private feedbackCard: UserFeedbackCard;
  private feedbackLogger: UserFeedbackLogger;
  private welcomeMessage: string;
  private currentAgent: BaseAgent | null = null;
  private conversationId: string;

  constructor(agentConfig: AgentConfig, callbacks: ChatCallbacks) {
    this.agentConfig = agentConfig;
    this.logger = new Logger();
    const defaultWelcomeMessage = '👋 你好！我是 CodeGen 通用智能体，请告诉我你需要什么帮助？';
    this.welcomeMessage = agentConfig.welcomeMessage || defaultWelcomeMessage;
    this.callbacks = callbacks;
    this.state = {
      messages: [],
      isProcessing: false,
      systemLogs: [],
      currentLogIndex: 0,
      isTyping: false,
    };
    // 直接添加欢迎消息作为独立消息
    this.callbacks.setMessages((prev: ChatMessage[]) => [
      ...prev,
      {
        id: `welcome_${Date.now()}`,
        content: this.welcomeMessage,
        isUser: false,
        timestamp: new Date(),
      },
    ]);
  }

  // 停止执行
  stopExecution() {
    if (this.state.isProcessing) {
      this.state.isProcessing = false;
      this.callbacks.setIsProcessing(false);
      // 更新消息显示
      this.feedbackCard.response('⚠️ 已手动停止运行');
      // 通知 agent 停止执行
      if (this.currentAgent) {
        this.currentAgent.stopExecution();
      }
      // 清空日志
      this.feedbackLogger.stop();
    }
  }

  // 处理发送消息
  async handleSendMessage(content: string) {
    // 生成conversessionId;
    this.conversationId = `sid${uniqueId(null, '', '')}`;
    // 获取附件信息
    const { attachments } = useAIChatStore.getState();
    const newMessage: ChatMessage = {
      id: this.conversationId,
      content,
      isUser: true,
      timestamp: new Date(),
    };
    if (attachments?.length) {
      newMessage.attachments = attachments;
    }
    // 首先添加用户消息
    this.callbacks.setMessages((prev: ChatMessage[]) => [...prev, newMessage]);
    // 创建一条处理请求的消息，同时也作为反馈消息的容器
    const processingMessage: ChatMessage = {
      id: this.conversationId,
      content: '',
      isUser: false,
      timestamp: new Date(),
    };
    // 立即添加处理消息
    this.callbacks.setMessages((prev: ChatMessage[]) => [...prev, processingMessage]);

    // 创建反馈日志器
    this.feedbackCard = new UserFeedbackCard({
      summaryCallback: this.callbacks.setIsSummarizing,
      updateMessageLogs: (logs: FeedbackLogItem[]) => {
        this.updateMessageLogs(logs);
      },
    });
    this.feedbackLogger = new UserFeedbackLogger({
      feedbackCard: this.feedbackCard,
    });
    window.__feedbackLogger = this.feedbackLogger;
    window.__feedbackCard = this.feedbackCard;
    // 创建 agent 实例
    this.currentAgent = FactoryAgent.createAgent({
      ...this.agentConfig,
      memory: this.currentAgent?.memory,
      logger: this.logger,
      feedbackLogger: this.feedbackLogger,
    });

    try {
      // 设置忙碌状态
      this.state.isProcessing = true;
      this.callbacks.setIsProcessing(true);
      // 执行Agent
      const result = await this.currentAgent.run(content);
      this.logger.info(`完成用户请求，答复内容: ${result}`);
    } catch (error) {
      this.logger.error(`处理消息错误: ${error}`);
      // 更新处理消息以显示错误
      const errorMessage = `处理你的请求时出错: ${error instanceof Error ? error.message : String(error)}`;
      this.currentAgent.memory.addMessage(MessageUtils.assistantMessage(errorMessage));
      this.feedbackCard.response(errorMessage);
    } finally {
      // 更新处理状态
      this.state.isProcessing = false;
      this.callbacks.setIsProcessing(false);
      // 发送后清空附件
      const { setAttachments } = useAIChatStore.getState();
      setAttachments([]);
    }
  }

  // 销毁
  destroy() {
    this.agentConfig = null;
    this.feedbackLogger = null;
    this.feedbackCard = null;
    this.welcomeMessage = null;
    this.callbacks = null;
  }

  // 更新规划状态
  private updateMessageLogs(logs: FeedbackLogItem[]): void {
    if (!logs.length) return;
    this.callbacks.setMessages((prev: ChatMessage[]) => {
      const processingIndex = prev.findIndex(
        (msg: ChatMessage) => !msg.isUser && msg.id && msg.id === this.conversationId,
      );
      if (processingIndex >= 0) {
        const updatedMessages = [...prev];
        updatedMessages[processingIndex] = {
          ...updatedMessages[processingIndex],
          content: logs,
          timestamp: new Date(),
        };
        return updatedMessages;
      } else {
        return [
          ...prev,
          {
            id: this.conversationId,
            content: logs,
            isUser: false,
            timestamp: new Date(),
          },
        ];
      }
    });
  }
}
