export class ManusError extends <PERSON>rror {
  constructor(message: string) {
    super(message);
    this.name = 'ManusError';
  }
}

export class ToolExecutionError extends ManusError {
  constructor(toolName: string, message: string) {
    super(`Tool '${toolName}' execution failed: ${message}`);
    this.name = 'ToolExecutionError';
  }
}

export class LLMError extends ManusError {
  constructor(message: string) {
    super(`LLM error: ${message}`);
    this.name = 'LLMError';
  }
}

export class ValidationError extends ManusError {
  errors: string[];

  constructor(message: string, errors: string[] = []) {
    super(`Validation error: ${message}`);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}
