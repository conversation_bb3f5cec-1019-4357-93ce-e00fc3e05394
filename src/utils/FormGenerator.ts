import { omit } from 'lodash';
import {
  IComponentModel,
  ISchema,
  traverseComponentsTree,
  i18nFormat,
  ratiocinateByAI,
  getYidaConfig,
} from '@ali/yc-utils';
import { LLMService } from '@/services/llmService';
import { MessageUtils } from '@/utils/Message';
import {
  getFormSchema,
  saveFormSchema,
  saveFormSchemaInfo,
  deleteFormSchema,
  getFormSchemaInfo,
  getDataViewConfig,
  updateDataViewConfig,
} from '@/apis';
import { FieldType, FIELD_INFO, generateFieldId, queryPageInfo } from '@/utils/YidaMeta';
import { streamType } from '@/types/types';

export interface ISubFieldMeta {
  label: string;
  fieldType: Exclude<FieldType, 'TableField' | 'SerialNumberField'>;
  behavior: 'NORMAL' | 'READONLY' | 'HIDDEN' | 'DISABLED';
  fieldId?: string;
  required?: boolean;
  options?: string[];
  multiple?: boolean;
  relateFormUuid?: string;
}

export interface IFieldMeta {
  label: string;
  fieldType: FieldType;
  behavior: 'NORMAL' | 'READONLY' | 'HIDDEN' | 'DISABLED';
  fieldId?: string;
  required?: boolean;
  options?: string[];
  multiple?: boolean;
  subFields?: ISubFieldMeta[];
  fromImageRecognition?: boolean; // 是否为智能图片识别需要填充的字段
  relateFormUuid?: string;
  bind?: string; // 绑定字段信息
}

export interface IFormMetaInfo {
  formType: 'normalForm' | 'imageAnalysisForm';
  fieldMetaList: IFieldMeta[];
  fieldMap?: Record<string, any>;
  pageConfig?: any;
  formConfig?: any;
  imageRecognitionFieldList?: string[];
}

const aiRecognitionPrompt = `{{render "tools.modify-form"}}`;
const transformFieldMetaPrompt = `{{render "tools.create-form"}}`;

export class FormGenerator {
  static validateFormMeta = (formMeta: IFormMetaInfo): string[] => {
    const errors: string[] = [];
    const { formType, fieldMetaList } = formMeta;
    if (!formType) {
      errors.push('表单类型不能为空');
    } else if (!['normalForm', 'imageAnalysisForm'].includes(formType)) {
      errors.push('表单类型必须为 normalForm 或 imageAnalysisForm');
    }

    if (!fieldMetaList?.length) {
      errors.push('表单必须至少包含一个字段');
    }

    if (formType === 'imageAnalysisForm') {
      if (fieldMetaList?.[0]?.fieldType !== 'ImageField') {
        errors.push('智能图片识别表单必须将ImageField作为首个字段');
      }
      if (fieldMetaList?.length < 2) {
        errors.push('智能图片识别表单必须包含至少两个字段');
      }

      // 检查是否至少有一个需要从图片识别填充的字段
      const hasRecognitionField = fieldMetaList.some(
        ({ fieldType, fromImageRecognition }) => fieldType !== 'ImageField' && fromImageRecognition,
      );

      if (!hasRecognitionField) {
        errors.push('智能图片识别表单必须至少有一个设置了fromImageRecognition=true的非图片字段');
      }
    }

    (fieldMetaList || []).forEach((field: any, index: number) => {
      const { label, fieldType, options, subFields, relateFormUuid } = field;
      if (!label) {
        errors.push(`字段 #${index + 1} 的标签不能为空`);
      }

      if (!fieldType) {
        errors.push(`字段 #${index + 1} 的类型不能为空`);
      } else if (!Object.keys(FIELD_INFO).includes(fieldType)) {
        errors.push(`字段 #${index + 1} 的类型 '${fieldType}' 无效`);
      }

      // 检查需要提供选项的字段类型
      if (['RadioField', 'SelectField', 'CheckboxField', 'MultiSelectField'].includes(fieldType) && !options?.length) {
        errors.push(`字段 #${index + 1} 是 ${fieldType} 类型，必须提供选项`);
      }

      if (fieldType === 'AssociationFormField' && !relateFormUuid) {
        errors.push(`字段 #${index + 1} 是 ${fieldType} 类型，必须提供关联页面`);
      }

      // 检查TableField是否包含子字段
      if (fieldType === 'TableField' && !subFields?.length) {
        errors.push(`字段 #${index + 1} 是 TableField 类型，必须提供子字段`);
      }
    });

    return errors;
  };

  private appId: string;
  private formUuid: string;
  private schema?: ISchema;
  private newSchema?: ISchema;
  private meta: IFormMetaInfo;
  private llmService: LLMService;

  constructor(appId: string, formUuid?: string) {
    this.appId = appId;
    this.formUuid = formUuid;
    this.llmService = new LLMService({
      model: 'deepseek-chat',
    });

    this.meta = {
      formType: 'normalForm',
      fieldMetaList: [],
      fieldMap: {},
      pageConfig: {},
      formConfig: {},
      imageRecognitionFieldList: [],
    };
  }

  // 初始化表单,获取表单Schema并进行解构
  init = async () => {
    if (!this.appId || !this.formUuid) return;
    const schemaRes = await getFormSchema({ appType: this.appId, formUuid: this.formUuid });
    if (!schemaRes) {
      throw new Error('表单不存在');
    }
    this.schema = schemaRes as ISchema;
    this.parseFormSchema();
  };

  setMeta = (meta: Partial<IFormMetaInfo>) => {
    this.meta = {
      ...this.meta,
      ...meta,
    };
  };

  // 重置表单Schema解构结果
  reset = () => {
    this.parseFormSchema();
  };

  create = async (title: string) => {
    const { appId } = this;
    const createRes = await saveFormSchemaInfo({
      appType: appId,
      formType: 'receipt',
      title,
    });

    if (!createRes) {
      throw new Error('表单创建失败');
    }
    this.formUuid = createRes.formUuid;
    return createRes.formUuid;
  };

  delete = async () => {
    const { appId, formUuid } = this;
    const res = await deleteFormSchema({
      appType: appId,
      formUuid,
    });
    if (!res) {
      throw new Error('表单删除失败');
    }
  };

  // 保存生成的表单Schema
  saveSchema = async () => {
    const { appId, formUuid } = this;
    if (!formUuid) {
      throw new Error('表单UUID不存在');
    }
    if (!this.newSchema) {
      throw new Error('表单Schema不存在');
    }
    const res = await saveFormSchema({
      appType: appId,
      formUuid,
      schemaVersion: 'V5',
      content: this.newSchema,
    });
    if (!res) {
      throw new Error('表单保存失败');
    }
    return res;
  };

  hideDataManageSystemColumn = async (formUuid: string) => {
    const { appId } = this;
    const HIDDEN_FIELDS = ['processInstanceTitle', 'originatorCorp', 'modifiedTime', 'processInstanceId'];
    const pageInfo = await getFormSchemaInfo({
      appType: appId,
      formUuid,
    });
    if (!pageInfo) {
      throw new Error('页面不存在');
    }
    for (const view of pageInfo.multiViewUuids) {
      const viewConfig = await getDataViewConfig({
        appType: appId,
        formUuid,
        viewUuid: view,
      });
      const showFields = viewConfig.componentValueVoList
        .filter((item) => item.supportVisible && !HIDDEN_FIELDS.includes(item.id))
        .map((item) => item.id);
      await updateDataViewConfig({
        appType: appId,
        formUuid,
        viewUuid: view,
        config: {
          ...viewConfig.viewConfig,
          showFields,
        },
      });
    }
  };

  generateDataManage = async (title: string, hideSystemColumn?: boolean) => {
    const { appId, formUuid } = this;
    const res = await saveFormSchemaInfo({
      appType: appId,
      formType: 'view', // 固定为view类型
      title,
      relateFormUuid: formUuid,
      relateFormType: 'receipt',
    });
    if (!res) {
      throw new Error('数据管理页创建失败');
    }
    if (hideSystemColumn) {
      await this.hideDataManageSystemColumn(res.formUuid);
    }
  };

  // 修改优化提示词的处理函数
  optimizeAIRecognitionPrompt = async (prompt: string): Promise<string> => {
    const tarPrompt = `
    ${aiRecognitionPrompt}

    用户输入:
    ${prompt}
    `;
    const res = await ratiocinateByAI(
      {
        prompt: tarPrompt,
        maxTokens: 2000,
        skill: 'ToData',
      },
      () => {},
    );
    if (!res?.prompt) {
      throw new Error('请稍后重试');
    }
    return res.prompt;
  };

  // 生成智能图片识别的props
  generateAIRecognitionProps = async (): Promise<any | null> => {
    const { fieldMetaList } = this.meta;
    const formatFields = (fieldList: IFieldMeta[]) => {
      return fieldList
        .map((f: IFieldMeta) => {
          // 过滤掉图片字段以及不需要从图片识别填充的字段
          const fieldInfo = FIELD_INFO[f.fieldType];
          if (!fieldInfo || f.fieldType === 'ImageField' || !f.fromImageRecognition) {
            return null;
          }
          let tarDataType = fieldInfo.type;
          if (tarDataType === 'string' && f.multiple) {
            tarDataType = 'string[]';
          }
          const oneField: any = {
            componentType: f.fieldType,
            dataType: tarDataType,
            label: f.label,
            fieldId: f.fieldId,
          };
          if (f.options) oneField.options = f.options;
          if (f.subFields) oneField.subFields = formatFields(f.subFields);
          return oneField;
        })
        .filter((f: any) => !!f);
    };
    const tarFields = formatFields(fieldMetaList);
    const fieldDesc = JSON.stringify(tarFields, null, 2);
    const strPrompt = `帮我对图片进行智能识别，并返回以下字段数据填充到表单中：\n${fieldDesc}\n 特别注意：输出示例的字段名称，必须和描述的字段label完全保持一致。根据场景不同允许对识别结果的准确性做微调，允许预估结果。`;
    const tarImageAnalysisPrompt = await this.optimizeAIRecognitionPrompt(strPrompt);
    return {
      aiRecognitionSwitch: true,
      aiRecognitionConfig: {
        fields: tarFields,
        prompt: tarImageAnalysisPrompt,
        testImage: [],
        testResult: {},
      },
    };
  };

  // 将表单项转换为meta信息
  transformFieldSchemaToMeta = (node: IComponentModel) => {
    const { meta } = this;
    const { fieldMap, imageRecognitionFieldList } = meta;
    const { componentName, props = {} } = node;
    const {
      fieldId,
      label,
      behavior = 'NORMAL',
      validation,
      multiple,
      mode,
      children,
      dataSource,
      associationForm,
      __bind__,
    } = props;
    fieldMap[fieldId] = {
      componentName,
      props,
    };
    const fieldMeta: IFieldMeta = {
      label: i18nFormat(label),
      fieldType: componentName as FieldType,
      fieldId,
      required: validation.some((item: any) => item.type === 'required'),
      behavior,
    };
    if (imageRecognitionFieldList.includes(fieldId)) {
      fieldMeta.fromImageRecognition = true;
    }
    if (__bind__) {
      fieldMeta.bind = __bind__;
    }
    if (mode === 'multiple' || multiple) {
      fieldMeta.multiple = true;
    }

    if (['CheckboxField', 'RadioField', 'SelectField', 'MultiSelectField'].includes(componentName)) {
      fieldMeta.options = (dataSource || []).map((i: any) => i.value);
    }

    if (['CheckboxField', 'MultiSelectField'].includes(componentName)) {
      fieldMeta.multiple = true;
    }

    if (['AssociationFormField'].includes(componentName)) {
      fieldMeta.relateFormUuid = associationForm?.formUuid;
    }

    if (componentName === 'TableField') {
      fieldMeta.subFields = children?.map((child: IComponentModel) => this.transformFieldSchemaToMeta(child));
    }
    return fieldMeta;
  };

  // 将表单项的meta信息转换为schema
  transformFieldMetaToSchema = async (fieldMeta: IFieldMeta): Promise<IComponentModel | null> => {
    const { appId, formUuid } = this;
    const { fieldMap } = this.meta;
    const { label, fieldId, fieldType, behavior, required, options, multiple, subFields, relateFormUuid } =
      fieldMeta || {};
    if (!FIELD_INFO[fieldType] || !label) return null;
    const id = fieldId || generateFieldId(fieldType);
    fieldMeta.fieldId = id;
    const compConfig = fieldMap?.[id] || {};
    const schema: IComponentModel = {
      componentName: fieldType,
      props: {
        ...(compConfig.props || {}),
        label: {
          type: 'i18n',
          zh_CN: label,
          en_US: label,
        },
        fieldId: id,
        __category__: 'form',
        behavior: behavior || 'NORMAL',
        visibility: ['PC', 'MOBILE'],
        submittable: 'DEFAULT',
      },
    };
    if (required && !(schema.props.validation || []).some((item: any) => item.type === 'required')) {
      schema.props.validation = schema.props.validation || [];
      schema.props.validation.push({ type: 'required' });
    } else if (!required) {
      schema.props.validation = (schema.props.validation || []).filter((item: any) => item.type !== 'required');
    }
    if (fieldType === 'TextareaField') {
      schema.props.rows = 4;
      schema.props.htmlType = 'textarea';
    } else if (['RadioField', 'CheckboxField', 'SelectField', 'MultiSelectField'].includes(fieldType)) {
      if (Array.isArray(options) && options?.length) {
        const isCheckbox = ['CheckboxField', 'MultiSelectField'].includes(fieldType);
        schema.props.dataSource = options.map((optName: any, index: number) => {
          if (index === 0) schema.props.value = optName;
          return {
            text: { zh_CN: optName, en_US: optName, type: 'i18n' },
            value: optName,
            defaultChecked: isCheckbox ? false : index === 0,
          };
        });
      }
    } else if (['EmployeeField', 'CountrySelectField', 'DepartmentSelectField'].includes(fieldType)) {
      if (multiple) {
        schema.props.multiple = true;
        schema.props.mode = 'multiple';
      }
    } else if (fieldType === 'AddressField') {
      if (!schema.props.addressType) {
        schema.props.addressType = 'ADDRESS';
      }
    } else if (fieldType === 'AttachmentField') {
      schema.props.autoUpload = true;
      schema.props.maxFileSize = 100;
    } else if (fieldType === 'SerialNumberField') {
      if (!schema.props.serialNumberRule) {
        schema.props.serialNumberRule = [
          {
            __hide_delete__: false,
            ruleType: 'date',
            content: '',
            formField: '',
            dateFormat: 'yyyyMMdd',
            timeZone: '+8',
            digitCount: 4,
            isFixed: true,
            isFixedTips: '',
            resetPeriod: 'noClean',
            resetPeriodTips: '',
            initialValue: 1,
          },
          {
            __hide_delete__: true,
            ruleType: 'autoCount',
            content: '',
            formField: '',
            dateFormat: 'yyyyMMdd',
            timeZone: '+8',
            digitCount: '4',
            isFixed: true,
            isFixedTips: '',
            resetPeriod: 'noClean',
            resetPeriodTips: '',
            initialValue: 1,
          },
        ];
        schema.props.formula = {
          expression: `SERIALNUMBER("${getYidaConfig('corpId')}", "${appId}", "${formUuid}", "${id}", "${JSON.stringify(
            {
              type: 'custom',
              value: schema.props.serialNumberRule,
            },
          ).replace(/"/g, '\\"')}")`,
        };
        schema.props.validation = (schema.props.validation || []).filter((item: any) => item.type !== 'required');
      }
    } else if (fieldType === 'TableField') {
      schema.props.layout = 'TABLE';
      schema.props.mobileLayout = 'TILED';
      schema.props.maxItems = 50;
      schema.children = await Promise.all(
        (subFields || []).map(async (subField) => await this.transformFieldMetaToSchema(subField)),
      );
    } else if (fieldType === 'AssociationFormField') {
      const relatePageInfo = await queryPageInfo(appId, relateFormUuid);
      // const { associationForm } = compConfig.props || {};
      if (relatePageInfo) {
        const { pageId, type, name, fieldList } = relatePageInfo;
        const relateFieldList = fieldList.filter((item) => item.fieldId.startsWith('textField'));
        // const relateFieldList = fieldList;
        schema.props = {
          ...schema.props,
          associationForm: {
            appType: appId,
            formUuid: pageId,
            formType: type,
            formTitle: i18nFormat(name),
            mainFieldId: relateFieldList[0]?.fieldId,
            mainComponentName: relateFieldList[0]?.type,
            mainFieldLabel: relateFieldList[0]?.label,
          },
          dataFilterRules: {
            instanceFieldId: null,
          },
          dataFillingRules: '',
        };
      }
    }

    return schema;
  };

  // 解析表单Schema
  parseFormSchema = () => {
    const { meta } = this;

    const rootNode: IComponentModel = this.schema?.pages?.[0]?.componentsTree?.[0];
    if (!rootNode) {
      throw new Error('表单Schema解析失败');
    }

    traverseComponentsTree(rootNode, (component): boolean => {
      const { componentName, props = {} } = component;
      const { __category__, children } = props;
      if (componentName === 'Page') {
        meta.pageConfig = component;
      } else if (componentName === 'FormContainer') {
        meta.formConfig = component;
        const aiImageField = (children || []).find(
          (child: IComponentModel) => child.componentName === 'ImageField' && child.props?.aiRecognitionSwitch,
        );
        if (aiImageField) {
          meta.formType = 'imageAnalysisForm';
          meta.imageRecognitionFieldList = (aiImageField.props?.aiRecognitionConfig?.fields || []).map(
            (item: any) => item.fieldId,
          );
        }
      } else if (__category__ === 'form') {
        const fieldMeta = this.transformFieldSchemaToMeta(component);
        meta.fieldMetaList.push(fieldMeta);
        if (componentName === 'TableField') {
          return false;
        }
      }
      return true;
    });
    return meta;
  };

  // 通过大模型转换表单Meta信息
  transformFormMetaInfo = async (prompt: string, callback?: (type: streamType, data: any) => void) => {
    const { formType = 'normalForm', fieldMetaList = [] } = this.meta;
    const userPrompt = `
    请按照下面描述修改表:
    ${prompt}

    当前表单信息为：
\`\`\`json
${JSON.stringify({ formType, fieldList: fieldMetaList }, null, 2)}
\`\`\`

    `;

    try {
      console.log('+++++old meta', this.meta);
      const res = await this.llmService.askStream<{
        fieldsInfo: string;
      }>(
        [MessageUtils.userMessage(userPrompt)],
        [MessageUtils.systemMessage(transformFieldMetaPrompt)],
        [],
        'none',
        callback,
        true,
        {
          context: {
            fieldsInfo: `${Object.entries(FIELD_INFO)
              .map(([key, value]) => `- ${key}：${value.type}，${value.desc}`)
              .join('\n')}`,
          },
        },
      );
      const content = JSON.parse(res.content);
      this.meta.formType = content.formType;
      this.meta.fieldMetaList = content.fieldList;
      console.log('+++++new meta', this.meta);
      return this.meta;
    } catch (err) {
      console.error('转换表单Meta信息失败', err);
      return null;
    }
  };

  generateFormSchema = async (): Promise<ISchema> => {
    const { fieldMetaList, formType } = this.meta;
    const fieldSchemaList = [];
    for (let index = 0; index < fieldMetaList.length; index++) {
      const meta = fieldMetaList[index];
      const { label, fieldType, multiple } = meta;
      const fieldSchema = await this.transformFieldMetaToSchema(meta);
      if (index === 0 && fieldType === 'EmployeeField' && !multiple) {
        fieldSchemaList.push({
          componentName: 'TextField',
          props: {
            label: {
              type: 'i18n',
              zh_CN: `${label}(姓名)`,
              en_US: `${label}(Name)`,
            },
            fieldId: generateFieldId('TextField'),
            __category__: 'form',
            __bind__: `${fieldSchema.props.fieldId}.name`,
            behavior: 'READONLY',
            visibility: ['PC', 'MOBILE'],
            submittable: 'DEFAULT',
            complexValue: {
              complexType: 'formula',
              formula: `USERFIELD(#{${fieldSchema.props.fieldId}}, "name")`,
            },
            valueType: 'formula',
          },
        });
      }

      fieldSchemaList.push(fieldSchema);
    }

    if (formType === 'imageAnalysisForm') {
      const imageFieldComp = fieldSchemaList.find((item) => item.componentName === 'ImageField');
      if (!imageFieldComp) {
        throw new Error('智能图片识别表单必须包含ImageField类型的字段');
      }
      const aiRecognitionProps = await this.generateAIRecognitionProps();
      imageFieldComp.props = {
        ...imageFieldComp.props,
        ...aiRecognitionProps,
      };
    }
    this.newSchema = this.buildFormSchema(fieldSchemaList);
    return this.newSchema;
  };

  buildFormSchema = (fieldNodeList: IComponentModel[]): ISchema => {
    const { pageConfig, formConfig } = this.meta;
    return {
      schemaType: 'superform',
      schemaVersion: '5.0',
      pages: [
        {
          utils: [
            {
              name: 'legaoBuiltin',
              type: 'npm',
              content: {
                package: '@ali/vu-legao-builtin',
                version: '3.0.0',
                exportName: 'legaoBuiltin',
              },
            },
            {
              name: 'yidaPlugin',
              type: 'npm',
              content: {
                package: '@ali/vu-yida-plugin',
                version: '1.0.13',
                exportName: 'yidaPlugin',
              },
            },
          ],
          componentsMap: [],
          componentsTree: [
            {
              componentName: 'Page',
              props: {
                templateVersion: '1.0.0',
                pageStyle: {
                  backgroundColor: '#f2f3f5',
                },
                titleName: {
                  type: 'i18n',
                  zh_CN: '标题名称',
                  en_US: 'title',
                },
                titleDesc: {
                  type: 'i18n',
                  zh_CN: '标题描述',
                  en_US: 'title',
                },
                titleColor: 'light',
                titleBg:
                  'https://img.alicdn.com/imgextra/i2/O1CN0143ATPP1wIa9TrVvzN_!!6000000006285-2-tps-3360-400.png_.webp',
                backgroundColorCustom: '#f1f2f3',
                sizePc: 'medium',
                labelAlignPc: 'top',
                labelWidthPc: '130px',
                labelWeightPc: 'normal',
                contentMargin: '12',
                contentPadding: '20',
                contentBgColor: 'white',
                showTitle: true,
                labelAlignMobile: 'left',
                labelWidthMobile: '100px',
                labelWeightMobile: 'bold',
                contentMarginMobile: '12',
                contentPaddingMobile: '0',
                contentBgColorMobile: 'white',
                className: 'page_m8o991i5',
              },
              dataSource: {
                offline: [],
                globalConfig: {
                  fit: {
                    compiled:
                      "'use strict';\n\nvar __preParser__ = function fit(response) {\n  var content = response.content !== undefined ? response.content : response;\n  var error = {\n    message: response.errorMsg || response.errors && response.errors[0] && response.errors[0].msg || response.content || '远程数据源请求出错，success is false'\n  };\n  var success = true;\n  if (response.success !== undefined) {\n    success = response.success;\n  } else if (response.hasError !== undefined) {\n    success = !response.hasError;\n  }\n  return {\n    content: content,\n    success: success,\n    error: error\n  };\n};",
                    source:
                      "function fit(response) {\r\n  const content = (response.content !== undefined) ? response.content : response;\r\n  const error = {\r\n    message: response.errorMsg ||\r\n      (response.errors && response.errors[0] && response.errors[0].msg) ||\r\n      response.content || '远程数据源请求出错，success is false',\r\n  };\r\n  let success = true;\r\n  if (response.success !== undefined) {\r\n    success = response.success;\r\n  } else if (response.hasError !== undefined) {\r\n    success = !response.hasError;\r\n  }\r\n  return {\r\n    content,\r\n    success,\r\n    error,\r\n  };\r\n}",
                    type: 'js',
                    error: {},
                  },
                },
                online: [],
                list: [],
                sync: true,
              },
              methods: {
                __initMethods__: {
                  type: 'js',
                  source: 'function (exports, module) { /*set actions code here*/ }',
                  compiled: 'function (exports, module) { /*set actions code here*/ }',
                },
              },
              lifeCycles: {
                componentDidMount: {
                  id: 'didMount',
                  name: 'didMount',
                  params: {},
                  type: 'actionRef',
                },
                componentWillUnmount: '',
                constructor: {
                  type: 'js',
                  compiled:
                    "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
                  source:
                    "function constructor() {\nvar module = { exports: {} };\nvar _this = this;\nthis.__initMethods__(module.exports, module);\nObject.keys(module.exports).forEach(function(item) {\n  if(typeof module.exports[item] === 'function'){\n    _this[item] = module.exports[item];\n  }\n});\n\n}",
                },
              },
              hidden: false,
              title: '',
              isLocked: false,
              condition: true,
              conditionGroup: '',
              children: [
                {
                  componentName: 'RootHeader',
                  id: 'node_ocm8o98bes2',
                  props: {},
                  hidden: false,
                  title: '',
                  isLocked: false,
                  condition: true,
                  conditionGroup: '',
                },
                {
                  componentName: 'RootContent',
                  id: 'node_ocm8o98bes3',
                  props: {},
                  hidden: false,
                  title: '',
                  isLocked: false,
                  condition: true,
                  conditionGroup: '',
                  children: [
                    {
                      componentName: 'FormContainer',
                      id: 'node_ocm8o98bes4',
                      props: {
                        columns: 1,
                        labelAlign: 'top',
                        submitText: {
                          type: 'i18n',
                          zh_CN: '提交',
                          en_US: 'Submit',
                        },
                        fieldId: 'formContainer_m8o98esn',
                        aiFormConfig: {
                          systemPrompt: '',
                          model: 'qwen',
                        },
                        beforeSubmit: false,
                        afterSubmit: false,
                      },
                      hidden: false,
                      title: '',
                      isLocked: false,
                      condition: true,
                      conditionGroup: '',
                      children: fieldNodeList,
                      ...omit(formConfig, ['componentName', 'children']),
                    },
                  ],
                },
                {
                  componentName: 'RootFooter',
                  id: 'node_ocm8o98bes5',
                  props: {},
                  hidden: false,
                  title: '',
                  isLocked: false,
                  condition: true,
                  conditionGroup: '',
                },
              ],
              css: 'body{background-color:#f2f3f5}',
              ...omit(pageConfig, ['componentName', 'children']),
            },
          ],
          componentAlias: {
            items: [],
          },
          id: 'xxxx',
          connectComponent: [],
        },
      ],
      actions: {
        module: {
          source:
            "/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系宜搭平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能，比如：调用阿里云接口用来做图像识别、上报用户使用数据（如加载完成打点）等等。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nexport function didMount() {\n  console.log(`「页面 JS」：当前页面地址 ${location.href}`);\n  // console.log(`「页面 JS」：当前页面 id 参数为 ${this.state.urlParams.id}`);\n  // 更多 this 相关 API 请参考：https://www.yuque.com/yida/support/ocmxyv#OCEXd\n  // document.title = window.loginUser.userName + ' | 宜搭';\n}",
          compiled:
            '"use strict";\n\nexports.__esModule = true;\nexports.didMount = didMount;\n/**\n* 尊敬的用户，你好：页面 JS 面板是高阶用法，一般不建议普通用户使用，如需使用，请确定你具备研发背景，能够自我排查问题。当然，你也可以咨询身边的技术顾问或者联系宜搭平台的技术支持获得服务（可能收费）。\n* 我们可以用 JS 面板来开发一些定制度高功能，比如：调用阿里云接口用来做图像识别、上报用户使用数据（如加载完成打点）等等。\n* 你可以点击面板上方的 「使用帮助」了解。\n*/\n\n// 当页面渲染完毕后马上调用下面的函数，这个函数是在当前页面 - 设置 - 生命周期 - 页面加载完成时中被关联的。\nfunction didMount() {\n  console.log("\\u300C\\u9875\\u9762 JS\\u300D\\uFF1A\\u5F53\\u524D\\u9875\\u9762\\u5730\\u5740 " + location.href);\n  // console.log(`「页面 JS」：当前页面 id 参数为 ${this.state.urlParams.id}`);\n  // 更多 this 相关 API 请参考：https://www.yuque.com/yida/support/ocmxyv#OCEXd\n  // document.title = window.loginUser.userName + \' | 宜搭\';\n}\n',
        },
        type: 'FUNCTION',
        list: [
          {
            id: 'didMount',
            title: 'didMount',
          },
        ],
      },
      config: {
        connectComponent: [],
      },
    };
  };
}
