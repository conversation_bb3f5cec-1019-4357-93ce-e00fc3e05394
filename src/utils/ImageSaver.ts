import { Message } from '@alifd/next';
import html2canvas from 'html2canvas';

/**
 * 图片保存工具类，用于将DOM元素转换为图片并保存
 */
export class ImageSaver {
  private messageCallback: (type: string, content: string) => void;

  /**
   * 构造函数
   * @param messageCallback 消息回调函数，用于显示操作状态
   */
  constructor(messageCallback?: (type: string, content: string) => void) {
    // 如果没有提供回调函数，默认使用 Message 组件
    this.messageCallback =
      messageCallback ||
      ((type, content) => {
        if (type === 'success') {
          Message.success(content);
        } else if (type === 'error') {
          Message.error(content);
          console.error(content);
        }
      });
  }

  /**
   * 将DOM元素保存为图片
   * @param element 目标DOM元素
   * @param options 配置选项
   * @returns Promise<boolean> 操作是否成功
   */
  async saveElementAsImage(
    element: HTMLElement,
    options: {
      hideSelector?: string /** 转换时需要隐藏的元素选择器 */;
      filename?: string /** 下载时的文件名（不支持剪贴板时使用） */;
      scrollElement?: HTMLElement /** 需要临时处理滚动的元素 */;
      scale?: number /** 图片缩放倍数，默认3 */;
    } = {},
  ): Promise<boolean> {
    if (!element) {
      this.messageCallback('error', '无法找到目标元素');
      return false;
    }

    try {
      // 找到需要隐藏的元素
      const elementsToHide: NodeListOf<Element> = options.hideSelector
        ? element.querySelectorAll(options.hideSelector)
        : document.createDocumentFragment().querySelectorAll('*');

      // 存储原始的滚动位置
      const originalScrollTop = options.scrollElement ? options.scrollElement.scrollTop : 0;

      // 隐藏指定元素
      elementsToHide.forEach((el) => {
        (el as HTMLElement).style.display = 'none';
      });

      // 对于聊天容器这样的滚动元素，我们可能需要临时修改其样式以捕获所有内容
      if (options.scrollElement) {
        options.scrollElement.style.overflow = 'visible';
        options.scrollElement.style.height = 'auto';
      }

      // 添加提示信息
      this.messageCallback('success', '正在生成高清图片，请稍候...');

      try {
        // 使用html2canvas生成图片，提高图像质量
        const canvas = await html2canvas(element, {
          scale: options.scale || 3, // 提高到3倍缩放以获得更清晰的图像
          useCORS: true, // 允许跨域加载图片
          logging: false, // 不输出日志
          allowTaint: true, // 允许跨域图片污染画布
          backgroundColor: '#ffffff', // 设置白色背景
          imageTimeout: 0, // 不设置图片加载超时
          removeContainer: false, // 不移除临时容器
          foreignObjectRendering: false, // 禁用foreignObject以提高兼容性
        });

        // 恢复隐藏的元素
        elementsToHide.forEach((el) => {
          (el as HTMLElement).style.display = '';
        });

        // 恢复滚动元素的样式和位置
        if (options.scrollElement) {
          options.scrollElement.style.overflow = '';
          options.scrollElement.style.height = '';
          options.scrollElement.scrollTop = originalScrollTop;
        }

        // 尝试将图片保存到剪贴板
        return await this.processCanvas(canvas, options.filename || `chat-${Date.now()}.png`);
      } catch (canvasError) {
        // 恢复隐藏的元素
        elementsToHide.forEach((el) => {
          (el as HTMLElement).style.display = '';
        });

        // 恢复滚动元素的样式和位置
        if (options.scrollElement) {
          options.scrollElement.style.overflow = '';
          options.scrollElement.style.height = '';
          options.scrollElement.scrollTop = originalScrollTop;
        }

        console.error('生成图片失败:', canvasError);
        this.messageCallback('error', '生成图片失败');
        return false;
      }
    } catch (error) {
      console.error('保存图片失败:', error);
      this.messageCallback('error', '保存图片失败');
      return false;
    }
  }

  /**
   * 处理canvas对象，尝试复制到剪贴板或下载
   * @param canvas Canvas元素
   * @param filename 下载文件名
   * @returns Promise<boolean> 操作是否成功
   */
  private async processCanvas(canvas: HTMLCanvasElement, filename: string): Promise<boolean> {
    return new Promise((resolve) => {
      // 使用较高的图片质量
      canvas.toBlob(
        (blob: Blob | null) => {
          if (!blob) {
            this.messageCallback('error', '创建图片blob失败');
            resolve(false);
            return;
          }

          // 检查是否支持剪贴板API
          if (navigator.clipboard && navigator.clipboard.write) {
            const item = new ClipboardItem({ 'image/png': blob });
            navigator.clipboard
              .write([item])
              .then(() => {
                this.messageCallback('success', '高清图片已保存到剪贴板');
                resolve(true);
              })
              .catch((err) => {
                console.error('无法保存图片到剪贴板:', err);
                this.messageCallback('error', '无法保存图片到剪贴板');

                // 尝试下载方式
                this.downloadImage(canvas, filename);
                resolve(false);
              });
          } else {
            // 如果不支持Clipboard API，提供下载选项
            this.downloadImage(canvas, filename);
            resolve(true);
          }
        },
        'image/png',
        1.0,
      ); // 使用最高质量
    });
  }

  /**
   * 下载图片的辅助方法
   * @param canvas Canvas元素
   * @param filename 文件名
   */
  private downloadImage(canvas: HTMLCanvasElement, filename: string): void {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png', 1.0); // 使用最高质量
    link.click();
    this.messageCallback('success', '高清图片已下载');
  }

  /**
   * 创建处理点击事件的处理函数
   * @param selector 消息内容元素的选择器
   * @param hideSelector 需要隐藏的元素的选择器
   * @returns 事件处理函数
   */
  createClickHandler(selector: string, hideSelector: string): (e: React.MouseEvent) => Promise<void> {
    return async (e: React.MouseEvent) => {
      const currentTarget = e.currentTarget as HTMLElement;
      const parentElement = currentTarget.closest(selector) as HTMLElement;

      if (!parentElement) {
        this.messageCallback('error', '无法找到消息内容元素');
        return;
      }

      await this.saveElementAsImage(parentElement, { hideSelector });
    };
  }
}
