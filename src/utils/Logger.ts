/* eslint-disable no-console */
import { LogLevel } from '../types/types';

export class Logger {
  // 日志级别
  private level: LogLevel;

  constructor(level: LogLevel = 'info') {
    this.level = level;
  }

  debug(...args: any[]): void {
    this.log('debug', ...args);
  }

  info(...args: any[]): void {
    this.log('info', ...args);
  }

  warning(...args: any[]): void {
    this.log('warning', ...args);
  }

  error(...args: any[]): void {
    this.log('error', ...args);
  }

  private shouldLog(messageLevel: LogLevel): boolean {
    const levels: { [key in LogLevel]: number } = {
      debug: 0,
      info: 1,
      warning: 2,
      error: 3,
    };
    return levels[messageLevel] >= levels[this.level];
  }

  private formatMessage(level: LogLevel): string {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] [${level.toUpperCase()}]`;
  }

  private log(level: LogLevel, ...args: any[]): void {
    if (this.shouldLog(level)) {
      const formattedMessage = this.formatMessage(level);

      // 控制台输出
      switch (level) {
        case 'debug':
          console.debug(formattedMessage, ...args);
          break;
        case 'info':
          console.info(formattedMessage, ...args);
          break;
        case 'warning':
          console.warn(formattedMessage, ...args);
          break;
        case 'error':
          console.error(formattedMessage, ...args);
          break;
        default:
          break;
      }
    }
  }
}
