import { Message } from '@/types/types';

export class Memory {
  private messages: Message[] = [];
  private maxMessages: number;

  constructor(maxMessages = 100) {
    this.maxMessages = maxMessages;
  }

  addMessage(message: Message): void {
    this.messages.push(message);
    // 实现消息数量限制
    if (this.messages.length > this.maxMessages) {
      this.messages = this.messages.slice(-this.maxMessages);
    }
  }

  addMessages(messages: Message[]): void {
    this.messages.push(...messages);
    // 实现消息数量限制
    if (this.messages.length > this.maxMessages) {
      this.messages = this.messages.slice(-this.maxMessages);
    }
  }

  clear(): void {
    this.messages = [];
  }

  clearPlanUserMessage(): void {
    this.messages = this.messages.filter((msg) => {
      return !(msg.role === 'user' && msg.isPlanMsg);
    });
  }

  getRecentMessages(n: number): Message[] {
    return this.messages.slice(-n);
  }

  getAllMessages(): Message[] {
    return [...this.messages];
  }

  removeLastMessage(): void {
    this.messages.pop();
  }

  toDictList(): Array<Record<string, any>> {
    return this.messages.map((msg) => {
      const dict: Record<string, any> = {
        role: msg.role,
      };

      if (msg.content !== undefined) {
        dict.content = msg.content;
      }
      if (msg.name !== undefined) {
        dict.name = msg.name;
      }
      if (msg.toolCallId !== undefined) {
        dict.tool_call_id = msg.toolCallId;
      }

      return dict;
    });
  }
}
