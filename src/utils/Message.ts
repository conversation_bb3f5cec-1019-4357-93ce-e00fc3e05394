import { ToolCall } from '@/types/types';

export interface Message {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content?: string;
  name?: string;
  toolCallId?: string;
  toolCalls?: ToolCall[];
  isPlanMsg?: boolean;
}

export class MessageUtils {
  static userMessage(content: string, options?: { isPlanMsg?: boolean }): Message {
    return { role: 'user', content, isPlanMsg: options?.isPlanMsg };
  }

  static systemMessage(content: string): Message {
    return { role: 'system', content };
  }

  static assistantMessage(content?: string): Message {
    return { role: 'assistant', content };
  }

  static toolMessage(content: string, name: string, toolCallId: string): Message {
    return {
      role: 'tool',
      content,
      name,
      toolCallId,
    };
  }

  static fromToolCalls(
    toolCalls: ToolCall[],
    content: string | string[] = '',
    additionalProps: Partial<Message> = {},
  ): Message {
    const formattedCalls = toolCalls.map((call) => ({
      id: call.id,
      type: 'function',
      function: {
        name: call.function.name,
        arguments: call.function.arguments,
      },
    }));

    return {
      role: 'assistant',
      content: Array.isArray(content) ? content.join('\n') : content,
      toolCalls: formattedCalls,
      ...additionalProps,
    };
  }

  static toDict(message: Message): Record<string, any> {
    const dict: Record<string, any> = {
      role: message.role,
    };

    if (message.content !== undefined) {
      dict.content = message.content;
    }
    if (message.toolCalls !== undefined) {
      dict.tool_calls = message.toolCalls.map((call) => ({
        id: call.id,
        type: 'function',
        function: {
          name: call.function.name,
          arguments: call.function.arguments,
        },
      }));
    }
    if (message.name !== undefined) {
      dict.name = message.name;
    }
    if (message.toolCallId !== undefined) {
      dict.tool_call_id = message.toolCallId;
    }

    return dict;
  }
}
