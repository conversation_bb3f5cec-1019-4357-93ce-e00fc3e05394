import { create<PERSON><PERSON><PERSON> } from 'alova';
import adaptorFetch from 'alova/fetch';
import Cookies from 'js-cookie';
import { AnyARecord } from 'node:dns';
export const alovaInst = createAlova({
  baseURL: '//polymind.alibaba.net',
  cacheFor: {
    GET: 0,
  },
  beforeRequest: (method: any) => {
    // method.config.headers['_csrf_token'] = Cookies.get('_csrf_token');
  },
  responded: async (response: any, method: AnyARecord) => {
    const json = await response.json();
    if (json.success === true) {
      return json.content;
    } else {
      throw new Error(json.errorMsg || '系统繁忙，请稍后再试');
    }
  },
  requestAdapter: adaptorFetch(),
});
// 扩展File类型以支持额外属性
interface EnhancedFile extends File {
  key?: string;
  url?: string;
  imgURL?: string;
  downloadURL?: string;
  originFileObj?: {
    key?: string;
  };
}
interface UploadParams {
  ossAccessKeyId: string;
  signature: string;
  dir: string;
  key: string;
  policy: string;
  host: string;
}
interface UploadResult {
  previewUrl: string;
  downloadUrl: string;
  objectName: string;
  fileType: string;
}
export class PolyMindUploader {
  async getOssUploadParams(file: EnhancedFile) {
    const { name } = file;
    const content = await alovaInst.Get<UploadParams>('/attach/ossSign', {
      params: {
        fileName: name,
        // 禁用缓存
        ts: Date.now(),
      },
    });
    return content;
  }
  async uploadComplete(options: { objectName: string; fileName: string; fileSize: number; signature: string }) {
    return alovaInst.Post<UploadResult>('/attach/ossComplete', options);
  }
  /**
   * 上传文件到OSS
   */
  public async uploadFile(file: File) {
    const enhancedFile = file as EnhancedFile;
    // OSS 上传
    const uploadParams = await this.getOssUploadParams(enhancedFile);
    const formData = new FormData();
    formData.append('name', file.name);
    formData.append('policy', uploadParams.policy);
    formData.append('OSSAccessKeyId', uploadParams.ossAccessKeyId);
    formData.append('signature', uploadParams.signature);
    formData.append('key', `${uploadParams.dir}/${uploadParams.key}`);
    formData.append('file', file);
    const uploadInst = createAlova({
      requestAdapter: adaptorFetch(),
    });
    await uploadInst.Post(uploadParams.host, formData);
    const uploadComplete = await this.uploadComplete({
      objectName: uploadParams.key,
      fileName: file.name,
      fileSize: file.size,
      signature: uploadParams.signature,
    });
    return {
      success: true,
      file: {
        ...uploadComplete,
        name: file.name,
        size: file.size,
        url: uploadComplete.downloadUrl,
      },
    };
  }
}
