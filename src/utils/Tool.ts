import { BaseAgent } from '@/core/BaseAgent';
import { Tool, ToolSchema } from '@/types/types';
import { ToolExecutionError } from '@/utils/Errors';
import { Logger } from '@/utils/Logger';
import { UserFeedbackLogger } from '@/utils/UserFeedbackLogger';

export interface BaseToolConfig {
  logger?: Logger;
  feedbackLogger?: UserFeedbackLogger;
  [key: string]: any;
}

export abstract class BaseTool implements Tool {
  name = '工具基类';
  nameCn = '工具基类';
  description = '工具基类';
  parameters: ToolSchema;
  logger: Logger;
  feedbackLogger: UserFeedbackLogger;
  constructor(config?: BaseToolConfig) {
    this.logger = config?.logger || new Logger();
    this.feedbackLogger = config?.feedbackLogger || new UserFeedbackLogger();
  }
  abstract execute(args: any): Promise<any>;
  agent: BaseAgent;
}

interface FactoryAgentToolArgs {
  context: string;
}
/**
 * 一个用于将Agent转换为Tool的工厂类
 */
export class FactoryAgentTool extends BaseTool {
  constructor(agent: BaseAgent) {
    super(agent.config);
    this.agent = agent;
    this.name = agent.name;
    this.nameCn = agent.nameCn;
    this.description = agent.description;
    this.parameters = agent.config.parameters || {
      type: 'object',
      properties: {
        context: { type: 'string', description: '上下文信息' },
      },
      required: ['context'],
    };
  }
  async execute(args: FactoryAgentToolArgs): Promise<any> {
    try {
      return await this.agent.run(args.context);
    } catch (error) {
      this.logger.error(`执行失败: ${error}`);
      throw new ToolExecutionError(this.name, `执行失败: ${error}`);
    }
  }
  validateArgs(args: any): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];
    if (!args.context) {
      errors.push('上下文信息不能为空');
    }
    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }
}
