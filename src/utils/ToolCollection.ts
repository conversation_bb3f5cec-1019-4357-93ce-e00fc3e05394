import { ToolExecutionError } from './Errors';
import { Tool } from '@/types/types';

export class ToolCollection {
  private tools: Map<string, Tool>;
  private isInterrupted = false;

  constructor(...tools: Tool[]) {
    this.tools = new Map();
    tools.forEach((tool) => this.addTool(tool));
  }

  interrupt(): void {
    this.isInterrupted = true;
  }

  resetInterrupt(): void {
    this.isInterrupted = false;
  }

  /**
   * 执行指定名称的工具
   * @param name 工具名称
   * @param toolInput 工具输入参数
   * @returns 工具执行结果
   */
  async execute(name: string, toolInput: any): Promise<any> {
    if (this.isInterrupted) {
      throw new Error('工具执行被中断');
    }

    const tool = this.getTool(name);
    if (!tool) {
      throw new ToolExecutionError(name, `Tool '${name}' not found`);
    }

    try {
      // 如果工具有验证方法，先验证参数
      if (tool.validateArgs) {
        const validation = tool.validateArgs(toolInput);
        if (!validation.valid) {
          throw new Error(`参数验证失败: ${validation.errors?.join(', ')}`);
        }
      }
      return await tool.execute(toolInput);
    } catch (error) {
      if (this.isInterrupted) {
        throw new Error('工具执行被中断');
      }
      if (error instanceof Error) {
        throw new ToolExecutionError(name, error.message);
      }
      throw new ToolExecutionError(name, String(error));
    }
  }

  /**
   * 获取所有工具的参数定义
   * @returns 工具参数定义数组
   */
  toParams(): Array<{ type: string; function: { name: string; description: string; parameters?: any } }> {
    return Array.from(this.tools.values()).map((tool) => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  /**
   * 获取工具映射
   */
  get toolMap(): { [key: string]: Tool } {
    return Object.fromEntries(this.tools);
  }

  /**
   * 添加工具
   * @param tool 要添加的工具
   * @returns 工具集合实例，支持链式调用
   */
  addTool(tool: Tool): this {
    this.tools.set(tool.name.toLowerCase(), tool);
    return this;
  }

  /**
   * 添加多个工具
   * @param tools 要添加的工具数组
   * @returns 工具集合实例，支持链式调用
   */
  addTools(...tools: Tool[]): this {
    tools.forEach((tool) => this.addTool(tool));
    return this;
  }

  /**
   * 获取指定名称的工具
   * @param name 工具名称
   * @returns 工具实例或undefined
   */
  getTool(name: string): Tool | undefined {
    return this.tools.get(name.toLowerCase());
  }

  /**
   * 获取所有工具
   * @returns 工具数组
   */
  getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * 检查是否包含指定名称的工具
   * @param name 工具名称
   * @returns 是否包含
   */
  hasTool(name: string): boolean {
    return this.tools.has(name.toLowerCase());
  }

  /**
   * 获取工具数量
   */
  get size(): number {
    return this.tools.size;
  }
}
