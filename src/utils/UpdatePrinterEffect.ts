import { PlanStatusData } from '@/types/types';

export interface FeedbackLogItem {
  type: FeedbackType;
  message: string;
  timestamp: Date;
  data?: PlanStatusData | any;
  stepNumber?: number;
}

export enum FeedbackType {
  RESPONSE = 'response', // 回应用户提问
  FILE = 'file', // 文件日志
  PLAN = 'plan', // 计划结果
  PLAN_END = 'plan_end', // 计划结束

  STEP_START = 'step_start', // 步骤开始执行
  STEP_END = 'step_end', // 步骤结束

  REASONING = 'reasoning', // 代理思考过程
  THINKING = 'thinking', // 代理思考结果
  TOOL_CALL = 'tool_call', // 工具调用
  TOOL_RESULT = 'tool_result', // 工具执行结果
  CODING = 'coding', // 代码生成中

  COMPLETE = 'complete', // 执行完成
}

interface logObjProps {
  stepNumber?: number;
  logs: FeedbackLogItem[];
}

interface UpdatePrinterEffectConfig {
  logObj: logObjProps;
  maxCharsPerBatch?: number;
  outputFun: (text: string | FeedbackLogItem[]) => void;
  mode?: 'text' | 'log';
}

export class UpdatePrinterEffect {
  protected logObj: logObjProps;
  protected outputFun: (text: string | FeedbackLogItem[]) => void;
  protected completeLogs: FeedbackLogItem[] = []; // 已完成日志
  protected completeText = ''; // 已完成文本
  protected currentTypingText = ''; // 当前正在输出的message
  protected typingLogIndex = 0; // 当前正在输入的日志序号
  protected maxCharsPerBatch = 10;
  protected isTyping = false;
  protected mode: 'text' | 'log';
  constructor(config: UpdatePrinterEffectConfig) {
    this.logObj = config.logObj;
    this.maxCharsPerBatch = config?.maxCharsPerBatch || this.maxCharsPerBatch;
    this.outputFun = config?.outputFun || this.outputFun;
    this.mode = config.mode || 'text';
    // 监听标签页是否隐藏
    if (document.hidden !== undefined) {
      document.addEventListener('visibilitychange', this.toEnd.bind(this));
    }
  }

  async renderChunk(force = false) {
    if (!force && this.isTyping) return;
    this.isTyping = true;
    const clogs = this.getLogs();
    const typingLog = clogs[this.typingLogIndex];
    const typingMessage = typingLog?.message;
    if (this.typingLogIndex > clogs.length - 1) {
      this.isTyping = false;
      return;
    }
    if (!typingMessage || typingMessage === this.currentTypingText) {
      this.typingLogIndex++;
      this.completeLogs = clogs.slice(0, this.typingLogIndex);
      this.completeText = this.formatLogsForDisplay(this.completeLogs);
      this.currentTypingText = '';
      if (this.mode === 'log') {
        this.outputFun([...this.completeLogs]);
      }
      await this.renderChunk(true);
      return;
    }
    // 继续渲染下一块
    const curComplateIndex = this.currentTypingText.length;
    // 判断接下来的字符，是否是图片
    const nextChar = typingMessage.substring(curComplateIndex);
    let { maxCharsPerBatch } = this;
    let nextCharIsImage = false;
    if (/^!\[图片\]\((.*?)\)/.test(nextChar)) {
      nextCharIsImage = true;
      maxCharsPerBatch = nextChar.length - nextChar.replace(/^!\[图片\]\((.*?)\)/, '').length;
    }
    const nextIndex = Math.min(curComplateIndex + maxCharsPerBatch, typingMessage.length);
    this.currentTypingText = typingMessage.substring(0, nextIndex);
    const currentTypingLog = { ...typingLog, message: this.currentTypingText };
    const currentTypingText = this.formatLogsForDisplay([currentTypingLog]);
    const outputLogs = [...this.completeLogs, currentTypingLog];
    requestAnimationFrame(() => {
      if (this.mode === 'log') {
        this.outputFun(outputLogs);
      } else {
        const outputText = this.completeText + currentTypingText;
        const outputTextWithoutTag = outputText.replace(/<(\/)?[^<>]*$/, '');
        this.outputFun(outputTextWithoutTag);
      }
    });
    // 计算下一帧的延迟
    const chunkLen = nextCharIsImage ? 1 : typingMessage.substring(curComplateIndex, nextIndex).length;
    // 计算动态延时
    const minDelay = 1; // 最小延时（毫秒）
    const midDelay = 10; // 中间延时（毫秒）
    const normalDelay = 20; // 一般延时（毫秒）
    const baseDelay = 30;
    const maxDelay = 50; // 最大延时（毫秒）
    const nextTypingTextLength = clogs.slice(this.typingLogIndex).reduce((acc, cur) => {
      return acc + cur.message.length;
    }, 0);
    const nextCurTextLength = typingMessage.length - this.currentTypingText.length;
    const waitTextLength = clogs.length - 1 > this.typingLogIndex ? nextTypingTextLength : nextCurTextLength;
    let dynamicDelay = baseDelay;
    if (waitTextLength >= 200) {
      dynamicDelay = minDelay;
    } else if (waitTextLength < 200 && waitTextLength > 100) {
      dynamicDelay = midDelay;
    } else if (waitTextLength <= 100 && waitTextLength > 50) {
      dynamicDelay = normalDelay;
    } else if (waitTextLength <= 50 && waitTextLength > 20) {
      dynamicDelay = baseDelay;
    } else {
      dynamicDelay = maxDelay;
    }
    // 应用动态延时
    const delay = Math.min(dynamicDelay * chunkLen, 1000); // 最大延时1秒
    await new Promise((resolve) => setTimeout(resolve, delay));
    await this.renderChunk(true);
  }

  formatLogsForDisplay(logs?: FeedbackLogItem[]): string {
    const clogs = this.getLogs();
    const tarLogs = logs || clogs || [];
    if (tarLogs.length === 0) return '';
    let output = '';
    for (const log of tarLogs) {
      output += log.message || '';
    }
    return output;
  }

  getLogs() {
    const { stepNumber, logs } = this.logObj;
    if (stepNumber === undefined) return [...logs];
    return logs.filter((log: FeedbackLogItem) => log.stepNumber === stepNumber);
  }

  reset(): void {
    this.completeLogs = [];
    this.completeText = '';
    this.currentTypingText = '';
    this.typingLogIndex = 0;
  }

  toEnd(): void {
    if (!this.isTyping) return;
    const clogs = this.getLogs();
    if (document.hidden || !clogs.length) return;
    this.typingLogIndex = clogs.length - 1;
    this.currentTypingText = clogs[this.typingLogIndex].message;
    this.completeLogs = clogs;
    this.completeText = this.formatLogsForDisplay(clogs);
    this.outputFun(this.completeText);
  }

  showAllLog() {
    if (this.mode === 'log') {
      this.outputFun(this.completeLogs);
    } else {
      this.outputFun(this.completeText);
    }
  }
}
