/* eslint-disable @iceworks/best-practices/recommend-polyfill */
import nattyFetch from 'natty-fetch';

import {
  getYidaConfig,
  getOssUploadOptions,
  getFileExtension,
  createFetch,
  getCsrfToken,
  getUrlParam,
} from '@ali/yc-utils';

function generateUUID() {
  let d = new Date().getTime();
  let d2 = (performance && performance.now && performance.now() * 1000) || 0;
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    let r = Math.random() * 16;
    if (d > 0) {
      // eslint-disable-next-line no-bitwise
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      // eslint-disable-next-line no-bitwise
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    // eslint-disable-next-line no-bitwise
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
}
const getOssUploadParams = async (file: File, params: any = {}) => {
  try {
    const appType = getYidaConfig('appType');
    const objectName = generateUUID().toUpperCase();
    const { name, type, size } = file;
    const ext = getFileExtension(name);

    const current = new Date();
    const year = current.getFullYear();
    const month = current.getMonth() + 1;
    const date = current.getDate();

    const localKey = `${appType}/${year}/${month}-${date}/${objectName}${ext ? `.${ext}` : ''}`;

    const fetch = createFetch({
      url: '/ossSign',
      data: {
        _csrf_token: getCsrfToken(),
        appType,
        fileName: name,
        fileSize: size,
        contentType: type,
        isOpen: 'n',
        newContext: 'y',
        objectName: localKey,
        procInstId: getUrlParam('procInsId') || '',
        businessType: getYidaConfig('formInstId') ? 'inst' : null,
        ...params,
      },
    });
    const content = await fetch();
    if (content) {
      content.key = content.objectName || localKey;
      return content;
    }
  } catch (err) {
    console.error(err);
    return null;
  }
};

const uploadCallback = async (file: File, params: Record<string, any> = {}) => {
  const res = await nattyFetch.create({
    method: 'POST',
    url: '/query/attach/uploadCallBack.json',
    data: {
      _csrf_token: getCsrfToken(),
      appType: getYidaConfig('appType') || 'default_tianshu_app',
      fileName: file.name,
      fileSize: file.size,
      objectName: (file as any)?.originFileObj?.key,
      formUuid: getYidaConfig('formUuid'),
      ...params,
    },
  })();
  return res;
};

export const UploadFile = async (file: File) => {
  // OSS 上传
  const res = await getOssUploadParams(file, { accelerate: 'y' });
  if (!res) return { success: false, error: '上传失败' };
  const uploadProps = getOssUploadOptions({}, res, file);
  const { action, ...restProps } = uploadProps;
  // 发送上传请求
  const formData = new FormData();
  Object.entries(restProps.data).forEach(([key, value]: any) => {
    formData.append(key, value);
  });
  formData.append('file', file);
  await fetch(action, {
    ...restProps,
    method: restProps.method || 'POST',
    headers: restProps.headers || {
      'Content-Type': 'multipart/form-data',
    },
    body: formData,
  });
  await uploadCallback(file);
  return { success: true, file };
};
