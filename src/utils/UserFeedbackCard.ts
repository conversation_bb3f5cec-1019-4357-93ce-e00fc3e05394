import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { FeedbackType, FeedbackLogItem, UpdatePrinterEffect } from './UpdatePrinterEffect';

export interface UserFeedbackCardConfig {
  updateMessageLogs?: (logs: FeedbackLogItem[]) => void;
  summaryCallback?: (isSummarizing: boolean) => void;
}
export interface FileData {
  id: string;
  type: 'file' | 'app' | 'page' | 'form' | 'other';
  link?: string;
  icon?: string;
  title?: string;
}

export class UserFeedbackCard {
  summaryCallback?: (isSummarizing: boolean) => void; // 添加总结状态回调
  protected logs: FeedbackLogItem[] = [];
  // 打印机效果相关属性
  protected keyResultData: any = []; // 关键产物
  protected cardPrinterEffect: UpdatePrinterEffect;

  // 单例模式
  constructor(config: UserFeedbackCardConfig = {}) {
    // 设置总结状态回调函数
    this.summaryCallback = config.summaryCallback || (() => {});
    // 输出到步骤日志
    this.cardPrinterEffect = new UpdatePrinterEffect({
      mode: 'log',
      logObj: {
        logs: this.logs,
      },
      maxCharsPerBatch: 1,
      outputFun: (logs: FeedbackLogItem[]) => {
        config.updateMessageLogs?.(logs);
      },
    });
  }
  // 回应用户提问
  response(message: string): void {
    this.log(FeedbackType.RESPONSE, message);
  }
  // 文件日志
  file(message: string, data: FileData, stepNumber: number): void {
    if (stepNumber !== undefined) {
      const tarPlanLog = this.logs.find((log) => log.type === FeedbackType.PLAN);
      const tarStep = tarPlanLog?.data?.steps?.find((_: any, index: number) => index === stepNumber);
      tarStep.files = tarStep.files || [];
      const tarFileIndex = tarStep.files.findIndex((file: FileData) => file.id === data.id);
      if (tarFileIndex !== -1) {
        tarStep.files[tarFileIndex] = {
          ...tarStep.files[tarFileIndex],
          ...data,
          title: message || tarStep.files[tarFileIndex].title,
        };
      } else {
        tarStep.files.push({
          ...data,
          title: message,
        });
      }
      this.cardPrinterEffect?.showAllLog();
    } else {
      const tarLog = this.logs.find((log) => log.type === FeedbackType.FILE && log.data.id === data.id);
      if (tarLog) {
        tarLog.message = message;
        tarLog.data = {
          ...tarLog.data,
          ...data,
        };
        tarLog.timestamp = new Date();
        this.cardPrinterEffect?.showAllLog();
      } else {
        this.log(FeedbackType.FILE, message, data);
      }
    }
  }
  // 计划日志
  plan(statusData: any): void {
    const tarPlanLog = this.logs.find((log) => log.type === FeedbackType.PLAN);
    if (tarPlanLog) {
      const tarSteps = statusData.steps.map((item: any, index: number) => {
        const oldStep = tarPlanLog.data.steps[index];
        return { ...oldStep, ...item };
      });
      tarPlanLog.data = { ...tarPlanLog.data, ...statusData, steps: tarSteps };
      tarPlanLog.timestamp = new Date();
      this.cardPrinterEffect?.showAllLog();
    } else {
      this.log(FeedbackType.PLAN, '', statusData);
    }
  }
  // 计划结束
  planEnd(message: string, executionData?: any): void {
    this.log(FeedbackType.PLAN_END, message, executionData);
  }
  // 步骤开始
  stepStart(stepNumber: number) {
    const statusLog = this.logs.find((log) => log.type === FeedbackType.PLAN);
    const tarStep = statusLog?.data?.steps?.find((_: any, index: number) => index === stepNumber);
    if (!tarStep) return;
    tarStep.status = 'in_progress';
    tarStep.timestamp = new Date();
    this.cardPrinterEffect?.showAllLog();
  }
  // 关键结果
  keyResult(data: any): void {
    this.keyResultData.push(data);
    // 如果是应用类型，更新图标
    if (data.type === 'app' && data.iconUrl) {
      const tarPlanLog = this.logs.find((log) => log.type === FeedbackType.PLAN);
      if (tarPlanLog) {
        tarPlanLog.data = {
          ...tarPlanLog.data,
          iconUrl: data.iconUrl,
        };
        tarPlanLog.timestamp = new Date();
        this.cardPrinterEffect?.showAllLog();
      }
    }
  }
  showKeyResult() {
    // 更新应用信息
    const lastAppResult = this.keyResultData.reverse().find((r: any) => r.type === 'app');
    if (lastAppResult) {
      const { icon, iconUrl, url, desc, title } = lastAppResult;
      const tarPlanLog = this.logs.find((log) => log.type === FeedbackType.PLAN);
      if (tarPlanLog) {
        tarPlanLog.data = {
          ...tarPlanLog.data,
          icon,
          iconUrl,
          url,
          desc,
          title,
        };
        tarPlanLog.timestamp = new Date();
        this.cardPrinterEffect?.showAllLog();
      }
    }
    // 预览最后一个页面
    const lastPageResult = this.keyResultData.reverse().find((r: any) => r.type === 'page');
    if (lastPageResult?.url) {
      const { setArtifactMode } = useAIArtifactStateStore.getState();
      setArtifactMode('preview');
      window.postMessage(
        {
          type: 'previewReady',
          url: lastPageResult.url,
        },
        '*',
      );
    }
  }
  log(type: FeedbackType, message: string, data?: any): void {
    const timestamp = new Date();
    const hasLogs = this.logs.length > 0;
    const lastLog = hasLogs ? this.logs[this.logs.length - 1] : null;
    if (lastLog && lastLog.type === type) {
      lastLog.message = message;
      lastLog.timestamp = timestamp;
      lastLog.data = data;
    } else {
      const logItem: FeedbackLogItem = {
        type,
        message,
        data,
        timestamp,
      };
      this.logs.push(logItem);
    }
    this.updatePrinterEffectHandler();
  }
  getLogs(): FeedbackLogItem[] {
    return [...this.logs];
  }
  async updatePrinterEffectHandler(): Promise<void> {
    await this.cardPrinterEffect.renderChunk();
    this.cardPrinterEffect.showAllLog();
  }
}
