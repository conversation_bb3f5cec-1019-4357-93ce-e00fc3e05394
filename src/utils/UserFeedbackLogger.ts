/* eslint-disable @typescript-eslint/member-ordering */
/* eslint-disable no-console */
/* eslint-disable no-param-reassign */
/* eslint-disable @iceworks/best-practices/recommend-polyfill */
import { useAIArtifactStateStore } from '@/store/aiArtifactStore';
import { FeedbackLogItem, FeedbackType, UpdatePrinterEffect } from './UpdatePrinterEffect';
import { setCodeFn } from '@/components/Code/code-editor';
import { FileData, UserFeedbackCard } from './UserFeedbackCard';
import { tryJSONParse } from '@ali/yc-utils';
import { PlanStatusData } from '@/types/types';

export interface UserFeedbackLoggerConfig {
  maxCharsPerBatch?: number;
  feedbackCard?: UserFeedbackCard;
}

export class UserFeedbackLogger {
  protected stepLogs: FeedbackLogItem[] = [];
  protected codeLogs: FeedbackLogItem[] = [];
  protected stepLogObj: any;
  protected codeLogObj: any;
  // 打印机效果相关属性
  protected isTyping = false;
  protected stopped = false;
  protected stepPrinterEffect: UpdatePrinterEffect;
  protected codePrinterEffect: UpdatePrinterEffect;
  protected feedbackCard?: UserFeedbackCard;
  protected mainPlanAgentId: string;
  protected isMainPlanAgent = false;
  protected mainPlanAgentStepNumber: number;
  // 单例模式
  constructor(config: UserFeedbackLoggerConfig = {}) {
    // 设置打印机效果
    const maxCharsPerBatch = config.maxCharsPerBatch || 1;
    // 设置反馈卡片
    this.feedbackCard = config.feedbackCard || null;
    // 获取日志
    const { setLogger } = useAIArtifactStateStore.getState();
    // 初始化
    setLogger('');
    setCodeFn('');
    const { setArtifactMode } = useAIArtifactStateStore.getState();
    setArtifactMode('logger');
    // 输出到步骤日志
    this.stepLogObj = {
      logs: this.stepLogs,
    };
    this.stepPrinterEffect = new UpdatePrinterEffect({
      mode: 'text',
      logObj: this.stepLogObj,
      maxCharsPerBatch,
      outputFun: (text: string) => {
        if (this.stopped) return;
        setLogger(text);
      },
    });
    // 输出到步骤代码
    this.codeLogObj = {
      logs: this.codeLogs,
    };
    this.codePrinterEffect = new UpdatePrinterEffect({
      mode: 'text',
      logObj: this.codeLogObj,
      maxCharsPerBatch: 30,
      outputFun: (text: string) => {
        if (this.stopped) return;
        setCodeFn(text);
      },
    });
  }
  stop(): void {
    this.stopped = true;
    this.stepPrinterEffect?.showAllLog();
    this.stepPrinterEffect?.reset();
    this.codePrinterEffect?.showAllLog();
    this.codePrinterEffect?.reset();
  }
  getStepLogs(): FeedbackLogItem[] {
    return [...this.stepLogs];
  }
  notifySummaryStatus(isSummarizing: boolean): void {
    this.feedbackCard?.summaryCallback?.(isSummarizing);
  }
  getMainPlanAgentId(): string {
    return this.mainPlanAgentId;
  }
  setMainPlanAgentId(id: string): void {
    if (this.mainPlanAgentId) return;
    this.mainPlanAgentId = id;
  }
  setIsMainPlanAgent(result: boolean): void {
    this.isMainPlanAgent = result;
  }
  setMainPlanAgentStepNumber(stepNumber: number): void {
    this.mainPlanAgentStepNumber = stepNumber;
  }
  isInPlanStep(): boolean {
    return this.mainPlanAgentStepNumber !== undefined;
  }
  // 回应用户提问
  response(message: string): void {
    if (this.stopped) return;
    if (this.isMainPlanAgent) {
      this.feedbackCard?.response(message);
      return;
    }
    const tarMessage = `${this.getLogTypeIcon(FeedbackType.RESPONSE)} ${message}\n`;
    this.stepLog(FeedbackType.RESPONSE, tarMessage, this.mainPlanAgentStepNumber);
  }
  // 文件日志, 仅for主Agent
  file(message: string, data: FileData): void {
    if (this.stopped) return;
    if (this.isMainPlanAgent) {
      this.feedbackCard?.file(message, data, this.mainPlanAgentStepNumber);
    }
  }
  // 计划状态日志
  plan(statusData: PlanStatusData): void {
    if (this.stopped) return;
    if (this.isMainPlanAgent) {
      this.feedbackCard?.plan(statusData);
      return;
    }
    const tarMessage = this.formatPlanData(statusData);
    const statusLog = this.stepLogs.reverse().find((l) => l.type === FeedbackType.PLAN);
    if (statusLog) {
      statusLog.message = tarMessage;
      this.stepPrinterEffect?.showAllLog();
    } else {
      this.stepLog(FeedbackType.PLAN, tarMessage, this.mainPlanAgentStepNumber);
    }
  }
  // 计划结束, 仅for主Agent
  planEnd(message: string, executionData?: any): void {
    if (this.stopped) return;
    if (this.isMainPlanAgent) {
      this.feedbackCard?.planEnd(message, executionData);
    }
  }
  private formatPlanData(statusData: any): string {
    if (!statusData) return '';
    let output = `\n${this.getLogTypeIcon(FeedbackType.PLAN)} 计划标题: ${statusData.title}\n`;
    // 显示计划步骤状态
    if (statusData.steps && Array.isArray(statusData.steps)) {
      output += '📋 当前计划状态:\n';
      output += statusData.steps
        .map((step: any, index: number) => {
          const statusIcon = this.getStatusIcon(step.status);
          let statusText = '';
          switch (step.status) {
            case 'completed':
              statusText = '✓ 已完成';
              break;
            case 'in_progress':
              statusText = '⌛ 执行中';
              break;
            case 'blocked':
              statusText = '⚠️ 已阻塞';
              break;
            case 'not_started':
              statusText = '⏱️ 等待中';
              break;
            default:
              statusText = '等待中';
          }
          // 为执行中的步骤添加特殊格式
          return `  ${statusIcon} 步骤 ${index + 1}: ${step.description} [${statusText}]`;
        })
        .join('\n');
      output += '\n\n';
    }
    // 计算并显示总体计划进度
    if (statusData.completed !== undefined && statusData.total !== undefined) {
      // 确保完成步骤数不超过总步骤数
      const completedSteps = Math.min(statusData.completed, statusData.total);
      const percentage = Math.min(Math.round((completedSteps / statusData.total) * 100), 100);
      let progressBar = '';
      const progressWidth = 20;
      const filledBlocks = Math.floor(percentage / (100 / progressWidth));
      // 使用绿色字符表示已完成部分，灰色字符表示未完成部分
      for (let i = 0; i < progressWidth; i++) {
        progressBar += i < filledBlocks ? '🟩' : '⬜'; // 使用彩色方块emoji替代ANSI颜色代码
      }
      output += `\n📊 执行进度: ${progressBar} ${completedSteps}/${statusData.total} (${percentage}%)\n\n`;
    }
    return output;
  }
  // 步骤开始, async for Manus
  async stepStart(stepTitle: string, stepNumber: number) {
    if (this.stopped) return;
    if (this.isMainPlanAgent) {
      this.setMainPlanAgentStepNumber(stepNumber);
      this.feedbackCard?.stepStart(stepNumber);
    } else {
      // 如果是步骤日志，更新步骤状态
      const statusLog = this.stepLogs.reverse().find((l) => l.type === FeedbackType.PLAN);
      const tarStep = statusLog?.data?.find((_: any, index: number) => index === stepNumber);
      if (!tarStep) return;
      tarStep.status = 'in_progress';
      this.stepPrinterEffect?.showAllLog();
    }
    // 切换到日志模式
    const { setArtifactMode } = useAIArtifactStateStore.getState();
    setArtifactMode('logger');
    // 添加日志中的步骤标题
    const tarMessage = this.formatStepStart(stepTitle, stepNumber);
    this.stepLog(FeedbackType.STEP_START, tarMessage, this.mainPlanAgentStepNumber);
  }
  private formatStepStart(stepTitle: string, stepNumber: number): string {
    let stepProgress = '';
    // 尝试从计划步骤中获取更准确的描述和位置
    const statusLogs = this.stepLogs.filter((l) => l.type === FeedbackType.PLAN && l.stepNumber === stepNumber);
    if (statusLogs.length > 0) {
      const latestStatus = statusLogs[statusLogs.length - 1].data;
      if (latestStatus && latestStatus.steps && latestStatus.total) {
        const currentPlanStepIndex = stepNumber !== undefined ? stepNumber : -1;
        if (currentPlanStepIndex !== -1 && currentPlanStepIndex < latestStatus.steps.length) {
          const step = latestStatus.steps[currentPlanStepIndex];
          if (step) {
            stepTitle = typeof step === 'string' ? step : step.description || stepTitle;
          }
          const totalSteps = latestStatus.total;
          const currentPlanStep = currentPlanStepIndex + 1;
          const stepPercentage = Math.min(Math.round((currentPlanStep / totalSteps) * 100), 100);
          if (currentPlanStep <= totalSteps) {
            stepProgress = ` (计划进度: ${stepPercentage}%)`;
          } else {
            console.warn(`警告：步骤序号 ${currentPlanStep} 超出计划总步骤数 ${totalSteps}`);
          }
        }
      }
    }
    const tarStepNumber = stepNumber !== undefined ? stepNumber + 1 : '';
    if (this.isMainPlanAgent) {
      return `<h2>🚩 开始执行步骤 ${tarStepNumber}: ${stepTitle}${stepProgress}</h2>\n`;
    }
    return `<h3>开始执行子步骤 ${tarStepNumber}: ${stepTitle}${stepProgress}</h3>\n`;
  }
  // 步骤结束, async for Manus
  async stepEnd() {
    if (this.stopped) return;
    console.log('stepEnd');
  }
  reasoning(message: string): void {
    if (this.stopped) return;
    if (!message) return;
    // 如果在主plan步骤中，没有步骤，则输出到默认步骤 -1 中
    let tarMessage = '';
    tarMessage = `\n┣━ <b>${this.getLogTypeIcon(
      FeedbackType.REASONING,
    )} 推理过程:</b>\n<div class="thinking-content"><pre>${message || ''}</pre></div>\n`;
    const tarStepNumber = this.isInPlanStep() ? this.mainPlanAgentStepNumber : -1;
    this.stepLog(FeedbackType.REASONING, tarMessage, tarStepNumber);
  }

  // 思考
  thinking(message: string): void {
    if (this.stopped) return;
    if (!message) return;
    // 如果在主plan步骤中，则直接输出到反馈卡片
    if (!this.isMainPlanAgent) {
      const tarMessage = typeof message === 'string' ? message : JSON.stringify(message, null, 2);
      this.feedbackCard?.response(tarMessage);
      return;
    }
    // 如果在主plan步骤中，没有步骤，则输出到默认步骤 -1 中
    let tarMessage = '';
    tarMessage = `\n┣━ <b>${this.getLogTypeIcon(
      FeedbackType.THINKING,
    )} 思考过程:</b>\n<div class="thinking-content"><pre>${message || ''}</pre></div>\n`;
    const tarStepNumber = this.isInPlanStep() ? this.mainPlanAgentStepNumber : -1;
    this.stepLog(FeedbackType.THINKING, tarMessage, tarStepNumber);
  }
  // 编码
  coding(message: string): void {
    if (this.stopped) return;
    if (!message) return;
    this.codeLog(FeedbackType.CODING, message, this.mainPlanAgentStepNumber);
  }
  // 工具调用
  toolCall(toolName: string, args: any): void {
    if (this.stopped) return;
    if (toolName === 'step_complete') return;
    const tarMessage = this.formatToolCall(toolName, { tool: toolName, args });
    const tarStepNumber = this.isInPlanStep() ? this.mainPlanAgentStepNumber : -1;
    this.stepLog(FeedbackType.TOOL_CALL, tarMessage, tarStepNumber);
  }
  private formatToolCall(message: string, data: any): string {
    const toolName = data?.tool || message.replace('调用工具: ', '');
    let content = `┣━ <b>${this.getLogTypeIcon(FeedbackType.TOOL_CALL)} 调用工具 ${toolName}:</b>\n`;
    if (data?.args && toolName !== 'message_ask_user') {
      content += this.formatToolArgs(data.args);
    }
    return content;
  }
  private formatToolArgs(args: any): string {
    let content = '';
    let formattedArgs = args;
    try {
      formattedArgs = JSON.parse(formattedArgs);
    } catch (e) {
      // 如果解析失败，保持原始格式
    }
    if (typeof formattedArgs === 'object') {
      for (const key in formattedArgs) {
        if (Object.prototype.hasOwnProperty.call(formattedArgs, key)) {
          const value = formattedArgs[key];
          if (value === undefined || value === null || value === '') continue;
          const formattedValue = this.formatValue(value);
          content += `┃    » ${key}: ${formattedValue}\n`;
        }
      }
    } else {
      formattedArgs = String(formattedArgs);
      content += `┃  ${formattedArgs}\n`;
    }
    return content;
  }
  private formatValue(value: any): string {
    if (typeof value === 'string') {
      return value.replace(/\n/g, '<br />').replace(/\r/g, '').replace(/\t/g, '  ');
    } else if (value === null) {
      return 'null';
    } else if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    } else {
      return String(value);
    }
  }
  // 工具执行结果
  toolResult(result: any): void {
    if (this.stopped) return;
    if (!result) return;
    // 如果在主plan步骤中，则直接输出到反馈卡片
    if (!this.isMainPlanAgent) {
      const message = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
      this.feedbackCard?.response(message);
      return;
    }
    // 如果在主plan步骤中，没有步骤，则输出到默认步骤 -1 中
    const tarMessage = this.formatToolResult(result);
    const tarStepNumber = this.isInPlanStep() ? this.mainPlanAgentStepNumber : -1;
    this.stepLog(FeedbackType.TOOL_RESULT, tarMessage, tarStepNumber);
  }
  private formatToolResult(result: any): string {
    let content = `\n┣━ <b>${this.getLogTypeIcon(
      FeedbackType.TOOL_RESULT,
    )} 执行结果:</b>\n<div class="thinking-content"><pre>`;
    if (result) {
      const tarResult = tryJSONParse(result, result);
      if (typeof tarResult === 'object' && tarResult !== null) {
        for (const key in tarResult) {
          if (Object.prototype.hasOwnProperty.call(tarResult, key)) {
            const value = tarResult[key];
            const valueStr = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value);
            content += `» ${key}: ${valueStr}\n`;
          }
        }
      } else {
        content += String(tarResult);
      }
    } else {
      content += '完成';
    }
    content += '</pre></div>';
    return content;
  }
  // 任务完成
  complete(message: string): void {
    if (this.stopped) return;
    const tarMessage = `\n\n${this.getLogTypeIcon(FeedbackType.COMPLETE)} ${message}\n`;
    this.stepLog(FeedbackType.COMPLETE, tarMessage, this.mainPlanAgentStepNumber || -1);
    // 显示关键结果
    this.feedbackCard?.showKeyResult();
  }
  // 关键结果, for主Agent
  keyResult(data: any): void {
    if (this.stopped) return;
    this.feedbackCard?.keyResult(data);
  }
  private stepLog(type: FeedbackType, message: string, stepNumber: number): void {
    if (this.stopped) return;
    const timestamp = new Date();
    const hasLogs = this.stepLogs.length > 0;
    const lastLog = hasLogs ? this.stepLogs[this.stepLogs.length - 1] : null;
    if (lastLog && lastLog.type === type && lastLog.stepNumber === stepNumber) {
      lastLog.message = message;
      lastLog.timestamp = timestamp;
    } else {
      const logItem: FeedbackLogItem = {
        type,
        message,
        timestamp,
        stepNumber,
      };
      this.stepLogs.push(logItem);
    }
    this.updatePrinterEffectHandler('stepPrinterEffect');
  }

  private codeLog(type: FeedbackType, message: string, stepNumber: number): void {
    if (this.stopped) return;
    const timestamp = new Date();
    const hasLogs = this.codeLogs.length > 0;
    const lastLog = hasLogs ? this.codeLogs[this.codeLogs.length - 1] : null;
    if (lastLog && lastLog.type === type) {
      lastLog.message = message;
      lastLog.timestamp = timestamp;
    } else {
      const logItem: FeedbackLogItem = {
        type,
        message,
        timestamp,
        stepNumber,
      };
      this.codeLogs.push(logItem);
    }
    this.updatePrinterEffectHandler('codePrinterEffect');
  }

  private async updatePrinterEffectHandler(type: 'stepPrinterEffect' | 'codePrinterEffect'): Promise<void> {
    if (this.isTyping || this.stopped) return;
    this.isTyping = true;
    await this[type]?.renderChunk();
    this.isTyping = false;
    this[type]?.showAllLog();
  }

  private getStatusIcon(status?: string): string {
    if (!status) return '⬜';
    switch (status) {
      case 'completed':
        return '✅';
      case 'in_progress':
        return '⏳';
      case 'blocked':
        return '⚠️';
      case 'not_started':
      default:
        return '⏱️';
    }
  }

  private getLogTypeIcon(type: FeedbackType): string {
    switch (type) {
      case FeedbackType.RESPONSE:
        return '🤖';
      case FeedbackType.PLAN:
        return '📋';
      case FeedbackType.STEP_START:
        return '🚩';
      case FeedbackType.TOOL_CALL:
        return '🛠️';
      case FeedbackType.TOOL_RESULT:
        return '📊';
      case FeedbackType.COMPLETE:
        return '🏁';
      case FeedbackType.REASONING:
        return '💭';
      case FeedbackType.THINKING:
        return '💭';
      case FeedbackType.CODING:
        return '📝';
      default:
        return '📝';
    }
  }
}
