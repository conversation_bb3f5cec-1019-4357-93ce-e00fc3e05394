import { ToolSchema } from '../types/types';
import { ValidationError } from './Errors';

/**
 * 工具参数验证器
 * 用于验证工具参数是否符合定义
 */
export class ToolValidator {
  /**
   * 验证参数是否符合工具定义
   * @param schema 工具参数定义
   * @param args 实际参数
   * @returns 验证结果
   */
  static validate(schema: ToolSchema, args: any): { valid: boolean; errors?: string[] } {
    if (!schema || !args) {
      return { valid: false, errors: ['无效的参数或定义'] };
    }

    const errors: string[] = [];

    // 验证必填参数
    if (schema.required && Array.isArray(schema.required)) {
      for (const requiredProp of schema.required) {
        if (args[requiredProp] === undefined) {
          errors.push(`缺少必填参数: ${requiredProp}`);
        }
      }
    }

    // 验证参数类型
    for (const [propName, propValue] of Object.entries(args)) {
      const propSchema = schema.properties[propName];
      if (!propSchema) {
        errors.push(`未知参数: ${propName}`);
        continue;
      }

      // 验证类型
      if (!this.validateType(propValue, propSchema.type)) {
        errors.push(`参数 ${propName} 类型错误，应为 ${propSchema.type}`);
      }

      // 验证枚举值
      if (propSchema.enum && Array.isArray(propSchema.enum) && !propSchema.enum.includes(String(propValue))) {
        errors.push(`参数 ${propName} 的值必须是以下之一: ${propSchema.enum.join(', ')}`);
      }

      // 验证数组项
      if (propSchema.type === 'array' && propSchema.items && Array.isArray(propValue)) {
        for (let i = 0; i < propValue.length; i++) {
          const item = propValue[i];
          if (!this.validateType(item, propSchema.items.type)) {
            errors.push(`参数 ${propName}[${i}] 类型错误，应为 ${propSchema.items.type}`);
          }

          if (
            propSchema.items.enum &&
            Array.isArray(propSchema.items.enum) &&
            !propSchema.items.enum.includes(String(item))
          ) {
            errors.push(`参数 ${propName}[${i}] 的值必须是以下之一: ${propSchema.items.enum.join(', ')}`);
          }
        }
      }

      // 验证对象属性
      if (propSchema.type === 'object' && propSchema.properties && typeof propValue === 'object') {
        const nestedResult = this.validateObject(propValue, propSchema.properties, propSchema.requiredProperties || []);
        if (!nestedResult.valid) {
          errors.push(...nestedResult.errors!.map((err) => `${propName}.${err}`));
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * 抛出验证错误
   * @param errors 错误信息
   */
  static throwValidationError(errors: string[]): never {
    throw new ValidationError('参数验证失败', errors);
  }

  /**
   * 验证对象属性
   * @param obj 对象
   * @param properties 属性定义
   * @param required 必填属性
   * @returns 验证结果
   */
  private static validateObject(
    obj: any,
    properties: Record<string, any>,
    required: string[],
  ): { valid: boolean; errors?: string[] } {
    const errors: string[] = [];

    // 验证必填属性
    for (const requiredProp of required) {
      if (obj[requiredProp] === undefined) {
        errors.push(`缺少必填属性: ${requiredProp}`);
      }
    }

    // 验证属性类型
    for (const [propName, propValue] of Object.entries(obj)) {
      const propSchema = properties[propName];
      if (!propSchema) {
        errors.push(`未知属性: ${propName}`);
        continue;
      }

      if (!this.validateType(propValue, propSchema.type)) {
        errors.push(`属性 ${propName} 类型错误，应为 ${propSchema.type}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * 验证值的类型
   * @param value 值
   * @param type 类型
   * @returns 是否符合类型
   */
  private static validateType(value: any, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'integer':
        return Number.isInteger(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'null':
        return value === null;
      default:
        return true; // 未知类型默认通过
    }
  }
}
