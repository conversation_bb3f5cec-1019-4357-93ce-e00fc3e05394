import * as XLSX from 'xlsx';

/**
 * 读取Excel文件
 * @param file Excel文件对象
 * @returns  Promise对象，resolve时返回一个对象，包含success, content三个key:
 *  - success: boolean - 是否成功解析
 *  - content: {
 *      sheetName: string - 工作表名称
 *      contentType: string - 内容类型，当前固定为csv
 *      content: string - csv内容
 *    }
 * reject时返回一个对象，包含success和error两个key
 *  - success: boolean - 是否成功解析
 *  - error: string - 错误信息
 */
export const readFile = async (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        // 获取第一个工作表】
        const sheetName = workbook.SheetNames[0];
        const firstSheet = workbook.Sheets[sheetName];
        // 转换为JSON
        const csv = XLSX.utils.sheet_to_csv(firstSheet);
        console.log('firstSheet csv: ', csv);
        // 返回前5行数据摘要
        const metaData = csv.split('\n').slice(0, 5).join('\n');
        // 返回结果
        resolve({
          success: true,
          file,
          sheetName,
          allData: csv,
          metaData,
        });
      } catch (error) {
        console.error('Error:', error);
        reject({
          success: false,
          error: error.message,
        });
      }
    };
    reader.onerror = function (error) {
      console.error('读取文件时出错:', error);
      reject({
        success: false,
        error,
      });
    };
    reader.readAsArrayBuffer(file);
  });
};
