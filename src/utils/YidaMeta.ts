import { cacheFetch, II18n, j<PERSON><PERSON><PERSON>, errorH<PERSON><PERSON>, i18nFormat } from '@ali/yc-utils';
import { uniqueId } from '@ali/ve-utils';
export interface IPageInfo {
  pageId: string;
  name: II18n;
  type: string;
  fieldList?: IFieldInfo[];
}

export interface IRelatePageInfo {
  appType: string;
  formTitle: string;
  formUuid: string;
  mainComponentName: string;
  mainFieldId: string;
  mainFieldLabel: II18n;
}

export interface IOption {
  text: string;
  value: string;
}

export interface IFieldInfo {
  fieldId: string;
  label: string;
  type: string;
  relatePageInfo?: IRelatePageInfo;
  subFields?: IFieldInfo[];
  options?: IOption[];
  bind?: string;
}

export interface IAppContext {
  pageList: Array<
    IPageInfo & {
      fieldList: IFieldInfo[];
    }
  >;
}

// 表单字段类型
export type FieldType =
  | 'TextField'
  | 'TextareaField'
  | 'RadioField'
  | 'SelectField'
  | 'CheckboxField'
  | 'MultiSelectField'
  | 'NumberField'
  | 'RateField'
  | 'DateField'
  // 暂不支持富文本组件 and 级联日期组件
  // | 'CascadeDateField'
  // | 'EditorField'
  | 'EmployeeField'
  | 'CountrySelectField'
  | 'DepartmentSelectField'
  | 'AddressField'
  | 'AttachmentField'
  | 'ImageField'
  | 'AssociationFormField'
  | 'TableField'
  | 'SerialNumberField';

export const FIELD_INFO = {
  TextField: {
    label: '单行文本',
    type: 'string',
    componentName: 'TextField',
    desc: '用于输入单行文本信息，如姓名、标题、编号等简短文本',
    valueExample: '测试文案',
  },
  TextareaField: {
    label: '多行文本',
    type: 'string',
    componentName: 'TextareaField',
    desc: '用于输入多行文本内容，如描述、备注、详细说明等长文本',
    valueExample: '长测试文案',
  },
  RadioField: {
    label: '单选',
    type: 'string',
    componentName: 'RadioField',
    desc: '用于从多个选项中选择一个选项，如性别、状态、类型等互斥选项',
    valueExample: '选项一',
  },
  SelectField: {
    label: '下拉单选',
    type: 'string',
    componentName: 'SelectField',
    desc: '用于从下拉列表中选择一个选项，适合选项较多时((>5))的单选场景',
    valueExample: '选项一',
  },
  CheckboxField: {
    label: '多选',
    type: 'string[]',
    componentName: 'CheckboxField',
    desc: '用于从多个选项中选择多个选项，如兴趣爱好、技能标签等可多选场景',
    valueExample: '["选项一", "选项二"]',
  },
  MultiSelectField: {
    label: '下拉多选',
    type: 'string[]',
    componentName: 'MultiSelectField',
    desc: '用于从下拉列表中选择多个选项，适合选项较多时(>5)的多选场景',
    valueExample: '["选项一", "选项二"]',
  },
  NumberField: {
    label: '数字',
    type: 'number',
    componentName: 'NumberField',
    desc: '用于输入数字类型数据，如金额、数量、年龄等需要计算的数值',
    valueExample: 100,
  },
  RateField: {
    label: '评分',
    type: 'number',
    componentName: 'RateField',
    desc: '用于对事物进行评分，如满意度评价、质量评分等星级打分场景',
    valueExample: 3,
  },
  DateField: {
    label: '日期',
    type: 'number',
    componentName: 'DateField',
    desc: '用于选择单个日期，如生日、会议日期、截止日期等时间点',
    valueExample: 1745241324196,
  },
  // 暂不支持级联日期组件
  // CascadeDateField: {
  //   label: '级联日期',
  //   type: '[number, number]',
  //   componentName: 'CascadeDateField',
  //   desc: '用于选择日期范围，如请假时间、出差时间、项目周期等时间段',
  //   valueExample: '[1745241324196, 1745241324196]',
  // },
  // 先不支持富文本组件，后续需要额外的适配
  // EditorField: {
  //   label: '富文本',
  //   type: 'longtext',
  //   componentName: 'EditorField',
  //   desc: '用于编辑富文本内容，支持文字格式化、图片插入等，如文章内容、通知公告等',
  // },
  EmployeeField: {
    label: '成员',
    type: 'string[]',
    componentName: 'EmployeeField',
    desc: '用于选择组织内的成员，包含姓名、工号、头像及部门信息，如负责人、经办人、审批人等人员选择，如果是外部人员场景请使用单行文本，如果是内部场景无需再添加部门及工号等相关字段',
    valueExample: '[43314767734684]',
  },
  CountrySelectField: {
    label: '国家',
    type: 'string',
    componentName: 'CountrySelectField',
    desc: '用于选择国家/地区，如国籍、所在地、业务区域等地理信息',
    valueExample: '中国',
  },
  DepartmentSelectField: {
    label: '部门',
    type: 'string[]',
    componentName: 'DepartmentSelectField',
    desc: '用于选择组织内的部门，如所属部门、对接部门、审批部门等组织架构',
    valueExample: '[981001106]',
  },
  AddressField: {
    label: '地址',
    type: 'string',
    componentName: 'AddressField',
    desc: '用于输入详细地址信息，如收货地址、办公地址、项目地址等位置信息',
  },
  ImageField: {
    label: '图片上传',
    type: `{
      downloadUrl: string;
      name: string;
    }[]`,
    componentName: 'ImageField',
    desc: '用于上传图片文件，如证件照片、产品图片、场景照片等图片资料',
    valueExample:
      '[{"downloadUrl":"https://img.alicdn.com/imgextra/i3/O1CN01ldUBhB28iU39JKz0J_!!6000000007966-2-tps-192-192.png","name":"image1.jpg"}]',
  },
  AttachmentField: {
    label: '附件上传',
    type: `{
      downloadUrl: string;
      name: string;
    }[]`,
    componentName: 'AttachmentField',
    desc: '用于上传附件文件，如合同、资料、报告等文件资料',
    valueExample:
      '[{"downloadUrl":"https://img.alicdn.com/imgextra/i3/O1CN01ldUBhB28iU39JKz0J_!!6000000007966-2-tps-192-192.png","name":"image1.jpg"}]',
  },
  SerialNumberField: {
    label: '流水号',
    type: 'string',
    componentName: 'SerialNumberField',
    desc: '用于自动生成编号，如合同编号、任务编号等，每个表单只能包含一个流水号字段，格式为YYYYMMDDNNNN',
    valueExample: '202504280001',
  },
  TableField: {
    label: '表格',
    type: 'any[]',
    componentName: 'TableField',
    desc: '用于录入表格数据，如费用明细、人员名单、产品清单等结构化数据',
    valueExample: `[{
      "textField_lzw4dkye": "文案一",
      "numberField_lzw4dkyf": 111,
      "radioField_lzw4dkyg": "选项一"
    }, {
      "textField_lzw4dkye": "文案二",
      "numberField_lzw4dkyf": 222,
      "radioField_lzw4dkyg": "选项二"
    }]`,
  },
  AssociationFormField: {
    label: '关联表单',
    type: `
    {
      "appType": string;
      "formUuid": string;
      "instanceId": string;
    }[]
    `,
    componentName: 'AssociationFormField',
    desc: '用于关联其他表单，如订单关联商品、员工关联部门等关联关系',
    valueExample: `[{
      "appType":"APP_L6010EZ6DSQLNKWHXK86",
      "formUuid":"FORM-EFFAA9300A864658AB27009B8EBE805FFXOB",
      "instanceId":"8631a1b3-c363-4ca8-a4be-1b5c121d5aba"
    }]`,
  },
};

export function generateFieldId(componentName: string) {
  return `${componentName.charAt(0).toLowerCase()}${componentName.slice(1)}${uniqueId(null, '', '')}`;
}

export async function queryPageList(
  appType: string,
  types: string[] = ['receipt', 'process', 'display', 'view'],
): Promise<IPageInfo[]> {
  try {
    const data = await cacheFetch(
      {
        url: `/${appType}/query/formnav/getFormNavigationListByOrderAndType.json`,
        method: 'GET',
      },
      true,
    );
    const res: IPageInfo[] = jsonFlat(data || [])
      .filter(({ formType }) => types && types.includes(formType))
      .map((item: any) => ({
        pageId: item.navUuid,
        name: i18nFormat(item.title),
        url: `/${appType}/workbench/${item.navUuid}`,
        type: item.formType,
      }));
    return res;
  } catch (err) {
    errorHandler(err);
    return [];
  }
}

export async function queryFieldList(appType: string, pageId: string): Promise<IFieldInfo[]> {
  try {
    const transformField = (item: any) => {
      const meta: IFieldInfo = {
        fieldId: item.componentId,
        label: i18nFormat(item.varName),
        type: item.componentName,
      };
      if (item?.props?.__bind__) {
        meta.bind = item.props.__bind__;
      }
      if (meta.type === 'AssociationFormField') {
        meta.relatePageInfo = item?.props?.associationForm;
      } else if (['CheckboxField', 'RadioField', 'SelectField', 'MultiSelectField'].includes(meta.type)) {
        meta.options = (item?.props?.dataSource || []).map((i: any) => ({
          text: i18nFormat(i.text),
          value: i.value,
        }));
      } else if (meta.type === 'TableField') {
        meta.subFields = (item?.children || [])
          .map((i: any) => transformField(i))
          .filter((i: IFieldInfo) => !i.fieldId.includes('subProcessInstanceId')); // 剔除系统字段
      }
      return meta;
    };

    const data = await cacheFetch(
      {
        url: `/${appType}/query/formProcInstData/getFormVariables.json`,
        method: 'GET',
        data: {
          formUuid: pageId,
          needInner: true,
        },
      },
      true,
    );
    const res: IFieldInfo[] = (data || []).map(transformField);
    // 忽略系统字段
    return res.filter((item) => item.fieldId.includes('_')); // 剔除系统字段
  } catch (err) {
    errorHandler(err);
    return [];
  }
}

export async function queryPageInfo(appId: string, pageId: string) {
  try {
    const pageList = await queryPageList(appId);
    const pageInfo = pageList.find((item) => item.pageId === pageId);
    if (!pageInfo) return null;
    if (['receipt', 'process'].includes(pageInfo.type)) {
      pageInfo.fieldList = await queryFieldList(appId, pageId);
    }
    return pageInfo;
  } catch (err) {
    errorHandler(err);
    return null;
  }
}

export async function queryAppContext(appId: string, types?: string[]): Promise<IAppContext> {
  try {
    const pageList = await queryPageList(appId, types);

    const fieldListPromises = pageList.map((page) => {
      if (['receipt', 'process'].includes(page.type)) {
        return queryFieldList(appId, page.pageId);
      }
      return undefined;
    });
    const fieldLists = await Promise.all(fieldListPromises);

    const pagesWithFields = pageList.map((page, index) => ({
      ...page,
      fieldList: fieldLists[index],
    }));

    return {
      pageList: pagesWithFields,
    };
  } catch (err) {
    errorHandler(err);
    return {
      pageList: [],
    };
  }
}
