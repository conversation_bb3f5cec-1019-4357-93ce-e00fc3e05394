import { Memory } from '../Memory';
// We're importing Message type from @/types/types
import { Message } from '@/types/types';

describe('Memory', () => {
  let memory: Memory;

  beforeEach(() => {
    memory = new Memory();
  });

  describe('constructor', () => {
    it('should initialize with default maxMessages', () => {
      expect(memory['maxMessages']).toBe(100);
    });

    it('should initialize with custom maxMessages', () => {
      const customMemory = new Memory(50);
      expect(customMemory['maxMessages']).toBe(50);
    });
  });

  describe('addMessage', () => {
    it('should add a message to the messages array', () => {
      const message: Message = {
        role: 'user',
        content: 'Test message',
      };
      memory.addMessage(message);
      expect(memory.getAllMessages()).toHaveLength(1);
      expect(memory.getAllMessages()[0]).toEqual(message);
    });

    it('should limit messages to maxMessages', () => {
      const memory = new Memory(2);
      const messages: Message[] = [
        { role: 'user', content: '1' },
        { role: 'assistant', content: '2' },
        { role: 'user', content: '3' },
      ];

      messages.forEach((msg) => memory.addMessage(msg));
      const allMessages = memory.getAllMessages();

      expect(allMessages).toHaveLength(2);
      expect(allMessages[0]).toEqual(messages[1]);
      expect(allMessages[1]).toEqual(messages[2]);
    });
  });

  describe('addMessages', () => {
    it('should add multiple messages at once', () => {
      const messages: Message[] = [
        { role: 'user', content: '1' },
        { role: 'assistant', content: '2' },
      ];
      memory.addMessages(messages);
      expect(memory.getAllMessages()).toHaveLength(2);
      expect(memory.getAllMessages()).toEqual(messages);
    });
  });

  describe('clear', () => {
    it('should remove all messages', () => {
      memory.addMessage({ role: 'user', content: 'Test' });
      memory.clear();
      expect(memory.getAllMessages()).toHaveLength(0);
    });
  });

  describe('clearPlanUserMessage', () => {
    it('should remove only user plan messages', () => {
      const messages: Message[] = [
        { role: 'user', content: '1', isPlanMsg: true },
        { role: 'assistant', content: '2', isPlanMsg: true },
        { role: 'user', content: '3', isPlanMsg: false },
      ];
      memory.addMessages(messages);
      memory.clearPlanUserMessage();
      const remaining = memory.getAllMessages();
      expect(remaining).toHaveLength(2);
      expect(remaining.some((msg) => msg.role === 'user' && msg.isPlanMsg)).toBeFalsy();
    });
  });

  describe('getRecentMessages', () => {
    it('should return the specified number of recent messages', () => {
      const messages: Message[] = [
        { role: 'user', content: '1' },
        { role: 'assistant', content: '2' },
        { role: 'user', content: '3' },
      ];
      memory.addMessages(messages);
      const recent = memory.getRecentMessages(2);
      expect(recent).toHaveLength(2);
      expect(recent).toEqual(messages.slice(-2));
    });
  });

  describe('removeLastMessage', () => {
    it('should remove the last message', () => {
      const messages: Message[] = [
        { role: 'user', content: '1' },
        { role: 'assistant', content: '2' },
      ];
      memory.addMessages(messages);
      memory.removeLastMessage();
      expect(memory.getAllMessages()).toHaveLength(1);
      expect(memory.getAllMessages()[0]).toEqual(messages[0]);
    });
  });

  describe('toDictList', () => {
    it('should convert messages to dictionary format', () => {
      const message: Message = {
        role: 'user',
        content: 'Test',
        name: 'TestUser',
        toolCallId: '123',
      };
      memory.addMessage(message);
      const dictList = memory.toDictList();
      expect(dictList[0]).toEqual({
        role: 'user',
        content: 'Test',
        name: 'TestUser',
        tool_call_id: '123',
      });
    });

    it('should handle messages with minimal properties', () => {
      const message: Message = {
        role: 'user',
        content: 'Test',
      };
      memory.addMessage(message);
      const dictList = memory.toDictList();
      expect(dictList[0]).toEqual({
        role: 'user',
        content: 'Test',
      });
    });
  });
});
