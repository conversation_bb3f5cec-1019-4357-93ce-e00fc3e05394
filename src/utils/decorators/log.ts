const globalColorMap = new Map<string, string>();

// const colors = ['\x1b[35m', '\x1b[36m', '\x1b[33m', '\x1b[32m'];
const colors = ['red', 'green', 'orange', 'blue', 'magenta', 'cyan'];
let colorIndex = 0;

const getColor = (key: string) => {
  if (globalColorMap.has(key)) {
    return globalColorMap.get(key);
  }
  const color = colors[colorIndex];
  globalColorMap.set(key, color);
  colorIndex = (colorIndex + 1) % colors.length;
  return colors[colorIndex];
};

// 用于打印类方法的输入参数和输出结果的装饰器，支持async函数
export function log() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = function (...args: any[]) {
      const className =
        (this && this.constructor && this.constructor.name) ||
        (target && target.constructor && target.constructor.name) ||
        'UnknownClass';
      // 根据 className 和 propertyKey 设置不同的颜色
      const color = getColor(`${className}`);
      const methodColor = getColor(propertyKey);

      const logResult = (r: any) => {
        let hasError = false;
        if (Array.isArray(r)) {
          hasError = r.reduce<boolean>((prev, cur) => {
            if (prev) {
              return prev;
            }
            if (typeof cur === 'string' && cur.includes('错误')) {
              return true;
            }
            return false;
          }, false);
        } else if (typeof r === 'string') {
          hasError = r.includes('错误');
        }
        if (hasError) {
          console.error(
            `📝 [LOG] [%c${className}%c] %c${propertyKey}%c ❌ 错误:`,
            `color: ${color}`,
            'color: black',
            `color: ${methodColor}`,
            'color: black',
            r,
          );
        } else {
          console.log(
            `📝 [LOG] [%c${className}%c] %c${propertyKey}%c ✅ 返回:`,
            `color: ${color}`,
            'color: black',
            `color: ${methodColor}`,
            'color: black',
            r,
          );
        }
      };

      console.log(
        `📝 [LOG] [%c${className}%c] 调用 %c${propertyKey}%c，参数:`,
        `color: ${color}`,
        'color: black',
        `color: ${methodColor}`,
        'color: black',
        ...args,
      );
      const result = originalMethod.apply(this, args);
      if (result instanceof Promise) {
        return result.then((res) => {
          logResult(res);
          return res;
        });
      } else {
        logResult(result);
        return result;
      }
    };
  };
}
