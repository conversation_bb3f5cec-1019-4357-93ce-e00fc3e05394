// test/setupTests.js
// Add any global test setup here if needed

// Mock browser globals that may be needed by dependencies
Object.defineProperty(window, '__defaultCodeGenAppType', {
  value: 'APP_WN5BIJCYKYB8KQKSXEA7',
  writable: true,
});

// Mock global config objects
Object.defineProperty(global, 'g_config', {
  value: {
    appKey: 'test-app',
    _csrf_token: 'test-token',
  },
  writable: true,
});

// Add properties to existing window object (with type safety workaround)
Object.assign(window, {
  g_config: {
    appKey: 'test-app',
    _csrf_token: 'test-token',
  },
  loginUser: {
    userName: 'testUser',
  },
});

// Skip location mocking in setup - handle per test as needed

// Mock URL constructor with proper type compatibility
const originalURL = global.URL;
global.URL = Object.assign(
  jest.fn().mockImplementation((url) => {
    const urlParts = new (require('url').URL)(url || 'http://localhost:3000');
    return {
      ...urlParts,
      searchParams: {
        get: (param) => {
          const params = new URLSearchParams(urlParts.search);
          return params.get(param);
        },
      },
    };
  }),
  originalURL, // Copy static methods like canParse, createObjectURL, etc.
);

// Mock TextEncoder/TextDecoder if they don't exist
if (typeof global.TextEncoder === 'undefined') {
  global.TextEncoder = class TextEncoder {
    encode(input) {
      return new Uint8Array([...Buffer.from(input, 'utf8')]);
    }
  };
}
if (typeof global.TextDecoder === 'undefined') {
  global.TextDecoder = class TextDecoder {
    decode(input) {
      return Buffer.from(input).toString('utf8');
    }
  };
}
