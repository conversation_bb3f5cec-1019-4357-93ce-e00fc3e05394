{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"outDir": "build", "module": "esnext", "target": "es6", "jsx": "react", "moduleResolution": "node", "lib": ["es6", "dom"], "sourceMap": true, "inlineSources": true, "allowJs": true, "noUnusedLocals": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "skipLibCheck": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "rootDir": "src", "esModuleInterop": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/node_modules/*", "build", "public"]}