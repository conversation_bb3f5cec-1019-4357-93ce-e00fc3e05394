{"bundle": {"bundler": "webpack", "entry": {"index": "src/pages/home/<USER>", "preview": "src/pages/preview/index.tsx", "mobile": "src/pages/mobile/index.ts"}, "moduleFederation": {"name": "yidaManus", "filename": "remoteEntry.js", "remotes": {"yida": "yida@https://g.alicdn.com/code/npm/@ali/yida/0.0.0/remoteEntry.js"}, "exposes": {"./mobile": "src/pages/mobile/index.ts"}}, "externals": {"react": "React", "react-dom": "ReactDOM", "natty-fetch": "natty<PERSON><PERSON>ch", "natty-storage": "nattyStorage", "@alifd/meet-react": "var window.Meet", "@alifd/next": "var window.Next", "@ali/deep": "var window.Deep", "lodash-es": "var window._", "lodash": "_"}, "devServer": {"bundler": "rspack", "port": 3500, "hot": false, "liveReload": false, "sourceMap": "inline-source-map", "headers": {"Access-Control-Allow-Origin": "*"}}, "alias": {"@": "src"}}}